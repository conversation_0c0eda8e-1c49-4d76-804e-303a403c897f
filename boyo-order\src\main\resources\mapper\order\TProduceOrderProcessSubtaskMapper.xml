<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.order.mapper.TProduceOrderProcessSubtaskMapper">

    <resultMap type="com.boyo.order.domain.TProduceOrderProcessSubtask" id="TProduceOrderProcessSubtaskResult">
        <result property="id" column="id"/>
        <result property="orderEquipmentId" column="order_equipment_id"/>
        <result property="produceOrderProcessId" column="produce_order_process_id"/>
        <result property="processUsers" column="process_users"/>
        <result property="processStatus" column="process_status"/>
        <result property="rollCode" column="roll_code"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="equipmentLedgerId" column="equipment_ledger_id"/>
        <result property="processName" column="process_name"/>

        <result property="reportCount" column="report_count"/>
        <result property="imperfectCount" column="imperfect_count"/>
        <result property="orderCode" column="order_code"/>
        <result property="materialName" column="material_name"/>
        <result property="orderCount" column="order_count"/>
        <result property="orderStart" column="order_start"/>
        <result property="orderEnd" column="order_end"/>
        <result property="workshopName" column="workshop_name"/>
        <result property="equipmentName" column="equipment_name"/>
        <result property="billCode" column="bill_code"/>
        <result property="materialId" column="material_id"/>
        <result property="materialCode" column="material_code"/>
        <result property="planStart" column="plan_start"/>
        <result property="planEnd" column="plan_end"/>
        <result property="subtaskCount" column="subtask_count"/>
        <result property="materialNorms" column="material_norms"/>
        <result property="spindleLength" column="spindle_length"/>
        <result property="equipmentCode" column="equipment_code"/>
    </resultMap>

    <select id="queryRollCodeByToday" resultType="string">
        select max(SUBSTRING_INDEX(roll_code,'-',-1))
        from t_produce_order_process_subtask
        where SUBSTRING_INDEX(roll_code,'-',2) = #{todayCode}
    </select>

    <select id="selectOwnList" resultMap="TProduceOrderProcessSubtaskResult">
        SELECT
        tt.*
        FROM
        (
        SELECT
        t1.*,
        t2.process_name,
        IFNULL(t3.report_count, 0 ) AS report_count,
        IFNULL(t3.imperfect_count, 0 ) AS imperfect_count,
        t3.order_process_subtask_id,
        t4.order_code,
        t4.order_start,
        t4.order_end,
        t4.order_count,
        t4.order_status,
        t4.plan_start,
        t4.plan_end,
        concat(t5.material_name, '(', t5.material_code, ')' ) AS material_name,
        t7.workshop_name,
        t5.material_norms,
        t9.equipment_name,
        t5.id AS material_id,
        t9.equipment_code
        FROM
        (
        SELECT
        *
        FROM
        t_produce_order_process_subtask ${ew.getCustomSqlSegment }) t1
        LEFT JOIN t_produce_order_process t8 ON t1.produce_order_process_id = t8.id
        LEFT JOIN t_base_process t2 ON t8.process_id = t2.id
        LEFT JOIN (
            SELECT
                IFNULL(SUM(report_count), 0) as report_count,
                IFNULL(SUM(imperfect_count), 0) as imperfect_count,
                order_process_subtask_id
            FROM t_produce_order_report
            WHERE report_status = '02'
            GROUP BY order_process_subtask_id
        ) t3 ON t1.id = t3.order_process_subtask_id
        LEFT JOIN t_produce_order t4 ON t8.order_id = t4.id
        LEFT JOIN t_base_material t5 ON t4.material_id = t5.id
        LEFT JOIN t_workshop_process t6 ON t2.id = t6.process_id
        LEFT JOIN t_workshop_user t7 ON t7.id = t6.workshop_id
        LEFT JOIN t_equipment_ledger t9 ON t1.equipment_ledger_id = t9.id
        ) tt
        <where>
            1 = 1
            <if test="null != bo.equipmentLedgerIdList">
                and
                <foreach collection="bo.equipmentLedgerIdList" item="equipmentLedgerId" open="(" close=")" separator="or" >
                tt.equipment_ledger_id = #{equipmentLedgerId}
                </foreach>
            </if>
            <if test="null != bo.orderCode and '' != bo.orderCode">
                and tt.order_code like CONCAT('%', #{bo.orderCode}, '%')
            </if>
            <if test="null != bo.processName and '' != bo.processName">
                and tt.process_name like CONCAT('%', #{bo.processName}, '%')
            </if>
        </where>
        order by tt.create_time desc,tt.process_status asc
    </select>


</mapper>

package com.boyo.process.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boyo.common.core.domain.PageQuery;
import com.boyo.process.domain.RawMiddle;
import com.boyo.process.service.RawMiddleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/rawmaterial")
public class RawMiddleController {

    @Autowired
    private RawMiddleService rawMiddleService;

    /**
     * 分页查询
     * @param rawMiddle
     * @param pageQuery
     * @return
     */
    @GetMapping
    public Page<RawMiddle> getPage(RawMiddle rawMiddle, PageQuery pageQuery) {
        return rawMiddleService.page(pageQuery.build(), new LambdaQueryWrapper<RawMiddle>()
            .like(StrUtil.isNotEmpty(rawMiddle.getPurchaseOrderNum()), RawMiddle::getPurchaseOrderNum, rawMiddle.getPurchaseOrderNum())
        );
    }
}

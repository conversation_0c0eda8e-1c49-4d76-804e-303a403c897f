package com.boyo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.domain.AoiEquipmentStatus;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * AOI设备状态Mapper接口
 * <AUTHOR>
 * @date 2024-01-15
 */
@Mapper
public interface AoiEquipmentStatusMapper extends BaseMapper<AoiEquipmentStatus> {

    /**
     * 根据核心表ID查询设备状态
     * @param reportCoreId 核心表ID
     * @return 设备状态数据
     */
    @Select("SELECT * FROM t_aoi_equipment_status WHERE report_core_id = #{reportCoreId} AND del_flag = 0")
    AoiEquipmentStatus selectByReportCoreId(@Param("reportCoreId") Long reportCoreId);

    /**
     * 根据核心表ID列表批量查询设备状态
     * @param reportCoreIds 核心表ID列表
     * @return 设备状态数据列表
     */
    List<AoiEquipmentStatus> selectByReportCoreIds(@Param("reportCoreIds") List<Long> reportCoreIds);

    /**
     * 查询设备产能统计
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 产能统计数据
     */
    List<Map<String, Object>> selectProductionStatistics(@Param("startDate") Date startDate, 
                                                         @Param("endDate") Date endDate);

    /**
     * 查询质量指标统计
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param machineSn 设备序列号(可选)
     * @return 质量指标数据
     */
    List<Map<String, Object>> selectQualityMetrics(@Param("startDate") Date startDate,
                                                   @Param("endDate") Date endDate,
                                                   @Param("machineSn") String machineSn);

    /**
     * 从原始数据生成设备状态
     * @param reportCoreId 核心表ID
     * @param reportDate 报表日期
     * @param machineSn 设备序列号
     * @return 影响行数
     */
    int generateFromRawData(@Param("reportCoreId") Long reportCoreId,
                           @Param("reportDate") Date reportDate,
                           @Param("machineSn") String machineSn);

    /**
     * 根据核心表ID删除设备状态
     * @param reportCoreId 核心表ID
     * @return 影响行数
     */
    @Delete("DELETE FROM t_aoi_equipment_status WHERE report_core_id = #{reportCoreId}")
    int deleteByCoreId(@Param("reportCoreId") Long reportCoreId);
}

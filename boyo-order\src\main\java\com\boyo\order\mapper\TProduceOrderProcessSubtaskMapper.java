package com.boyo.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boyo.order.domain.ProduceOrderProcess;
import com.boyo.order.domain.TProduceOrderProcessSubtask;
import com.boyo.order.domain.bo.ProduceOrderProcessBo;
import com.boyo.order.domain.bo.TProduceOrderProcessSubtaskBo;
import com.boyo.order.domain.vo.ProduceOrderProcessVo;
import com.boyo.order.domain.vo.TProduceOrderProcessSubtaskVo;
import com.boyo.common.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

/**
 * 工单工序子任务Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-07
 */
public interface TProduceOrderProcessSubtaskMapper extends BaseMapperPlus<TProduceOrderProcessSubtaskMapper, TProduceOrderProcessSubtask, TProduceOrderProcessSubtaskVo> {

    String queryRollCodeByToday(String todayCode);

    Page<TProduceOrderProcessSubtaskVo> selectOwnList(@Param("page") Page<TProduceOrderProcessSubtask> page, @Param(Constants.WRAPPER) LambdaQueryWrapper<TProduceOrderProcessSubtask> queryWrapper, @Param("bo") TProduceOrderProcessSubtaskBo bo);

}

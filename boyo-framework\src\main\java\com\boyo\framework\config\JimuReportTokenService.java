package com.boyo.framework.config;

import com.boyo.common.helper.LoginHelper;
import org.jeecg.modules.jmreport.api.JmReportTokenServiceI;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@Component
public class JimuReportTokenService implements JmReportTokenServiceI {

    @Override
    public String getToken(HttpServletRequest request) {
        String token = request.getHeader("token");
        return token;
    }

    @Override
    public String getToken() {
        return JmReportTokenServiceI.super.getToken();
    }

    @Override
    public String getUsername(String s) {
        return "admin";
    }

    @Override
    public String[] getRoles(String s) {
        return new String[0];
    }

    @Override
    public Boolean verifyToken(String s) {
        return true;
    }

}

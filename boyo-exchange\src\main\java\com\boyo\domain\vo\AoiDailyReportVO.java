package com.boyo.domain.vo;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * AOI生产日报表聚合VO
 * 用于前端展示完整的日报数据
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
public class AoiDailyReportVO implements Serializable {
    
    // ===== 核心表字段 =====
    private Long id;
    private Date reportDate;
    private String machineSn;
    private String projectName;
    private String productionTimeStatus;
    private Integer inputCount;
    private Integer productionCount;
    private Integer okCount;
    private Integer ngCount;
    private BigDecimal controlledYield;
    private String ngTop1;
    private String ngTop2;
    private String ngTop3;
    private BigDecimal machineDetectionRate;
    private String abnormalInfo;

    // ===== 缺陷统计字段 =====
    private Integer webBlemishCount;
    private Integer pillarCount;
    private Integer holeCount;
    private Integer doubleLineCount;
    private Integer knotCount;
    private Integer oxidationCount;
    private Integer oilStainCount;
    private Integer foreignObjectCount;
    private Integer deformationCount;
    private Integer crackCount;
    private Integer discolorationCount;
    private Integer hairinessCount;
    private Integer connectorlugCount;
    
    private BigDecimal webBlemishRate;
    private BigDecimal pillarRate;
    private BigDecimal holeRate;
    private BigDecimal doubleLineRate;
    private BigDecimal knotRate;
    private BigDecimal oxidationRate;
    private BigDecimal oilStainRate;
    private BigDecimal foreignObjectRate;
    private BigDecimal deformationRate;
    private BigDecimal crackRate;
    private BigDecimal discolorationRate;
    private BigDecimal hairinessRate;
    private BigDecimal connectorlugRate;

    // ===== 设备状态字段 =====
    private String shiftType;
    private Date startTime;
    private Date endTime;
    private Integer productionDuration;
    private String equipmentStatus;
    private String downtimeReason;
    private Integer downtimeDuration;
    private BigDecimal firstPassYield;
    private BigDecimal defectDensity;
    private BigDecimal oee;

    // ===== 操作员信息字段 =====
    private String operatorName;
    private String qualityInspector;
    private String shiftLeader;
    private String remark;
    private String dataSource;
    private String syncStatus;
    private Integer rawDataCount;

    // ===== 统计汇总字段 =====
    private Integer totalBatches;
    private BigDecimal avgInput;
    private BigDecimal avgOk;
    private BigDecimal avgNg;
    private BigDecimal avgYield;
    private Integer totalInput;
    private Integer totalOk;
    private Integer totalNg;

    // ===== 系统字段 =====
    private Date createTime;
    private Date updateTime;
    private String createBy;
    private String updateBy;

    private static final long serialVersionUID = 1L;
}

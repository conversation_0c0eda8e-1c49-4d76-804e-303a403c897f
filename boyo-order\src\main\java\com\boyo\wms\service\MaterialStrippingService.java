package com.boyo.wms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.boyo.common.core.domain.PageQuery;
import com.boyo.wms.domain.MaterialStripping;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【t_material_stripping(领料单主表)】的数据库操作Service
* @createDate 2025-06-11 20:31:29
*/
public interface MaterialStrippingService extends IService<MaterialStripping> {

    IPage<MaterialStripping> getPage(MaterialStripping materialStripping, PageQuery pageQuery);

    Boolean add(MaterialStripping materialStripping);

    Boolean audit(MaterialStripping materialStripping);
}

package com.boyo.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * AOI缺陷详细统计表
 * @TableName t_aoi_defect_detail
 * <AUTHOR>
 * @date 2024-01-15
 */
@TableName(value = "t_aoi_defect_detail")
@Data
public class AoiDefectDetail implements Serializable {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联核心日报表ID
     */
    @TableField(value = "report_core_id")
    private Long reportCoreId;

    // ===== 详细缺陷统计字段 =====
    
    /**
     * 网疤缺陷数量
     */
    @TableField(value = "web_blemish_count")
    private Integer webBlemishCount;

    /**
     * 柱道缺陷数量
     */
    @TableField(value = "pillar_count")
    private Integer pillarCount;

    /**
     * 破洞缺陷数量
     */
    @TableField(value = "hole_count")
    private Integer holeCount;

    /**
     * 双线缺陷数量
     */
    @TableField(value = "double_line_count")
    private Integer doubleLineCount;

    /**
     * 打结缺陷数量
     */
    @TableField(value = "knot_count")
    private Integer knotCount;

    /**
     * 氧化缺陷数量
     */
    @TableField(value = "oxidation_count")
    private Integer oxidationCount;

    /**
     * 油污缺陷数量
     */
    @TableField(value = "oil_stain_count")
    private Integer oilStainCount;

    /**
     * 异物缺陷数量
     */
    @TableField(value = "foreign_object_count")
    private Integer foreignObjectCount;

    /**
     * 变形缺陷数量
     */
    @TableField(value = "deformation_count")
    private Integer deformationCount;

    /**
     * 裂口缺陷数量
     */
    @TableField(value = "crack_count")
    private Integer crackCount;

    /**
     * 异色缺陷数量
     */
    @TableField(value = "discoloration_count")
    private Integer discolorationCount;

    /**
     * 毛丝缺陷数量
     */
    @TableField(value = "hairiness_count")
    private Integer hairinessCount;

    /**
     * 接线头缺陷数量
     */
    @TableField(value = "connectorlug_count")
    private Integer connectorlugCount;

    // ===== 缺陷率统计字段 =====
    
    /**
     * 网疤缺陷率(%)
     */
    @TableField(value = "web_blemish_rate")
    private BigDecimal webBlemishRate;

    /**
     * 柱道缺陷率(%)
     */
    @TableField(value = "pillar_rate")
    private BigDecimal pillarRate;

    /**
     * 破洞缺陷率(%)
     */
    @TableField(value = "hole_rate")
    private BigDecimal holeRate;

    /**
     * 双线缺陷率(%)
     */
    @TableField(value = "double_line_rate")
    private BigDecimal doubleLineRate;

    /**
     * 打结缺陷率(%)
     */
    @TableField(value = "knot_rate")
    private BigDecimal knotRate;

    /**
     * 氧化缺陷率(%)
     */
    @TableField(value = "oxidation_rate")
    private BigDecimal oxidationRate;

    /**
     * 油污缺陷率(%)
     */
    @TableField(value = "oil_stain_rate")
    private BigDecimal oilStainRate;

    /**
     * 异物缺陷率(%)
     */
    @TableField(value = "foreign_object_rate")
    private BigDecimal foreignObjectRate;

    /**
     * 变形缺陷率(%)
     */
    @TableField(value = "deformation_rate")
    private BigDecimal deformationRate;

    /**
     * 裂口缺陷率(%)
     */
    @TableField(value = "crack_rate")
    private BigDecimal crackRate;

    /**
     * 异色缺陷率(%)
     */
    @TableField(value = "discoloration_rate")
    private BigDecimal discolorationRate;

    /**
     * 毛丝缺陷率(%)
     */
    @TableField(value = "hairiness_rate")
    private BigDecimal hairinessRate;

    /**
     * 接线头缺陷率(%)
     */
    @TableField(value = "connectorlug_rate")
    private BigDecimal connectorlugRate;

    /**
     * 目数缺陷数量
     */
    @TableField(value = "mesh_count")
    private Integer meshCount;

    /**
     * 厚度缺陷数量
     */
    @TableField(value = "thickness_count")
    private Integer thicknessCount;

    /**
     * 标记点缺陷数量
     */
    @TableField(value = "markdot_count")
    private Integer markdotCount;

    /**
     * 目数缺陷率(%)
     */
    @TableField(value = "mesh_rate")
    private BigDecimal meshRate;

    /**
     * 厚度缺陷率(%)
     */
    @TableField(value = "thickness_rate")
    private BigDecimal thicknessRate;

    /**
     * 标记点缺陷率(%)
     */
    @TableField(value = "markdot_rate")
    private BigDecimal markdotRate;

    // ===== 系统字段 =====

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 删除标志(0:正常,1:删除)
     */
    @TableField(value = "del_flag")
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

package com.boyo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.domain.AoiDailyReportCore;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * AOI生产日报表核心Mapper接口
 * <AUTHOR>
 * @date 2024-01-15
 */
@Mapper
public interface AoiDailyReportCoreMapper extends BaseMapper<AoiDailyReportCore> {

    /**
     * 根据日期和设备查询核心日报
     * @param reportDate 报表日期
     * @param machineSn 设备序列号
     * @return 核心日报数据
     */
    @Select("SELECT * FROM t_aoi_daily_report_core WHERE report_date = #{reportDate} AND machine_sn = #{machineSn} AND del_flag = 0")
    AoiDailyReportCore selectByDateAndMachine(@Param("reportDate") Date reportDate, @Param("machineSn") String machineSn);

    /**
     * 根据日期和设备查询所有项目的核心日报
     * @param reportDate 报表日期
     * @param machineSn 设备序列号
     * @return 核心日报数据列表
     */
    @Select("SELECT * FROM t_aoi_daily_report_core WHERE report_date = #{reportDate} AND machine_sn = #{machineSn} AND del_flag = 0 ORDER BY project_name")
    List<AoiDailyReportCore> selectAllByDateAndMachine(@Param("reportDate") Date reportDate, @Param("machineSn") String machineSn);

    /**
     * 根据日期查询所有设备的核心日报
     * @param reportDate 报表日期
     * @return 核心日报数据列表
     */
    @Select("SELECT * FROM t_aoi_daily_report_core WHERE report_date = #{reportDate} AND del_flag = 0 ORDER BY machine_sn")
    List<AoiDailyReportCore> selectByDate(@Param("reportDate") Date reportDate);

    /**
     * 查询白板显示数据
     * @param reportDate 报表日期
     * @return 白板显示数据
     */
    @Select("SELECT " +
            "machine_sn AS machineSn, " +
            "production_time_status AS productionTimeStatus, " +
            "input_count AS inputCount, " +
            "roll_input AS rollInput, " +
            "ok_count AS okCount, " +
            "ng_count AS ngCount, " +
            "controlled_yield AS controlledYield, " +
            "roll_yield AS rollYield, " +
            "yield_by_id AS yieldById, " +
            "roll_ng_over_120 AS rollNgOver120, " +
            "roll_ng_between_50_and_120 AS rollNgBetween50And120, " +
            "IFNULL(ng_top1, '暂无') AS ngTop1, " +
            "IFNULL(ng_top2, '暂无') AS ngTop2, " +
            "IFNULL(ng_top3, '暂无') AS ngTop3, " +
            "machine_detection_rate AS machineDetectionRate, " +
            "abnormal_info AS abnormalInfo " +
            "FROM t_aoi_daily_report_core " +
            "WHERE report_date = #{reportDate} AND del_flag = 0 " +
            "ORDER BY machine_sn")
    List<Map<String, Object>> selectWhiteboardData(@Param("reportDate") Date reportDate);

    /**
     * 查询前端表格完整数据（包含所有关联表字段）
     * @param reportDate 报表日期
     * @param machineSn 设备序列号(可选)
     * @return 完整表格数据
     */
    List<Map<String, Object>> selectTableData(@Param("reportDate") Date reportDate,
                                             @Param("machineSn") String machineSn);

    /**
     * 分页查询前端表格完整数据（包含所有关联表字段）
     * @param offset 偏移量
     * @param pageSize 每页大小
     * @param reportDate 报表日期(可选)
     * @param machineSn 设备序列号(可选)
     * @param projectName 项目名称(可选)
     * @return 完整表格数据
     */
    List<Map<String, Object>> selectTableDataWithPaging(@Param("offset") Integer offset,
                                                        @Param("pageSize") Integer pageSize,
                                                        @Param("reportDate") Date reportDate,
                                                        @Param("machineSn") String machineSn,
                                                        @Param("projectName") String projectName);

    /**
     * 统计表格数据总数
     * @param reportDate 报表日期(可选)
     * @param machineSn 设备序列号(可选)
     * @param projectName 项目名称(可选)
     * @return 总数
     */
    Integer countTableData(@Param("reportDate") Date reportDate,
                          @Param("machineSn") String machineSn,
                          @Param("projectName") String projectName);

    /**
     * 统计NG数量分级数据
     * @param reportDate 报表日期(可选)
     * @param machineSn 设备序列号(可选)
     * @param projectName 项目名称(可选)
     * @return NG分级统计
     */
    Map<String, Object> countNgLevelStatistics(@Param("reportDate") Date reportDate,
                                              @Param("machineSn") String machineSn,
                                              @Param("projectName") String projectName);

    /**
     * 根据日期范围查询核心日报
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param machineSn 设备序列号(可选)
     * @return 核心日报数据列表
     */
    List<AoiDailyReportCore> selectByDateRange(@Param("startDate") Date startDate,
                                              @Param("endDate") Date endDate,
                                              @Param("machineSn") String machineSn);

    /**
     * 从原始数据生成核心日报
     * @param reportDate 报表日期
     * @param machineSn 设备序列号
     * @return 影响行数
     */
    int generateFromRawData(@Param("reportDate") Date reportDate, @Param("machineSn") String machineSn);

    /**
     * 批量生成核心日报
     * @param reportDate 报表日期
     * @return 影响行数
     */
    int batchGenerateFromRawData(@Param("reportDate") Date reportDate);

    /**
     * 更新NG前三
     * @param id 核心表ID
     * @param ngTop1 NG项前一
     * @param ngTop2 NG项前二
     * @param ngTop3 NG项前三
     * @return 影响行数
     */
    @Update("UPDATE t_aoi_daily_report_core SET ng_top1 = #{ngTop1}, ng_top2 = #{ngTop2}, ng_top3 = #{ngTop3} WHERE id = #{id}")
    int updateNgTop3(@Param("id") Long id,
                     @Param("ngTop1") String ngTop1,
                     @Param("ngTop2") String ngTop2,
                     @Param("ngTop3") String ngTop3);

    /**
     * 更新按ID良率
     * @param coreId 核心表ID
     * @return 影响行数
     */
    int updateYieldById(@Param("coreId") Long coreId);

    /**
     * 根据日期删除核心日报
     * @param reportDate 报表日期
     * @return 影响行数
     */
    @Delete("DELETE FROM t_aoi_daily_report_core WHERE DATE(report_date) = DATE(#{reportDate})")
    int deleteByDate(@Param("reportDate") Date reportDate);
}

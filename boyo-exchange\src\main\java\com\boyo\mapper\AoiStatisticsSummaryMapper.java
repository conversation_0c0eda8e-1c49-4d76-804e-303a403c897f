package com.boyo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.domain.AoiStatisticsSummary;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * AOI统计汇总Mapper接口
 * <AUTHOR>
 * @date 2024-01-15
 */
@Mapper
public interface AoiStatisticsSummaryMapper extends BaseMapper<AoiStatisticsSummary> {

    /**
     * 根据核心表ID查询统计汇总
     * @param reportCoreId 核心表ID
     * @return 统计汇总数据
     */
    @Select("SELECT * FROM t_aoi_statistics_summary WHERE report_core_id = #{reportCoreId} AND del_flag = 0")
    AoiStatisticsSummary selectByReportCoreId(@Param("reportCoreId") Long reportCoreId);

    /**
     * 根据核心表ID列表批量查询统计汇总
     * @param reportCoreIds 核心表ID列表
     * @return 统计汇总数据列表
     */
    List<AoiStatisticsSummary> selectByReportCoreIds(@Param("reportCoreIds") List<Long> reportCoreIds);

    /**
     * 从原始数据生成统计汇总
     * @param reportCoreId 核心表ID
     * @param reportDate 报表日期
     * @param machineSn 设备序列号
     * @return 影响行数
     */
    int generateFromRawData(@Param("reportCoreId") Long reportCoreId,
                           @Param("reportDate") Date reportDate,
                           @Param("machineSn") String machineSn);

    /**
     * 根据核心表ID删除统计汇总
     * @param reportCoreId 核心表ID
     * @return 影响行数
     */
    @Delete("DELETE FROM t_aoi_statistics_summary WHERE report_core_id = #{reportCoreId}")
    int deleteByCoreId(@Param("reportCoreId") Long reportCoreId);
}

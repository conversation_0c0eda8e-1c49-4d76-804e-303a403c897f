package com.boyo.wms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 领料单明细表
 * @TableName t_material_stripping_details
 */
@TableName(value ="t_material_stripping_details")
@Data
public class MaterialStrippingDetails implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 退料单编号
     */
    @TableField(value = "stripping_id")
    private String strippingId;

    /**
     * 出入库物料
     */
    @TableField(value = "material_code")
    private String materialCode;

    /**
     * 物料
     */
    @TableField(value = "material_id")
    private Long materialId;

    /**
     * 批次号
     */
    @TableField(value = "flow_batch")
    private String flowBatch;

    /**
     * 仓库
     */
    @TableField(value = "warehouse_id")
    private Long warehouseId;

    /**
     * 库区
     */
    @TableField(value = "area_id")
    private Long areaId;

    /**
     * 货位
     */
    @TableField(value = "allocation_id")
    private Long allocationId;

    /**
     * 库存
     */
    @TableField(value = "material_count")
    private BigDecimal materialCount;

    /**
     * 物料的具体成品编码
     */
    @TableField(value = "material_product_code")
    private String materialProductCode;

    /**
     * 货位
     */
    @TableField(value = "allocation_code")
    private String allocationCode;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

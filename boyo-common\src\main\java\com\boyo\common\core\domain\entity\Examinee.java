package com.boyo.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 考生管理对象 t_examinee
 *
 * <AUTHOR>
 * @date 2023-08-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_examinee")
public class Examinee extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 考试id
     */
    private Long examinationId;
    /**
     * 学生姓名
     */
    private String studentName;
    /**
     * 身份证号
     */
    private String studentCard;
    /**
     * 考试次数
     */
    private Long examinationCount;
    /**
     * 考试状态 0：未开始 1：已开始 2：已交卷 3：允许继续答题
     */
    private Integer examinationStatus;
    /**
     * 开考时间
     */
    private Date startTime;
    /**
     * 交卷时间
     */
    private Date endTime;

    @TableField(exist = false)
    private String examinationName;

    @TableField(exist = false)
    private Integer score;


}

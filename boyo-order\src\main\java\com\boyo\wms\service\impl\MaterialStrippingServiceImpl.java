package com.boyo.wms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.common.core.domain.PageQuery;
import com.boyo.common.storage.TenantStorage;
import com.boyo.common.utils.redis.RedisUtils;
import com.boyo.order.domain.FudaSupplier;
import com.boyo.wms.domain.MaterialStripping;
import com.boyo.wms.domain.MaterialStrippingDetails;
import com.boyo.wms.domain.WmsFlow;
import com.boyo.wms.domain.WmsStock;
import com.boyo.wms.domain.enums.MaterialStrippingSubmitStatus;
import com.boyo.wms.mapper.MaterialStrippingDetailsMapper;
import com.boyo.wms.mapper.MaterialStrippingMapper;
import com.boyo.wms.mapper.WmsFlowMapper;
import com.boyo.wms.mapper.WmsStockMapper;
import com.boyo.wms.service.MaterialStrippingService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.boyo.process.utils.SourceCodeUtils.getInnerCode;

/**
 * <AUTHOR>
 * @description 针对表【t_material_stripping(领料单主表)】的数据库操作Service实现
 * @createDate 2025-06-11 20:31:29
 */
@RequiredArgsConstructor
@Service
public class MaterialStrippingServiceImpl extends ServiceImpl<MaterialStrippingMapper, MaterialStripping>
    implements MaterialStrippingService {
    private final WmsStockMapper wmsStockMapper;
    private final MaterialStrippingDetailsMapper materialStrippingDetailsMapper;
    private final WmsFlowMapper wmsFlowMapper;

    private static final String FLOW_TYPE_IN = "0";
    private static final int STORAGE_STATUS_APPROVED = 2;

    @Override
    public IPage<MaterialStripping> getPage(MaterialStripping materialStripping, PageQuery pageQuery) {
        return baseMapper.getPage(pageQuery.build(), materialStripping);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean add(MaterialStripping materialStripping) {
        if (CollUtil.isEmpty(materialStripping.getDetails())) {
            throw new RuntimeException("请选择要退的产品");
        }
        String key = "TLD" + DateUtil.format(new Date(), "yyyyMMdd");
        materialStripping.setRequisitionId(key + RedisUtils.incrAtomicValue(key));
        baseMapper.insert(materialStripping);
        for (MaterialStrippingDetails detail : materialStripping.getDetails()) {
            materialStrippingDetailsMapper.insert(detail);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean audit(MaterialStripping materialStripping) {
        // 1. 判断一下库存中是否有相同编号的产品，有则报错
        List<String> productCodeList = materialStripping.getDetails()
            .stream().map(MaterialStrippingDetails::getMaterialProductCode).collect(Collectors.toList());
        List<WmsStock> wmsStockList = wmsStockMapper.selectList(
            new LambdaQueryWrapper<WmsStock>().in(WmsStock::getMaterialProductCode, productCodeList));
        if (CollUtil.isNotEmpty(wmsStockList)) {
            List<String> wmsStockExistList = wmsStockList.stream().map(WmsStock::getMaterialProductCode).collect(Collectors.toList());
            throw new RuntimeException(StrUtil.format("库存中已存在{}编号的产品", wmsStockExistList));
        }
        // 2. 添加库存
        materialStripping.setSubmitStatus(MaterialStrippingSubmitStatus.REVIEWED.getCode());
        baseMapper.updateById(materialStripping);

        for (MaterialStrippingDetails detail : materialStripping.getDetails()) {
            // 入库操作记录
            WmsFlow wmsFlow = new WmsFlow();
            wmsFlow.setFlowOpenid("I-" + DateUtil.format(new Date(), "yyMMddHHmmss") + "-" + StrUtil.padPre(Convert.toStr(RedisUtils.incrAtomicValue(TenantStorage.getTenantId() + DateUtil.format(new Date(), "yyMMdd") + "_nextNumber")), 4, "0"));
            wmsFlow.setFlowType(FLOW_TYPE_IN);
            wmsFlow.setFlowWay("退料");
            wmsFlow.setMaterialCode(detail.getMaterialCode());
            wmsFlow.setFlowBatch(detail.getFlowBatch());
            wmsFlow.setAllocationCode(detail.getAllocationCode());
            wmsFlow.setOrderCode(materialStripping.getRequisitionId());
            wmsFlow.setAllocationCode(detail.getAllocationCode());
            wmsFlow.setRemark("退料单号：" + materialStripping.getRequisitionId());
            // 入库状态  0待入库 1待审批  2审批通过/已入库  3审批驳回
            wmsFlow.setStorageStatus(STORAGE_STATUS_APPROVED);
            // 插入仓库记录流程记录，并判断插入结果
            wmsFlowMapper.insert(wmsFlow);

            //入库操作
            WmsStock wmsStock = new WmsStock();
            wmsStock.setMaterialCode(detail.getMaterialCode());
            wmsStock.setAllocationCode(detail.getAllocationCode());
            wmsStock.setFlowBatch(detail.getFlowBatch());
            wmsStock.setMaterialProductCode(detail.getMaterialProductCode());
            wmsStockMapper.insert(wmsStock);
        }
        return true;
    }
}





package com.boyo.order.controller.produceorderprocesssubtask;

import java.util.List;
import java.util.Arrays;

import com.boyo.order.domain.BomDetail;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.boyo.common.annotation.RepeatSubmit;
import com.boyo.common.annotation.Log;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.PageQuery;
import com.boyo.common.core.domain.R;
import com.boyo.common.core.validate.AddGroup;
import com.boyo.common.core.validate.EditGroup;
import com.boyo.common.enums.BusinessType;
import com.boyo.common.utils.poi.ExcelUtil;
import com.boyo.order.domain.vo.TProduceOrderProcessSubtaskVo;
import com.boyo.order.domain.bo.TProduceOrderProcessSubtaskBo;
import com.boyo.order.service.ITProduceOrderProcessSubtaskService;
import com.boyo.common.core.page.TableDataInfo;

/**
 * 工单工序子任务
 *
 * <AUTHOR>
 * @date 2025-06-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/produceOrderProcessSubtask")
public class TProduceOrderProcessSubtaskController extends BaseController {

    private final ITProduceOrderProcessSubtaskService iTProduceOrderProcessSubtaskService;

    /**
     * 查询工单工序子任务列表
     */
    @SaCheckPermission("order:produceOrderProcessSubtask:list")
    @GetMapping("/list")
    public TableDataInfo<TProduceOrderProcessSubtaskVo> list(TProduceOrderProcessSubtaskBo bo, PageQuery pageQuery) {
        return iTProduceOrderProcessSubtaskService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出工单工序子任务列表
     */
    @SaCheckPermission("order:produceOrderProcessSubtask:export")
    @Log(title = "工单工序子任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TProduceOrderProcessSubtaskBo bo, HttpServletResponse response) {
        List<TProduceOrderProcessSubtaskVo> list = iTProduceOrderProcessSubtaskService.queryList(bo);
        ExcelUtil.exportExcel(list, "工单工序子任务", TProduceOrderProcessSubtaskVo.class, response);
    }

    /**
     * 获取工单工序子任务详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("order:produceOrderProcessSubtask:query")
    @GetMapping("/{id}")
    public R<TProduceOrderProcessSubtaskVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iTProduceOrderProcessSubtaskService.queryById(id));
    }

    /**
     * 新增工单工序子任务
     */
    @SaCheckPermission("order:produceOrderProcessSubtask:add")
    @Log(title = "工单工序子任务", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TProduceOrderProcessSubtaskBo bo) {
        return toAjax(iTProduceOrderProcessSubtaskService.insertByBo(bo));
    }

    /**
     * 修改工单工序子任务
     */
    @SaCheckPermission("order:produceOrderProcessSubtask:edit")
    @Log(title = "工单工序子任务", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TProduceOrderProcessSubtaskBo bo) {
        return toAjax(iTProduceOrderProcessSubtaskService.updateByBo(bo));
    }

    /**
     * 删除工单工序子任务
     *
     * @param ids 主键串
     */
    @SaCheckPermission("order:produceOrderProcessSubtask:remove")
    @Log(title = "工单工序子任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iTProduceOrderProcessSubtaskService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 查询工单工序子任务列表（app）
     * 根据当前登录用户所关联的班组分配的设备去表里查询对应任务
     */
    //@SaCheckPermission("order:produceOrderProcessSubtask:list")
    @GetMapping("/ownList")
    public TableDataInfo<TProduceOrderProcessSubtaskVo> selectOwnList(TProduceOrderProcessSubtaskBo bo, PageQuery pageQuery) {
        return iTProduceOrderProcessSubtaskService.selectOwnList(bo, pageQuery);
    }

    /**
     * 根据工序子任务id领料
     */
    @GetMapping("/bom/getmaterial")
    public R<List<BomDetail>> bomMaterial(@RequestParam("id") Long id){
        List<BomDetail> bomDetails = iTProduceOrderProcessSubtaskService.materialRequisitionByProcessSubtask(id);
        return R.ok(bomDetails);
    }

    /**
     * 根据工序子任务id退料
     */
    @GetMapping("/bom/returnMaterial")
    public R<List<BomDetail>> bomReturnMaterial(@RequestParam("id") Long id){
        List<BomDetail> bomDetails = iTProduceOrderProcessSubtaskService.returnMaterialRequisitionByProcessSubtask(id);
        return R.ok(bomDetails);
    }

    /**
     * 完工
     */
    @GetMapping("/completeOrderProcessSubtask")
    public R completeOrderProcessSubtask(@RequestParam("id") Long id){
        Boolean aBoolean = iTProduceOrderProcessSubtaskService.completeOrderProcessSubtask(id);
        if (aBoolean){
            return R.ok();
        }
        return R.fail();
    }




}

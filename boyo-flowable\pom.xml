<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>boyo-flowable-plus</artifactId>
        <groupId>com.boyo</groupId>
        <version>0.8.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>boyo-flowable</artifactId>

    <description>
        flowable工作流
    </description>

    <dependencies>

        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-common</artifactId>
        </dependency>

        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter-process</artifactId>
        </dependency>

        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
            <version>5.3.1</version>
        </dependency>

    </dependencies>
</project>

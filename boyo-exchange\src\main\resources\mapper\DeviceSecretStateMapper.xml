<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mapper.DeviceSecretStateMapper">

    <resultMap id="BaseResultMap" type="com.boyo.domain.DeviceSecretState">
            <id property="machineId" column="machine_id" jdbcType="BIGINT"/>
            <result property="statusCode" column="status_code" jdbcType="VARCHAR"/>
            <result property="errMsg" column="err_msg" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        machine_id,status_code,err_msg,
        create_time
    </sql>
</mapper>

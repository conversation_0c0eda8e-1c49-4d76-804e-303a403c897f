package com.boyo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.domain.AoiDailyReport;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * AOI生产日报表Mapper接口
 * <AUTHOR>
 * @date 2024-01-15
 */
@Mapper
public interface AoiDailyReportMapper extends BaseMapper<AoiDailyReport> {

    /**
     * 根据日期和设备查询日报
     * @param reportDate 报表日期
     * @param machineSn 设备序列号
     * @return 日报数据
     */
    @Select("SELECT * FROM t_aoi_daily_report WHERE report_date = #{reportDate} AND machine_sn = #{machineSn} AND del_flag = 0")
    AoiDailyReport selectByDateAndMachine(@Param("reportDate") Date reportDate, @Param("machineSn") String machineSn);

    /**
     * 根据日期查询所有设备的日报
     * @param reportDate 报表日期
     * @return 日报数据列表
     */
    @Select("SELECT * FROM t_aoi_daily_report WHERE report_date = #{reportDate} AND del_flag = 0 ORDER BY machine_sn")
    List<AoiDailyReport> selectByDate(@Param("reportDate") Date reportDate);

    /**
     * 根据日期查询所有设备的日报(轻量级，仅核心字段)
     * @param reportDate 报表日期
     * @return 日报数据列表
     */
    List<Map<String, Object>> selectByDateLight(@Param("reportDate") Date reportDate);

    /**
     * 根据日期范围查询日报
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param machineSn 设备序列号(可选)
     * @return 日报数据列表
     */
    List<AoiDailyReport> selectByDateRange(@Param("startDate") Date startDate, 
                                          @Param("endDate") Date endDate, 
                                          @Param("machineSn") String machineSn);

    /**
     * 查询白板显示数据
     * @param reportDate 报表日期
     * @return 白板显示数据
     */
    List<Map<String, Object>> selectWhiteboardData(@Param("reportDate") Date reportDate);

    /**
     * 统计设备良率趋势
     * @param machineSn 设备序列号
     * @param days 天数
     * @return 良率趋势数据
     */
    List<Map<String, Object>> selectYieldTrend(@Param("machineSn") String machineSn, @Param("days") Integer days);

    /**
     * 统计缺陷分布
     * @param reportDate 报表日期
     * @param machineSn 设备序列号(可选)
     * @return 缺陷分布数据
     */
    List<Map<String, Object>> selectDefectDistribution(@Param("reportDate") Date reportDate, 
                                                       @Param("machineSn") String machineSn);

    /**
     * 从原始数据生成日报
     * @param reportDate 报表日期
     * @param machineSn 设备序列号
     * @return 影响行数
     */
    int generateFromRawData(@Param("reportDate") Date reportDate, @Param("machineSn") String machineSn);

    /**
     * 批量生成日报
     * @param reportDate 报表日期
     * @return 影响行数
     */
    int batchGenerateFromRawData(@Param("reportDate") Date reportDate);

    /**
     * 查询设备产能统计
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 产能统计数据
     */
    List<Map<String, Object>> selectProductionStatistics(@Param("startDate") Date startDate, 
                                                         @Param("endDate") Date endDate);

    /**
     * 查询质量指标统计
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param machineSn 设备序列号(可选)
     * @return 质量指标数据
     */
    List<Map<String, Object>> selectQualityMetrics(@Param("startDate") Date startDate,
                                                   @Param("endDate") Date endDate,
                                                   @Param("machineSn") String machineSn);

    /**
     * 计算NG前三缺陷
     * @param reportId 报表ID
     * @return NG前三缺陷数据
     */
    List<Map<String, Object>> calculateNgTop3(@Param("reportId") Long reportId);

    /**
     * 更新NG前三
     * @param reportId 报表ID
     * @param ngTop1 NG项前一
     * @param ngTop2 NG项前二
     * @param ngTop3 NG项前三
     * @return 影响行数
     */
    int updateNgTop3(@Param("reportId") Long reportId,
                     @Param("ngTop1") String ngTop1,
                     @Param("ngTop2") String ngTop2,
                     @Param("ngTop3") String ngTop3);

    /**
     * 计算并更新缺陷率
     * @param reportId 报表ID
     * @return 影响行数
     */
    int calculateAndUpdateDefectRates(@Param("reportId") Long reportId);

}

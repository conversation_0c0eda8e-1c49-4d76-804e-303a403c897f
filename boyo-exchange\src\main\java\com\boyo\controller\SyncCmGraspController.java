package com.boyo.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.client.CMGraspApiUtils;
import com.boyo.common.core.domain.R;
import com.boyo.common.storage.TenantStorage;
import com.boyo.order.domain.*;
import com.boyo.order.domain.dto.*;
import com.boyo.order.domain.vo.BomVo;
import com.boyo.order.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29 11:06
 */
@RestController
@RequestMapping("/SyncCmGrasp")
@Slf4j
public class SyncCmGraspController {

    @Autowired
    private CMGraspApiUtils cmGraspApiUtils;
    @Resource
    private BaseMaterialMapper baseMaterialMapper;
    @Resource
    private BomMapper bomMapper;
    @Resource
    private BomDetailMapper bomDetailMapper;
    @Resource
    private FudaSupplierMapper fudaSupplierMapper;
    @Resource
    private FudaCustomerMapper fudaCustomerMapper;

    /**
      * <AUTHOR>
      * @param
      * @return com.boyo.common.core.domain.R
      * @remark 从管家婆同步物料
      * @date 2025/5/29 11:06
     */
    @PostMapping("/syncMaterial")
    public R syncMaterial() {
        JSONObject resultData = cmGraspApiUtils.getCMGraspData("ptype", "{\"PageSize\": 10,\"PageIndex\": 1}");
        if (ObjectUtil.isNotNull(resultData) && Integer.valueOf(resultData.getString("code"))<0){
            log.error("请求失败");
            return R.fail("请求失败");
        }
        Integer pagetotal = resultData.getInteger("pagetotal");
        if (ObjectUtil.isNull(pagetotal)){
            log.error("返回总条数为空");
            return R.fail("返回总条数为空");
        }
        //总条数不为空算出需要循环多少次才能取出全部数据
        int number = (int)(pagetotal/200)+1;
        System.out.println("number: "+number);
        for (int i = 0; i < number; i++) {
            String paramJson = "{\"PageSize\": 200,\"PageIndex\" : "+(i+1)+"}";
            JSONObject result = cmGraspApiUtils.getCMGraspData("ptype",paramJson);
            if (ObjectUtil.isNotNull(result) && Integer.valueOf(result.getString("code"))<0){
                log.error("请求失败");
                return R.fail();
            }
            JSONArray response = result.getJSONArray("response");
            if (ObjectUtil.isNull(response)){
                log.error("返回值为空");
            }
            List<BaseMaterialDto> baseMaterialDtos = JSON.parseArray(response.toString(), BaseMaterialDto.class);
            if (ObjectUtil.isNotNull(baseMaterialDtos) && baseMaterialDtos.size()>0){
                for (BaseMaterialDto baseMaterialDto : baseMaterialDtos) {
                    //查询表里是否存在
                    QueryWrapper<BaseMaterial> baseMaterialQueryWrapper = new QueryWrapper<>();
                    baseMaterialQueryWrapper.eq("typeid",baseMaterialDto.getTypeid());
                    Long count = baseMaterialMapper.selectCount(baseMaterialQueryWrapper);
                    if (count == 0){
                        //新增
                        BaseMaterial baseMaterial = BeanUtil.toBean(baseMaterialDto, BaseMaterial.class);
                        baseMaterial.setTenantId(1710998872911200258L);
                        baseMaterial.setMaterialName(baseMaterialDto.getFullname());
                        baseMaterial.setMaterialCode(baseMaterialDto.getUsercode());
                        baseMaterial.setMaterialStatus(String.valueOf(baseMaterialDto.getDeleted()));
                        baseMaterial.setMaterialType(baseMaterialDto.getGpfullname());
                        baseMaterial.setMaterialNorms(baseMaterialDto.getStandard());
                        baseMaterialMapper.insert(baseMaterial);
                    }else {
                        //todo:修改需要看下是否需要
                    }
                }
            }
        }
        return R.ok("物料同步erp成功");
    }


    @PostMapping("/syncBom")
    public R syncBom(){
        JSONObject resultData = cmGraspApiUtils.getCMGraspData("standardbom", "{\"PageSize\": 10,\"PageIndex\": 1,\"audittype\": 1}");
        if (ObjectUtil.isNotNull(resultData) && Integer.valueOf(resultData.getString("code"))<0){
            log.error("请求失败");
            return R.fail("请求失败");
        }
        Integer pagetotal = resultData.getInteger("pagetotal");
        if (ObjectUtil.isNull(pagetotal)){
            log.error("返回总条数为空");
            return R.fail("返回总条数为空");
        }
        //总条数不为空算出需要循环多少次才能取出全部数据
        int number = (int)(pagetotal/200)+1;
        for (int i = 0; i < number; i++) {
            String paramJson = "{\"PageSize\": 200,\"audittype\" : 1,\"PageIndex\" : "+(i+1)+"}";
            JSONObject result = cmGraspApiUtils.getCMGraspData("standardbom",paramJson);
            if (ObjectUtil.isNotNull(result) && Integer.valueOf(result.getString("code"))<0){
                log.error("请求失败");
                return R.fail("请求失败");
            }
            JSONArray response = result.getJSONArray("response");
            if (ObjectUtil.isNull(response)){
                log.error("返回值为空");
                return R.fail("返回值为空");
            }
            List<BomDto> bomDtos = JSON.parseArray(response.toString(), BomDto.class);
            if (ObjectUtil.isNotNull(bomDtos) && bomDtos.size()>0){
                for (BomDto bomDto : bomDtos) {
                    BomVo bomVo = bomMapper.selectVoById(bomDto.getId());
                    if (ObjectUtil.isNull(bomVo)){
                        //新增bom
                        Bom bom = new Bom();
                        bom.setId(bomDto.getId());
                        bom.setBomStatus("0");
                        bom.setBomVersion(1);
                        bom.setTenantId(1710998872911200258L);
                        bom.setCreateBy(bomDto.getInputno());
                        bom.setCreateTime(DateUtil.parseDate(bomDto.getBomdate()));
                        bom.setBomtype(bomDto.getBomtype());
                        bom.setBomfullname(bomDto.getBomfullname());
                        bom.setBomusercode(bomDto.getBomusercode());
                        bom.setQty(bomDto.getQty());
                        bom.setManhour(new BigDecimal(bomDto.getManhour()));
                        bom.setAuditor(bomDto.getAuditor());
                        bom.setAuditdate(DateUtil.parseDate(bomDto.getAuditdate()));
                        bom.setPtypeid(bomDto.getPtypeid());
                        bom.setPusercode(bomDto.getPusercode());
                        //查询该bom关联的物料id
                        QueryWrapper<BaseMaterial> baseMaterialQueryWrapper = new QueryWrapper<>();
                        baseMaterialQueryWrapper.eq("typeid",bomDto.getPtypeid());
                        List<BaseMaterial> baseMaterials = baseMaterialMapper.selectList(baseMaterialQueryWrapper);
                        if (ObjectUtil.isNotNull(baseMaterials) && baseMaterials.size()>0){
                            bom.setMaterialId(baseMaterials.get(0).getId());
                        }
                        bomMapper.insert(bom);
                        //新增bom详情
                        if (ObjectUtil.isNotNull(bomDto.getDetail()) && bomDto.getDetail().size()>0){
                            for (BomDetailDto bomDetailDto : bomDto.getDetail()) {
                                BomDetail bomDetail = new BomDetail();
                                bomDetail.setBomId(Long.valueOf(bomDetailDto.getSource1()));
                                bomDetail.setMaterialCount(new BigDecimal(bomDetailDto.getQty()));
                                bomDetail.setCreateBy(bomDto.getInputno());
                                bomDetail.setCreateTime(DateUtil.parseDate(bomDto.getBomdate()));
                                bomDetail.setBaseqty(bomDetailDto.getBaseqty());
                                bomDetail.setWastage(new BigDecimal(bomDetailDto.getWastage()));
                                bomDetail.setScale(new BigDecimal(bomDetailDto.getScale()));
                                bomDetail.setComment(bomDetailDto.getComment());
                                bomDetail.setComment2(bomDetailDto.getComment2());
                                bomDetail.setPtypeid(bomDetailDto.getPtypeid());
                                bomDetail.setPusercode(bomDetailDto.getPusercode());
                                bomDetail.setSource2(bomDetailDto.getSource2());
                                bomDetail.setPId(-1L);
                                //查询该bom关联的物料id
                                QueryWrapper<BaseMaterial> baseMaterialQueryWrapperByDetail = new QueryWrapper<>();
                                baseMaterialQueryWrapperByDetail.eq("typeid",bomDetailDto.getPtypeid());
                                List<BaseMaterial> baseMaterialList = baseMaterialMapper.selectList(baseMaterialQueryWrapperByDetail);
                                if (ObjectUtil.isNotNull(baseMaterialList) && baseMaterialList.size()>0){
                                    bomDetail.setMaterialId(baseMaterialList.get(0).getId());
                                }
                                bomDetailMapper.insert(bomDetail);
                            }
                        }
                    }else {
                        //修改
                    }
                }
            }
        }
        return R.ok("同步bom成功");
    }

    @PostMapping("/syncFudaSupplier")
    public R syncFudaSupplier(){
        JSONObject resultData = cmGraspApiUtils.getCMGraspData("supplier", "{\"PageSize\": 10,\"PageIndex\": 1,\"bsonnum\":1}");
        if (ObjectUtil.isNotNull(resultData) && Integer.valueOf(resultData.getString("code"))<0){
            log.error("请求失败");
            return R.fail("请求失败");
        }
        Integer pagetotal = resultData.getInteger("pagetotal");
        if (ObjectUtil.isNull(pagetotal)){
            log.error("返回总条数为空");
            return R.fail("返回总条数为空");
        }
        //总条数不为空算出需要循环多少次才能取出全部数据
        int number = (int)(pagetotal/200)+1;
        for (int i = 0; i < number; i++) {
            //String paramJson = "{\"PageSize\": 200,\"PageIndex\" : "+(i+1)+"}";
            String paramJson = "{\"PageSize\": 200,\"bsonnum\": 1,\"PageIndex\" : "+(i+1)+"}";
            JSONObject result = cmGraspApiUtils.getCMGraspData("supplier",paramJson);
            if (ObjectUtil.isNotNull(result) && Integer.valueOf(result.getString("code"))<0){
                log.error("请求失败");
                return R.fail("请求失败");
            }
            JSONArray response = result.getJSONArray("response");
            if (ObjectUtil.isNull(response)){
                log.error("返回值为空");
                return R.fail("返回值为空");
            }
            List<FudaSupplierDto> fudaSupplierDtos = JSON.parseArray(response.toString(), FudaSupplierDto.class);
            if (ObjectUtil.isNotNull(fudaSupplierDtos) && fudaSupplierDtos.size()>0){
                for (FudaSupplierDto fudaSupplierDto : fudaSupplierDtos) {
                    //根据供应商名称查询
                    QueryWrapper<FudaSupplier> fudaSupplierQueryWrapper = new QueryWrapper<>();
                    fudaSupplierQueryWrapper.eq("name",fudaSupplierDto.getName());
                    Long count = fudaSupplierMapper.selectCount(fudaSupplierQueryWrapper);
                    if (count ==0){
                        //没有，新增
                        FudaSupplier fudaSupplier = new FudaSupplier();
                        fudaSupplier.setCode(fudaSupplierDto.getUsercode());
                        fudaSupplier.setName(fudaSupplierDto.getName());
                        fudaSupplier.setNameJiancheng(fudaSupplierDto.getName());
                        fudaSupplier.setZhujima(fudaSupplierDto.getNamepy());
                        fudaSupplier.setTypeCode(String.valueOf(fudaSupplierDto.getPreprice()));
                        fudaSupplierMapper.insert(fudaSupplier);
                    }
                }
            }
        }
        return R.ok("同步供应商成功");
    }

    @PostMapping("/syncFudaCustomer")
    public R syncFudaCustomer(){
        JSONObject resultData = cmGraspApiUtils.getCMGraspData("customer", "{\"PageSize\": 10,\"PageIndex\": 1,\"bsonnum\":1}");
        if (ObjectUtil.isNotNull(resultData) && Integer.valueOf(resultData.getString("code"))<0){
            log.error("请求失败");
            return R.fail("请求失败");
        }
        Integer pagetotal = resultData.getInteger("pagetotal");
        if (ObjectUtil.isNull(pagetotal)){
            log.error("返回总条数为空");
            return R.fail("返回总条数为空");
        }
        //总条数不为空算出需要循环多少次才能取出全部数据
        int number = (int)(pagetotal/200)+1;
        for (int i = 0; i < number; i++) {
            //String paramJson = "{\"PageSize\": 200,\"PageIndex\" : "+(i+1)+"}";
            String paramJson = "{\"PageSize\": 200,\"bsonnum\": 1,\"PageIndex\" : "+(i+1)+"}";
            JSONObject result = cmGraspApiUtils.getCMGraspData("customer",paramJson);
            if (ObjectUtil.isNotNull(result) && Integer.valueOf(result.getString("code"))<0){
                log.error("请求失败");
                return R.fail("请求失败");
            }
            JSONArray response = result.getJSONArray("response");
            if (ObjectUtil.isNull(response)){
                log.error("返回值为空");
                return R.fail("返回值为空");
            }
            List<FudaCustomerDto> fudaCustomerDtos = JSON.parseArray(response.toString(), FudaCustomerDto.class);
            if (ObjectUtil.isNotNull(fudaCustomerDtos) && fudaCustomerDtos.size()>0){
                for (FudaCustomerDto fudaCustomerDto : fudaCustomerDtos) {
                    //根据客户名称查询
                    QueryWrapper<FudaCustomer> fudaCustomerQueryWrapper = new QueryWrapper<>();
                    fudaCustomerQueryWrapper.eq("name",fudaCustomerDto.getFullname());
                    Long count = fudaCustomerMapper.selectCount(fudaCustomerQueryWrapper);
                    if (count ==0){
                        //没有，新增
                        FudaCustomer fudaCustomer = new FudaCustomer();
                        fudaCustomer.setCode(fudaCustomerDto.getUsercode());
                        fudaCustomer.setName(fudaCustomerDto.getFullname());
                        fudaCustomer.setNameJiancheng(fudaCustomerDto.getName());
                        fudaCustomer.setZhujima(fudaCustomerDto.getNamepy());
                        fudaCustomer.setTypeCode(String.valueOf(fudaCustomerDto.getPreprice()));
                        fudaCustomerMapper.insert(fudaCustomer);
                    }
                }
            }
        }
        return R.ok("同步客户成功");
    }

    @PostMapping("/syncWarehouse")
    public R syncWarehouse(){
        JSONObject resultData = cmGraspApiUtils.getCMGraspData("stock", "{\"PageSize\": 200,\"PageIndex\": 1,\"bsonnum\":1}");
        if (ObjectUtil.isNotNull(resultData) && Integer.valueOf(resultData.getString("code"))<0){
            log.error("请求失败");
            return R.fail("请求失败");
        }
        Integer pagetotal = resultData.getInteger("pagetotal");
        if (ObjectUtil.isNull(pagetotal)){
            log.error("返回总条数为空");
            return R.fail("返回总条数为空");
        }
        if (ObjectUtil.isNotNull(pagetotal)){
            return R.fail();
        }
        //总条数不为空算出需要循环多少次才能取出全部数据
        int number = (int)(pagetotal/200)+1;
//        for (int i = 0; i < number; i++) {
//            //String paramJson = "{\"PageSize\": 200,\"PageIndex\" : "+(i+1)+"}";
//            String paramJson = "{\"PageSize\": 200,\"bsonnum\": 1,\"PageIndex\" : "+(i+1)+"}";
//            JSONObject result = cmGraspApiUtils.getCMGraspData("customer",paramJson);
//            if (ObjectUtil.isNotNull(result) && Integer.valueOf(result.getString("code"))<0){
//                log.error("请求失败");
//                return R.fail("请求失败");
//            }
//            JSONArray response = result.getJSONArray("response");
//            if (ObjectUtil.isNull(response)){
//                log.error("返回值为空");
//                return R.fail("返回值为空");
//            }
//            List<FudaCustomerDto> fudaCustomerDtos = JSON.parseArray(response.toString(), FudaCustomerDto.class);
//            if (ObjectUtil.isNotNull(fudaCustomerDtos) && fudaCustomerDtos.size()>0){
//                for (FudaCustomerDto fudaCustomerDto : fudaCustomerDtos) {
//                    //根据客户名称查询
//                    QueryWrapper<FudaCustomer> fudaCustomerQueryWrapper = new QueryWrapper<>();
//                    fudaCustomerQueryWrapper.eq("name",fudaCustomerDto.getFullname());
//                    Long count = fudaCustomerMapper.selectCount(fudaCustomerQueryWrapper);
//                    if (count ==0){
//                        //没有，新增
//                        FudaCustomer fudaCustomer = new FudaCustomer();
//                        fudaCustomer.setCode(fudaCustomerDto.getUsercode());
//                        fudaCustomer.setName(fudaCustomerDto.getFullname());
//                        fudaCustomer.setNameJiancheng(fudaCustomerDto.getName());
//                        fudaCustomer.setZhujima(fudaCustomerDto.getNamepy());
//                        fudaCustomer.setTypeCode(String.valueOf(fudaCustomerDto.getPreprice()));
//                        fudaCustomerMapper.insert(fudaCustomer);
//                    }
//                }
//            }
//        }
        return R.ok("同步客户成功");
    }

    @PostMapping("/syncAllocation")
    public R syncAllocation(){
        JSONObject resultData = cmGraspApiUtils.getCMGraspData("position", "{\"PageSize\": 200,\"PageIndex\": 1}");
        if (ObjectUtil.isNotNull(resultData) && Integer.valueOf(resultData.getString("code"))<0){
            log.error("请求失败");
            return R.fail("请求失败");
        }
        Integer pagetotal = resultData.getInteger("pagetotal");
        if (ObjectUtil.isNull(pagetotal)){
            log.error("返回总条数为空");
            return R.fail("返回总条数为空");
        }
        if (ObjectUtil.isNotNull(pagetotal)){
            return R.fail();
        }
        //总条数不为空算出需要循环多少次才能取出全部数据
        int number = (int)(pagetotal/200)+1;
//        for (int i = 0; i < number; i++) {
//            //String paramJson = "{\"PageSize\": 200,\"PageIndex\" : "+(i+1)+"}";
//            String paramJson = "{\"PageSize\": 200,\"bsonnum\": 1,\"PageIndex\" : "+(i+1)+"}";
//            JSONObject result = cmGraspApiUtils.getCMGraspData("customer",paramJson);
//            if (ObjectUtil.isNotNull(result) && Integer.valueOf(result.getString("code"))<0){
//                log.error("请求失败");
//                return R.fail("请求失败");
//            }
//            JSONArray response = result.getJSONArray("response");
//            if (ObjectUtil.isNull(response)){
//                log.error("返回值为空");
//                return R.fail("返回值为空");
//            }
//            List<FudaCustomerDto> fudaCustomerDtos = JSON.parseArray(response.toString(), FudaCustomerDto.class);
//            if (ObjectUtil.isNotNull(fudaCustomerDtos) && fudaCustomerDtos.size()>0){
//                for (FudaCustomerDto fudaCustomerDto : fudaCustomerDtos) {
//                    //根据客户名称查询
//                    QueryWrapper<FudaCustomer> fudaCustomerQueryWrapper = new QueryWrapper<>();
//                    fudaCustomerQueryWrapper.eq("name",fudaCustomerDto.getFullname());
//                    Long count = fudaCustomerMapper.selectCount(fudaCustomerQueryWrapper);
//                    if (count ==0){
//                        //没有，新增
//                        FudaCustomer fudaCustomer = new FudaCustomer();
//                        fudaCustomer.setCode(fudaCustomerDto.getUsercode());
//                        fudaCustomer.setName(fudaCustomerDto.getFullname());
//                        fudaCustomer.setNameJiancheng(fudaCustomerDto.getName());
//                        fudaCustomer.setZhujima(fudaCustomerDto.getNamepy());
//                        fudaCustomer.setTypeCode(String.valueOf(fudaCustomerDto.getPreprice()));
//                        fudaCustomerMapper.insert(fudaCustomer);
//                    }
//                }
//            }
//        }
        return R.ok("同步客户成功");
    }

    @PostMapping("/syncWorkshopUser")
    public R syncWorkshopUser(){
        JSONObject resultData = cmGraspApiUtils.getCMGraspData("dtype", "{\"PageSize\": 200,\"PageIndex\": 1,\"bSonnum\":0}");
        if (ObjectUtil.isNotNull(resultData) && Integer.valueOf(resultData.getString("code"))<0){
            log.error("请求失败");
            return R.fail("请求失败");
        }
        Integer pagetotal = resultData.getInteger("pagetotal");
        if (ObjectUtil.isNull(pagetotal)){
            log.error("返回总条数为空");
            return R.fail("返回总条数为空");
        }
        if (ObjectUtil.isNotNull(pagetotal)){
            return R.fail();
        }
        //总条数不为空算出需要循环多少次才能取出全部数据
        int number = (int)(pagetotal/200)+1;
//        for (int i = 0; i < number; i++) {
//            //String paramJson = "{\"PageSize\": 200,\"PageIndex\" : "+(i+1)+"}";
//            String paramJson = "{\"PageSize\": 200,\"bsonnum\": 1,\"PageIndex\" : "+(i+1)+"}";
//            JSONObject result = cmGraspApiUtils.getCMGraspData("customer",paramJson);
//            if (ObjectUtil.isNotNull(result) && Integer.valueOf(result.getString("code"))<0){
//                log.error("请求失败");
//                return R.fail("请求失败");
//            }
//            JSONArray response = result.getJSONArray("response");
//            if (ObjectUtil.isNull(response)){
//                log.error("返回值为空");
//                return R.fail("返回值为空");
//            }
//            List<FudaCustomerDto> fudaCustomerDtos = JSON.parseArray(response.toString(), FudaCustomerDto.class);
//            if (ObjectUtil.isNotNull(fudaCustomerDtos) && fudaCustomerDtos.size()>0){
//                for (FudaCustomerDto fudaCustomerDto : fudaCustomerDtos) {
//                    //根据客户名称查询
//                    QueryWrapper<FudaCustomer> fudaCustomerQueryWrapper = new QueryWrapper<>();
//                    fudaCustomerQueryWrapper.eq("name",fudaCustomerDto.getFullname());
//                    Long count = fudaCustomerMapper.selectCount(fudaCustomerQueryWrapper);
//                    if (count ==0){
//                        //没有，新增
//                        FudaCustomer fudaCustomer = new FudaCustomer();
//                        fudaCustomer.setCode(fudaCustomerDto.getUsercode());
//                        fudaCustomer.setName(fudaCustomerDto.getFullname());
//                        fudaCustomer.setNameJiancheng(fudaCustomerDto.getName());
//                        fudaCustomer.setZhujima(fudaCustomerDto.getNamepy());
//                        fudaCustomer.setTypeCode(String.valueOf(fudaCustomerDto.getPreprice()));
//                        fudaCustomerMapper.insert(fudaCustomer);
//                    }
//                }
//            }
//        }
        return R.ok("同步客户成功");
    }




}

package com.boyo.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ContractStatus {
    //    待收款
    TO_RECEIVED("0"),
    //    待审核
    TO_EXAMINE("1"),
    //    待分配
    TO_DISTRIBUTION("2"),
    //    待执行
    TO_EXECUTE("3"),
    //    执行中
    EXECUTE("4"),
    //    已完成
    FINISH("5"),
    //    已拒绝
    REFUSE("9"),
    ;
    private final String status;

}

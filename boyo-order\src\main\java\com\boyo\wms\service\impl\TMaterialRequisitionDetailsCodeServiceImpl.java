package com.boyo.wms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.boyo.wms.domain.bo.TMaterialRequisitionDetailsCodeBo;
import com.boyo.wms.domain.vo.TMaterialRequisitionDetailsCodeVo;
import com.boyo.wms.domain.TMaterialRequisitionDetailsCode;
import com.boyo.wms.mapper.TMaterialRequisitionDetailsCodeMapper;
import com.boyo.wms.service.ITMaterialRequisitionDetailsCodeService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 领料单详情物料Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@RequiredArgsConstructor
@Service
public class TMaterialRequisitionDetailsCodeServiceImpl implements ITMaterialRequisitionDetailsCodeService {

    private final TMaterialRequisitionDetailsCodeMapper baseMapper;

    /**
     * 查询领料单详情物料
     */
    @Override
    public TMaterialRequisitionDetailsCodeVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询领料单详情物料列表
     */
    @Override
    public TableDataInfo<TMaterialRequisitionDetailsCodeVo> queryPageList(TMaterialRequisitionDetailsCodeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TMaterialRequisitionDetailsCode> lqw = buildQueryWrapper(bo);
        Page<TMaterialRequisitionDetailsCodeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询领料单详情物料列表
     */
    @Override
    public List<TMaterialRequisitionDetailsCodeVo> queryList(TMaterialRequisitionDetailsCodeBo bo) {
        LambdaQueryWrapper<TMaterialRequisitionDetailsCode> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TMaterialRequisitionDetailsCode> buildQueryWrapper(TMaterialRequisitionDetailsCodeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TMaterialRequisitionDetailsCode> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getTenantId() != null, TMaterialRequisitionDetailsCode::getTenantId, bo.getTenantId());
        lqw.like(StringUtils.isNotBlank(bo.getMaterialName()), TMaterialRequisitionDetailsCode::getMaterialName, bo.getMaterialName());
        lqw.eq(bo.getMaterialRequisitionDetailsId() != null, TMaterialRequisitionDetailsCode::getMaterialRequisitionDetailsId, bo.getMaterialRequisitionDetailsId());
        lqw.eq(StringUtils.isNotBlank(bo.getMaterialProductCode()), TMaterialRequisitionDetailsCode::getMaterialProductCode, bo.getMaterialProductCode());
        return lqw;
    }

    /**
     * 新增领料单详情物料
     */
    @Override
    public Boolean insertByBo(TMaterialRequisitionDetailsCodeBo bo) {
        TMaterialRequisitionDetailsCode add = BeanUtil.toBean(bo, TMaterialRequisitionDetailsCode.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改领料单详情物料
     */
    @Override
    public Boolean updateByBo(TMaterialRequisitionDetailsCodeBo bo) {
        TMaterialRequisitionDetailsCode update = BeanUtil.toBean(bo, TMaterialRequisitionDetailsCode.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TMaterialRequisitionDetailsCode entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除领料单详情物料
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}

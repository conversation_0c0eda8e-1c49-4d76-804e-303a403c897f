package com.boyo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.domain.AoiOperatorInfo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * AOI操作员信息Mapper接口
 * <AUTHOR>
 * @date 2024-01-15
 */
@Mapper
public interface AoiOperatorInfoMapper extends BaseMapper<AoiOperatorInfo> {

    /**
     * 根据核心表ID查询操作员信息
     * @param reportCoreId 核心表ID
     * @return 操作员信息数据
     */
    @Select("SELECT * FROM t_aoi_operator_info WHERE report_core_id = #{reportCoreId} AND del_flag = 0")
    AoiOperatorInfo selectByReportCoreId(@Param("reportCoreId") Long reportCoreId);

    /**
     * 根据核心表ID列表批量查询操作员信息
     * @param reportCoreIds 核心表ID列表
     * @return 操作员信息数据列表
     */
    List<AoiOperatorInfo> selectByReportCoreIds(@Param("reportCoreIds") List<Long> reportCoreIds);

    /**
     * 从原始数据生成操作员信息
     * @param reportCoreId 核心表ID
     * @param reportDate 报表日期
     * @param machineSn 设备序列号
     * @return 影响行数
     */
    int generateFromRawData(@Param("reportCoreId") Long reportCoreId,
                           @Param("reportDate") Date reportDate,
                           @Param("machineSn") String machineSn);

    /**
     * 根据核心表ID删除操作员信息
     * @param reportCoreId 核心表ID
     * @return 影响行数
     */
    @Delete("DELETE FROM t_aoi_operator_info WHERE report_core_id = #{reportCoreId}")
    int deleteByCoreId(@Param("reportCoreId") Long reportCoreId);
}

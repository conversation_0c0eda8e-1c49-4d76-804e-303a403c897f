<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>boyo-flowable-plus</artifactId>
        <groupId>com.boyo</groupId>
        <version>0.8.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>boyo-admin</artifactId>

    <description>
        web服务入口
    </description>

    <dependencies>

        <!-- spring-boot-devtools -->
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-devtools</artifactId>-->
<!--            <optional>true</optional> &lt;!&ndash; 表示依赖不会传递 &ndash;&gt;-->
<!--        </dependency>-->

        <!-- Mysql驱动包 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <!-- Oracle -->
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
        </dependency>
        <!-- PostgreSql -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <!-- SqlServer -->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
        </dependency>

        <!-- 核心模块-->
        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-framework</artifactId>
        </dependency>

        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-system</artifactId>
        </dependency>
        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-job</artifactId>
        </dependency>

        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-oss</artifactId>
        </dependency>

        <!-- 代码生成-->
        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-generator</artifactId>
        </dependency>

        <!-- flowable模块-->
        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-flowable</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-cp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-order</artifactId>
        </dependency>

        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-exchange</artifactId>
        </dependency>

        <!-- skywalking 整合 logback -->
<!--        <dependency>-->
<!--            <groupId>org.apache.skywalking</groupId>-->
<!--            <artifactId>apm-toolkit-logback-1.x</artifactId>-->
<!--            <version>${与你的agent探针版本保持一致}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.apache.skywalking</groupId>-->
<!--            <artifactId>apm-toolkit-trace</artifactId>-->
<!--            <version>${与你的agent探针版本保持一致}</version>-->
<!--        </dependency>-->

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.2.2</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${project.artifactId}</warName>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <repositories>
        <repository>
            <id>Eclipse Paho Repository</id>
            <url>https://repo.eclipse.org/content/repositories/paho-releases/</url>
        </repository>
    </repositories>
</project>

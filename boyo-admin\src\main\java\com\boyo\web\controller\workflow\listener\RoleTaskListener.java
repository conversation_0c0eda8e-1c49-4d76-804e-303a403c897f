package com.boyo.web.controller.workflow.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.boyo.common.core.domain.entity.SysDept;
import com.boyo.common.core.domain.entity.SysRole;
import com.boyo.common.core.domain.entity.SysUser;
import com.boyo.common.core.domain.model.LoginUser;
import com.boyo.common.helper.LoginHelper;
import com.boyo.flowable.common.constant.TaskConstants;
import com.boyo.system.service.ISysDeptService;
import com.boyo.system.service.ISysRoleService;
import com.boyo.system.service.ISysUserService;
import org.apache.commons.lang3.StringUtils;
import org.flowable.common.engine.api.delegate.Expression;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 流程监听器（角色）
 *
 * <AUTHOR>
 * @date 2021/4/20
 */
@Component("roleTaskListener")
public class RoleTaskListener implements TaskListener, ExecutionListener {

    @Resource
    private ISysUserService userService;
    @Resource
    private ISysDeptService deptService;
    @Autowired
    private TaskService taskService;
    /**
     * 角色
     */
    private Expression role;

    @Override
    public void notify(DelegateTask delegateTask) {
        System.out.println(String.format("%s: %s, %s ", "roleTaskListener - 任务监听器:", delegateTask.getEventName(), delegateTask.getName()));

        // 创建任务时执行
        if ("create".equals(delegateTask.getEventName())) {
            //根据当前流程任务名称来设定分配任务的负责人
            if (role != null && StringUtils.isNotBlank(role.getExpressionText())) {
                SysUser initialtor = null;
                try {
                    // 取得创建人
                    String userId = delegateTask.getVariableInstances().get("initiator").getTextValue();
                    if (StringUtils.isNotBlank(userId)) {
                        initialtor = userService.selectUserById(Convert.toLong(userId));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                // 根据角色选择部门负责人
                if (initialtor != null) {
                    System.out.println(initialtor.getDeptId());
                    String roleKey = role.getExpressionText();

                    // 查询用户所属部门及所有父级部门
                    List<Long> deptIdList = new ArrayList<>();
                    SysDept dept = deptService.selectDeptById(initialtor.getDeptId());
                    if (dept != null) {
                        deptIdList.add(dept.getDeptId());
                        Long[] deptIds = Convert.toLongArray(dept.getAncestors());
                        deptIdList.addAll(CollectionUtil.reverse(Arrays.asList(deptIds)));
                    }
                    // 根据部门和角色查询用户列表
                    SysUser user = new SysUser();
                    user.setDeptIdList(deptIdList);
                    user.setRoleKey(roleKey);
                    List<SysUser> userList = userService.selectUserByRoleAndDeptId(user);
                    if (userList != null && !userList.isEmpty()) {
                        List<SysUser> subUserList = null;
                        // 按照部门层级筛选
                        for (Long aLong : deptIdList) {
                            subUserList = userList.stream().filter(user1 -> ObjectUtil.equals(user1.getDeptId(), aLong)).collect(Collectors.toList());
                            if (!subUserList.isEmpty()) {
                                break;
                            }
                        }
                        if (subUserList != null && !subUserList.isEmpty()) {
                            delegateTask.setVariable("customAssignee", subUserList.get(0).getUserId().toString());
                            // 在notify方法中不要使用 `delegateTask.setAssignee(String assignee)` 来设置，因为历史表中的 assignee 还是 null
                            taskService.setAssignee(delegateTask.getId(), subUserList.get(0).getUserId().toString());
                        }
                    }
                }
            }
        } else if ("delete".equals(delegateTask.getEventName())) {
            String overFlag = Convert.toStr(delegateTask.getVariable("overFlag"));
            if ("1".equals(overFlag)) {
                System.out.println(">>>>>>>>>>>>>>> 拒绝");
            } else {
                System.out.println(">>>>>>>>>>>>>>> 退回");
            }
        }
    }

    @Override
    public void notify(DelegateExecution delegateExecution) {
        System.out.println(delegateExecution.getEventName());
    }
}

package com.boyo.wms.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.boyo.common.core.domain.BaseEntity;


/**
 * 领料单详情物料对象 t_material_requisition_details_code
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_material_requisition_details_code")
public class TMaterialRequisitionDetailsCode extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 
     */
    private Long tenantId;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 领料单详情id
     */
    private Long materialRequisitionDetailsId;
    /**
     * 物料的具体成品编码
     */
    private String materialProductCode;

}

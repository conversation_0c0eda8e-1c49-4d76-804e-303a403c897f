package com.boyo.order.domain.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;
import com.boyo.common.core.domain.BaseEntity;
import com.boyo.common.core.validate.AddGroup;
import com.boyo.common.core.validate.EditGroup;

import java.util.List;

/**
 * 工单工序子任务业务对象 t_produce_order_process_subtask
 *
 * <AUTHOR>
 * @date 2025-06-07
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TProduceOrderProcessSubtaskBo extends BaseEntity {

    /**
     * 
     */
    //@NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 设备派工id
     */
    //@NotNull(message = "设备派工id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderEquipmentId;

    /**
     * 工单工序id
     */
    //@NotNull(message = "工单工序id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long produceOrderProcessId;

    /**
     * 报工人，为空时不限制
     */
    //@NotBlank(message = "报工人，为空时不限制不能为空", groups = { AddGroup.class, EditGroup.class })
    private String processUsers;

    /**
     * 工序状态
     */
    //@NotBlank(message = "工序状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String processStatus;

    /**
     * 编码
     */
    //@NotBlank(message = "编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String rollCode;

    /**
     * 所属企业
     */
    //@NotNull(message = "所属企业不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long tenantId;

    /**
     * 设备台账id
     */
    private Long equipmentLedgerId;

    /**
     * 设备台账idList
     */
    private List<Long> equipmentLedgerIdList;

    /**
     * 工单号
     */
    private String orderCode;

    /**
     * 工序名称
     */
    private String processName;


}

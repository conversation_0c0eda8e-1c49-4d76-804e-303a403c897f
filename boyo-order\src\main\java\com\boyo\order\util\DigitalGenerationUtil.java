package com.boyo.order.util;

import cn.hutool.core.util.ObjectUtil;

public class DigitalGenerationUtil {

    public static String getNum(String code) {
        String roundCode = "0001";
        if (ObjectUtil.isNotNull(code)) {
            int intCode = Integer.parseInt(code) + 1;
            if (intCode < 9999) {
                roundCode = String.format("%04d", intCode);
            } else {
                throw new RuntimeException("轮次编号达到最大");
            }
        }
        return roundCode;
    }

}

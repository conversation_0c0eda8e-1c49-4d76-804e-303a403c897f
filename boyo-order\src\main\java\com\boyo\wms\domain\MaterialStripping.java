package com.boyo.wms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 领料单主表
 * @TableName t_material_stripping
 */
@TableName(value ="t_material_stripping")
@Data
public class MaterialStripping implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     *
     */
    @TableField(value = "tenant_id")
    private Long tenantId;

    /**
     * 退料单编号（关联用）
     */
    @TableField(value = "requisition_id")
    private String requisitionId;

    /**
     * 退料部门
     */
    @TableField(value = "department")
    private String department;

    /**
     * 退料部门id
     */
    @TableField(value = "department_id")
    private String departmentId;

    /**
     * 退料日期
     */
    @TableField(value = "requisition_date")
    private Date requisitionDate;

    /**
     * 源单类型
     */
    @TableField(value = "yuandan_type")
    private String yuandanType;

    /**
     * 选单号
     */
    @TableField(value = "xuandan_no")
    private String xuandanNo;

    /**
     * 产品
     */
    @TableField(value = "product")
    private String product;

    /**
     * 单位
     */
    @TableField(value = "product_unit")
    private String productUnit;

    /**
     * 投入产量
     */
    @TableField(value = "input_output")
    private String inputOutput;

    /**
     * 基本单位
     */
    @TableField(value = "basic_unit")
    private String basicUnit;

    /**
     * 基本单位投入产量
     */
    @TableField(value = "basic_unit_input")
    private String basicUnitInput;

    /**
     * 生产批号
     */
    @TableField(value = "production_batch")
    private String productionBatch;

    /**
     * 客户料号
     */
    @TableField(value = "customer_part_number")
    private String customerPartNumber;

    /**
     * 客户商品名
     */
    @TableField(value = "customer_product_name")
    private String customerProductName;

    /**
     * 制单
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 摘要
     */
    @TableField(value = "summary")
    private String summary;

    /**
     * 发料
     */
    @TableField(value = "issued_by")
    private String issuedBy;

    /**
     * 审核
     */
    @TableField(value = "reviewed_by")
    private String reviewedBy;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 审核日期
     */
    @TableField(value = "review_date")
    private Date reviewDate;

    /**
     * 记账
     */
    @TableField(value = "jizhang")
    private String jizhang;

    /**
     * 领料
     */
    @TableField(value = "picker")
    private String picker;

    /**
     *
     */
    @TableField(value = "ex1")
    private String ex1;

    /**
     *
     */
    @TableField(value = "ex2")
    private String ex2;

    /**
     * 提交状态
     */
    @TableField(value = "submit_status")
    private Integer submitStatus;

    /**
     * 提交日期
     */
    @TableField(value = "submit_date")
    private Date submitDate;

    /**
     * 领料用途
     */
    @TableField(value = "purpose")
    private String purpose;

    /**
     * 领料编号
     */
    @TableField(value = "requisition_number")
    private String requisitionNumber;

    /**
     * 设备名称
     */
    @TableField(value = "equipment_name")
    private String equipmentName;

    /**
     * 设备id
     */
    @TableField(value = "equipment_id")
    private Long equipmentId;

    /**
     * 任务单号
     */
    @TableField(value = "roll_code")
    private String rollCode;

    /**
     * 工序名称
     */
    @TableField(value = "process_name")
    private String processName;
    /**
     * 任务id
     */
    @TableField(value = "task_id")
    private Long taskId;

    @TableField(exist = false)
    private List<MaterialStrippingDetails> details;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

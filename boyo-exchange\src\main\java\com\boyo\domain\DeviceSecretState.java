package com.boyo.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 保密设备状态
 * @TableName t_device_secret_state
 */
@TableName(value ="t_device_secret_state")
@Data
public class DeviceSecretState implements Serializable {

    @TableId(value = "id")
    private Long id;
    /**
     *
     */
    @JsonProperty("machineID")
    @TableField(value = "machine_id")
    private String machineId;

    /**
     * 设备状态
     */
    @TableField(value = "status_code")
    private String statusCode;

    /**
     * 报错信息
     */
    @TableField(value = "err_msg")
    private String errMsg;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

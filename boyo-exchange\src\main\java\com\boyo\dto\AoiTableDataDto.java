package com.boyo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * AOI前端表格数据DTO
 * 包含嵌套的NG前三对象结构
 */
@Data
public class AoiTableDataDto {
    
    // 基础信息
    private Long id;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date reportDate;
    
    private String machineSn;
    private String projectName;
    private String productionTimeStatus;
    
    // 生产统计
    private Integer inputCount;
    private Integer rollInput;
    private Integer okCount;
    private Integer ngCount;
    private String controlledYield;
    private String rollYield;
    private String yieldById;
    private String machineDetectionRate;
    private String abnormalInfo;

    /**
     * 累计平均良率(%)
     */
    private String avgYield;
    
    // NG前五对象（对应前端的RankTop）
    private NgTopDefect rankTop1;
    private NgTopDefect rankTop2;
    private NgTopDefect rankTop3;
    private NgTopDefect rankTop4;
    private NgTopDefect rankTop5;
    
    // 各种缺陷统计对象
    private DefectDetail webBlemish;      // 网疤
    private DefectDetail pillar;          // 柱道
    private DefectDetail hole;            // 破洞
    private DefectDetail doubleLine;      // 双线
    private DefectDetail knot;            // 打结
    private DefectDetail oxidation;       // 氧化
    private DefectDetail oilStain;        // 油污
    private DefectDetail foreignObject;   // 异物
    private DefectDetail deformation;     // 变形
    private DefectDetail crack;           // 裂口
    private DefectDetail discoloration;   // 异色
    private DefectDetail hairiness;       // 毛丝
    private DefectDetail connectorlug;    // 连接片
    private DefectDetail mesh;            // 目数
    private DefectDetail thickness;       // 厚度
    private DefectDetail markdot;         // 标记点

    // ===== 计算字段 =====

    /**
     * 缺陷总和
     */
    private Integer defectSum;

    /**
     * NG数量大于120的记录数
     */
    private Integer rollNgOver120;

    /**
     * NG数量在50-120之间的记录数
     */
    private Integer rollNgBetween50And120;

    /**
     * 分组良率参考值
     */
    private String rollYieldWithGroup;

    /**
     * NG前三缺陷对象
     */
    @Data
    public static class NgTopDefect {
        private String defectName;        // 缺陷名称
        private Integer defectCount;      // 缺陷数量
        private String defectRatio;       // 缺陷比例
        
        public NgTopDefect() {}
        
        public NgTopDefect(String defectName, Integer defectCount, String defectRatio) {
            this.defectName = defectName;
            this.defectCount = defectCount;
            this.defectRatio = defectRatio;
        }
    }
    
    /**
     * 缺陷详情对象
     */
    @Data
    public static class DefectDetail {
        private Integer count;            // 数量
        private String rate;              // 比例
        
        public DefectDetail() {}
        
        public DefectDetail(Integer count, String rate) {
            this.count = count;
            this.rate = rate;
        }
    }
}

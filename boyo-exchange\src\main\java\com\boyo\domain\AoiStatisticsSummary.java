package com.boyo.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * AOI统计汇总表
 * @TableName t_aoi_statistics_summary
 * <AUTHOR>
 * @date 2024-01-15
 */
@TableName(value = "t_aoi_statistics_summary")
@Data
public class AoiStatisticsSummary implements Serializable {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联核心日报表ID
     */
    @TableField(value = "report_core_id")
    private Long reportCoreId;

    // ===== Excel表格的统计汇总字段 =====
    
    /**
     * 总批次数
     */
    @TableField(value = "total_batches")
    private Integer totalBatches;

    /**
     * 平均投入量
     */
    @TableField(value = "avg_input")
    private BigDecimal avgInput;

    /**
     * 平均OK数量
     */
    @TableField(value = "avg_ok")
    private BigDecimal avgOk;

    /**
     * 平均NG数量
     */
    @TableField(value = "avg_ng")
    private BigDecimal avgNg;

    /**
     * 平均良率(%)
     */
    @TableField(value = "avg_yield")
    private BigDecimal avgYield;

    /**
     * 总投入量
     */
    @TableField(value = "total_input")
    private Integer totalInput;

    /**
     * 总OK数量
     */
    @TableField(value = "total_ok")
    private Integer totalOk;

    /**
     * 总NG数量
     */
    @TableField(value = "total_ng")
    private Integer totalNg;

    // ===== 系统字段 =====

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 删除标志(0:正常,1:删除)
     */
    @TableField(value = "del_flag")
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

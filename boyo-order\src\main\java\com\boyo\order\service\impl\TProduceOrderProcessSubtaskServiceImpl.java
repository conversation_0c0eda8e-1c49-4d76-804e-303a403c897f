package com.boyo.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boyo.common.exception.ServiceException;
import com.boyo.common.helper.LoginHelper;
import com.boyo.order.domain.*;
import com.boyo.order.domain.vo.BomVo;
import com.boyo.order.domain.vo.ProduceOrderVo;
import com.boyo.order.mapper.*;
import com.boyo.order.service.IBomService;
import com.boyo.system.domain.TenantUser;
import com.boyo.system.mapper.SysUserMapper;
import com.boyo.wms.domain.WmsFlow;
import com.boyo.wms.domain.WmsStock;
import com.boyo.wms.mapper.TMaterialRequisitionDetailsMapper;
import com.boyo.wms.mapper.WmsFlowMapper;
import com.boyo.wms.mapper.WmsStockMapper;
import org.apache.commons.lang3.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.boyo.order.domain.bo.TProduceOrderProcessSubtaskBo;
import com.boyo.order.domain.vo.TProduceOrderProcessSubtaskVo;
import com.boyo.order.service.ITProduceOrderProcessSubtaskService;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 工单工序子任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-07
 */
@RequiredArgsConstructor
@Service
public class TProduceOrderProcessSubtaskServiceImpl implements ITProduceOrderProcessSubtaskService {

    private final TProduceOrderProcessSubtaskMapper baseMapper;
    private final TEquipmentShiftMapper equipmentShiftMapper;
    private final ProduceOrderProcessMapper produceOrderProcessMapper;
    private final ProduceOrderMapper orderMapper;
    private final SysUserMapper userMapper;
    private final BomMapper bomMapper;
    private final BomDetailMapper bomDetailMapper;
    private final BaseMaterialMapper baseMaterialMapper;
    private final WmsFlowMapper wmsFlowMapper;
    private final IBomService iBomService;
    private final TMaterialRequisitionDetailsMapper materialRequisitionDetailsMapper;
    private final WmsStockMapper wmsStockMapper;
    private final BaseUnitMapper baseUnitMapper;

    /**
     * 查询工单工序子任务
     */
    @Override
    public TProduceOrderProcessSubtaskVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询工单工序子任务列表
     */
    @Override
    public TableDataInfo<TProduceOrderProcessSubtaskVo> queryPageList(TProduceOrderProcessSubtaskBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TProduceOrderProcessSubtask> lqw = buildQueryWrapper(bo);
        Page<TProduceOrderProcessSubtaskVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询工单工序子任务列表
     */
    @Override
    public List<TProduceOrderProcessSubtaskVo> queryList(TProduceOrderProcessSubtaskBo bo) {
        LambdaQueryWrapper<TProduceOrderProcessSubtask> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TProduceOrderProcessSubtask> buildQueryWrapper(TProduceOrderProcessSubtaskBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TProduceOrderProcessSubtask> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOrderEquipmentId() != null, TProduceOrderProcessSubtask::getOrderEquipmentId, bo.getOrderEquipmentId());
        lqw.eq(bo.getProduceOrderProcessId() != null, TProduceOrderProcessSubtask::getProduceOrderProcessId, bo.getProduceOrderProcessId());
        lqw.eq(StringUtils.isNotBlank(bo.getProcessUsers()), TProduceOrderProcessSubtask::getProcessUsers, bo.getProcessUsers());
        lqw.eq(StringUtils.isNotBlank(bo.getProcessStatus()), TProduceOrderProcessSubtask::getProcessStatus, bo.getProcessStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getRollCode()), TProduceOrderProcessSubtask::getRollCode, bo.getRollCode());
        lqw.eq(bo.getTenantId() != null, TProduceOrderProcessSubtask::getTenantId, bo.getTenantId());
        return lqw;
    }

    /**
     * 新增工单工序子任务
     */
    @Override
    public Boolean insertByBo(TProduceOrderProcessSubtaskBo bo) {
        TProduceOrderProcessSubtask add = BeanUtil.toBean(bo, TProduceOrderProcessSubtask.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改工单工序子任务
     */
    @Override
    public Boolean updateByBo(TProduceOrderProcessSubtaskBo bo) {
        TProduceOrderProcessSubtask update = BeanUtil.toBean(bo, TProduceOrderProcessSubtask.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TProduceOrderProcessSubtask entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除工单工序子任务
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public TableDataInfo<TProduceOrderProcessSubtaskVo> selectOwnList(TProduceOrderProcessSubtaskBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TProduceOrderProcessSubtask> lqw = buildQueryWrapper(bo);
        //如果用户不是admin
        if(!LoginHelper.isAdmin()){
            //就根据当前用户所在班次关联的设备id来查询
            List<Long> equipmentIdList = equipmentShiftMapper.getEquipmentIdByShiftId(LoginHelper.getUserId());
            if (equipmentIdList.size() == 0){
                Page<TProduceOrderProcessSubtaskVo> tProduceOrderProcessSubtaskVoPage = new Page<>();
                return TableDataInfo.build(tProduceOrderProcessSubtaskVoPage);
            }
            bo.setEquipmentLedgerIdList(equipmentIdList);
        }else {
            //是admin就根据班次管理的所有设备来查询
            List<Long> equipmentIdAll = equipmentShiftMapper.getEquipmentIdAll();
            if (equipmentIdAll.size() == 0){
                Page<TProduceOrderProcessSubtaskVo> tProduceOrderProcessSubtaskVoPage = new Page<>();
                return TableDataInfo.build(tProduceOrderProcessSubtaskVoPage);
            }
            bo.setEquipmentLedgerIdList(equipmentIdAll);
        }
        Page<TProduceOrderProcessSubtaskVo> result = baseMapper.selectOwnList(pageQuery.build(), lqw,bo);
        return TableDataInfo.build(result);
    }

    @Override
    public List<BomDetail> materialRequisitionByProcessSubtask(Long id) {
        //查询与工序子任务关联的工序
        TProduceOrderProcessSubtask subtask = baseMapper.selectById(id);
        ProduceOrderProcess produceOrderProcess = produceOrderProcessMapper.selectById(subtask.getProduceOrderProcessId());
        // 查询与工序关联的物料
        ProduceOrder produceOrder = orderMapper.selectById(produceOrderProcess.getOrderId());
        ArrayList<BomDetail> detailArrayList = new ArrayList<>();

        // 遍历每个关联的物料，获取其BOM预览信息，并合并到明细列表中
        List<BomDetail> bomDetails = bomPreviewTwo(produceOrder.getId(), produceOrder.getMaterialId(),subtask.getSubtaskCount());
        if (bomDetails != null) {
            detailArrayList.addAll(bomDetails);
        }
        // 将每个BOM详情中的子项合并到一个新的列表中
        List<BomDetail> detailList = new ArrayList<>();
        for (BomDetail bomDetail : detailArrayList) {
            final Long id1 = bomDetail.getBomId();
            final BomVo bomVo = iBomService.queryById(id1);
            if (bomVo != null && bomVo.getChildren() != null) {
                bomVo.getChildren().forEach(child -> {
                    child.setPlanNum(BigDecimal.ZERO);
                    //退料要查出该工单关联的所有物料相加，再查出该工序报工的物料数量
                    //todo:暂时只返回物料，具体数量由员工填报
                    try {
//                        List<TMaterialRequisitionDetailsVo> rows = materialRequisitionDetailsMapper.queryListByXuandanNo(produceOrder.getBillCode());
//                        for (TMaterialRequisitionDetailsVo row : rows) {
//                            if (row.getMaterialId().equals(child.getMaterialId() + "")) {
//                                //审核后减去实际的审核数
//                                if (row.getIssuedQty() != null) {
//                                    child.setPlanNum(child.getPlanNum().subtract(row.getIssuedQty()));
//                                } else {//若未审核，减去申请数
//                                    child.setPlanNum(child.getPlanNum().subtract(row.getAppliedQty()));
//                                }
//                            }
//                        }
                        //查询物料仓库库存,同种物料个数相加即为总数量（一物一码）
                        QueryWrapper<WmsStock> wmsStockQueryWrapper = new QueryWrapper<>();
                        wmsStockQueryWrapper.eq("material_id", child.getMaterialId());
                        Long count = wmsStockMapper.selectCount(wmsStockQueryWrapper);
                        if (ObjectUtil.isNotNull(count)){
                            child.setMaterialCountWms(count);
                        }
                        BaseMaterial baseMaterial = baseMaterialMapper.selectById(child.getMaterialId());
                        if (Objects.isNull(baseMaterial.getUnitId())){

                        }else {
                            BaseUnit baseUnit = baseUnitMapper.selectById(baseMaterial.getUnitId());
                            child.setUnitName(baseUnit.getUnitName());
                        }

                    } catch (Exception e) {
                        e.printStackTrace();
                    }


                    detailList.add(child);
                });
            }
        }
        return detailList;
    }

    @Override
    public List<BomDetail> returnMaterialRequisitionByProcessSubtask(Long id) {
        //查询与工序子任务关联的工序
        TProduceOrderProcessSubtask subtask = baseMapper.selectById(id);
        ProduceOrderProcess produceOrderProcess = produceOrderProcessMapper.selectById(subtask.getProduceOrderProcessId());
        // 查询与工序关联的物料
        ProduceOrder produceOrder = orderMapper.selectById(produceOrderProcess.getOrderId());
        ArrayList<BomDetail> detailArrayList = new ArrayList<>();

        // 遍历每个关联的物料，获取其BOM预览信息，并合并到明细列表中
        List<BomDetail> bomDetails = bomPreviewTwo(produceOrder.getId(), produceOrder.getMaterialId(),subtask.getSubtaskCount());
        if (bomDetails != null) {
            detailArrayList.addAll(bomDetails);
        }
        // 将每个BOM详情中的子项合并到一个新的列表中
        List<BomDetail> detailList = new ArrayList<>();
        for (BomDetail bomDetail : detailArrayList) {
            final Long id1 = bomDetail.getBomId();
            final BomVo bomVo = iBomService.queryById(id1);
            if (bomVo != null && bomVo.getChildren() != null) {
                bomVo.getChildren().forEach(child -> {
                    child.setPlanNum(BigDecimal.ZERO);
                    //退料要查出该工单关联的所有物料相加，再查出该工序报工的物料数量
                    //todo:暂时只返回物料，具体数量由员工填报
                    try {
//                        List<TMaterialRequisitionDetailsVo> rows = materialRequisitionDetailsMapper.queryListByXuandanNo(produceOrder.getBillCode());
//                        for (TMaterialRequisitionDetailsVo row : rows) {
//                            if (row.getMaterialId().equals(child.getMaterialId() + "")) {
//                                //审核后减去实际的审核数
//                                if (row.getIssuedQty() != null) {
//                                    child.setPlanNum(child.getPlanNum().subtract(row.getIssuedQty()));
//                                } else {//若未审核，减去申请数
//                                    child.setPlanNum(child.getPlanNum().subtract(row.getAppliedQty()));
//                                }
//                            }
//                        }
                        BaseMaterial baseMaterial = baseMaterialMapper.selectById(child.getMaterialId());
                        if (Objects.isNull(baseMaterial.getUnitId())){

                        }else {
                            BaseUnit baseUnit = baseUnitMapper.selectById(baseMaterial.getUnitId());
                            child.setUnitName(baseUnit.getUnitName());
                        }

                    } catch (Exception e) {
                        e.printStackTrace();
                    }


                    detailList.add(child);
                });
            }
        }
        return detailList;
    }

    @Override
    @Transactional
    public Boolean completeOrderProcessSubtask(Long id) {
        //完工按钮，
        //当最后一道工序点击完工，就修改工单，销售订单状态为已完工
        //同时更改下一道工序的任务状态为已下发
        //先查出当前所属工序
        TProduceOrderProcessSubtask subtask = baseMapper.selectById(id);
        ProduceOrderProcess produceOrderProcess = produceOrderProcessMapper.selectById(subtask.getProduceOrderProcessId());
        //再查询后置工序
        QueryWrapper<ProduceOrderProcess> produceOrderProcessQueryWrapper = new QueryWrapper<>();
        produceOrderProcessQueryWrapper.eq("order_id",produceOrderProcess.getOrderId())
            .eq("process_sort",produceOrderProcess.getProcessSort()+1);
        ProduceOrderProcess produceOrderProcessAfter = produceOrderProcessMapper.selectOne(produceOrderProcessQueryWrapper);
        if (ObjectUtil.isNotNull(produceOrderProcessAfter)){
            if (produceOrderProcessAfter.getProcessStatus().equals("01")){
                produceOrderProcessAfter.setProcessStatus("02");
                produceOrderProcessMapper.updateById(produceOrderProcessAfter);
            }
            //再查询后置工序的子任务中处于新建状态的
            QueryWrapper<TProduceOrderProcessSubtask> tProduceOrderProcessSubtaskQueryWrapper = new QueryWrapper<>();
            tProduceOrderProcessSubtaskQueryWrapper.eq("produce_order_process_id",produceOrderProcessAfter.getId())
                .eq("process_status","01");
            List<TProduceOrderProcessSubtask> tProduceOrderProcessSubtaskList = baseMapper.selectList(tProduceOrderProcessSubtaskQueryWrapper);
            if (ObjectUtil.isNotNull(tProduceOrderProcessSubtaskList) && tProduceOrderProcessSubtaskList.size()>0){
                //随机开启第一条数据为已下发
                tProduceOrderProcessSubtaskList.get(0).setProcessStatus("02");
                baseMapper.updateById(tProduceOrderProcessSubtaskList.get(0));
            }
        }
        //判断当前工序的子任务是否全部是已完工状态，
        QueryWrapper<TProduceOrderProcessSubtask> tProduceOrderProcessSubtaskQueryWrapper = new QueryWrapper<>();
        tProduceOrderProcessSubtaskQueryWrapper.eq("produce_order_process_id",subtask.getProduceOrderProcessId());
        List<TProduceOrderProcessSubtask> tProduceOrderProcessSubtaskList = baseMapper.selectList(tProduceOrderProcessSubtaskQueryWrapper);
        if (ObjectUtil.isNotNull(tProduceOrderProcessSubtaskList) && tProduceOrderProcessSubtaskList.size()>0){
            boolean b = tProduceOrderProcessSubtaskList.stream().allMatch(TProduceOrderProcessSubtask::isTrue);
            if (b){
                //是就修改工单工序状态为已完工
                produceOrderProcess.setProcessStatus("04");
                produceOrderProcessMapper.updateById(produceOrderProcess);
            }
        }
        //当最后一道工序点击完工，就修改工单状态为已完工
        QueryWrapper<ProduceOrderProcess> produceOrderProcessQueryWrapper1 = new QueryWrapper<>();
        produceOrderProcessQueryWrapper1.eq("order_id",produceOrderProcess.getOrderId())
            .orderBy(true,false,"process_status");
        List<ProduceOrderProcess> produceOrderProcessList = produceOrderProcessMapper.selectList(produceOrderProcessQueryWrapper1);
        if (ObjectUtil.isNotNull(produceOrderProcessList) && produceOrderProcessList.size()>0){
            if (produceOrderProcess.getProcessSort().equals(produceOrderProcessList.get(0).getProcessSort())){
                ProduceOrder produceOrder = orderMapper.selectById(produceOrderProcess.getOrderId());
                produceOrder.setOrderStatus("04");
                orderMapper.updateById(produceOrder);
            }
        }
        return true;
    }


    public List<BomDetail> bomPreviewTwo(Long orderId, Long materialId,Long subtaskCount) {
        //查询生产订单中的物料id、计划数量
        ProduceOrderVo produceOrderVo = orderMapper.selectVoById(orderId);
        if (produceOrderVo.getIsFirst() == 1) {
            //订单为第一级仍用原来的逻辑
            //根据物料id，查询此物料的bom
            QueryWrapper<Bom> bomQueryWrapper = new QueryWrapper<>();
            bomQueryWrapper.eq("material_id", produceOrderVo.getMaterialId());
            bomQueryWrapper.eq("bom_status", "0");//状态为开启的bom
            List<BomVo> bomVos = bomMapper.selectVoList(bomQueryWrapper);
            if (bomVos != null && bomVos.size() > 0) {
                if (bomVos.size() > 1) {
                    throw new ServiceException("此订单的" + bomVos.get(0).getMaterialName() + "(" + bomVos.get(0).getMaterialCode() + ")" + "物料Bom不唯一，请关闭其他版本的Bom");
                }
                // 根据bom查询bom详情
                List<BomDetail> bomDetails = bomDetailMapper.queryBomMaterialListByBomAndMaterial(bomVos.get(0).getId(), null);
                if (bomDetails != null && bomDetails.size() > 0) {
                    recursion(produceOrderVo, bomDetails, new BigDecimal(1),subtaskCount);
                }
                return bomDetails;
            } else {
                QueryWrapper<BaseMaterial> baseMaterialQueryWrapper = new QueryWrapper<>();
                baseMaterialQueryWrapper.eq("id", produceOrderVo.getMaterialId());
                BaseMaterial baseMaterial = baseMaterialMapper.selectOne(baseMaterialQueryWrapper);
                throw new ServiceException("此订单的" + baseMaterial.getMaterialName() + "(" + baseMaterial.getMaterialCode() + ")" + "物料没有创建BOM，请先在基础数据中创建");
            }
        } else {
            QueryWrapper<Bom> bomQueryWrapper = new QueryWrapper<>();
            bomQueryWrapper.eq("material_id", materialId);
            bomQueryWrapper.eq("bom_status", "0");//状态为开启的bom
            List<BomVo> bomVos = bomMapper.selectVoList(bomQueryWrapper);
            if (bomVos != null && bomVos.size() > 0) {
                if (bomVos.size() > 1) {
                    throw new ServiceException("此订单的" + bomVos.get(0).getMaterialName() + "(" + bomVos.get(0).getMaterialCode() + ")" + "物料Bom不唯一，请关闭其他版本的Bom");
                }
                //根据当前物料id和父bom_id查询出当前的bom详情
                List<BomDetail> bomDetails1 = bomDetailMapper.queryBomMaterialListByBomAndMaterial(bomVos.get(0).getId(), null);
                recursion(produceOrderVo, bomDetails1, new BigDecimal(1),subtaskCount);
                return bomDetails1;
            } else {
                QueryWrapper<BaseMaterial> baseMaterialQueryWrapper = new QueryWrapper<>();
                baseMaterialQueryWrapper.eq("id", produceOrderVo.getMaterialId());
                BaseMaterial baseMaterial = baseMaterialMapper.selectOne(baseMaterialQueryWrapper);
                throw new ServiceException("此订单的" + baseMaterial.getMaterialName() + "(" + baseMaterial.getMaterialCode() + ")" + "物料没有创建BOM，请先在基础数据中创建");
            }
        }

    }

    private void recursion(ProduceOrderVo produceOrderVo, List<BomDetail> bomDetails, BigDecimal pCount,Long subtaskCount) {
        for (BomDetail bomDetail : bomDetails) {
            List<BomDetail> bomDetails1 = bomDetailMapper.queryBomMaterialListByBomAndPid(bomDetail.getBomId(), bomDetail.getId());
            if (bomDetails1 != null && bomDetails1.size() > 0) {
                recursion(produceOrderVo, bomDetails1, bomDetail.getMaterialCount(),subtaskCount);
                bomDetail.setChildren(bomDetails1);
            }
            //计划用料数
            if (ObjectUtil.isNotNull(bomDetail.getMaterialCount())) {
                BigDecimal bigDecimal = new BigDecimal(subtaskCount);
                BigDecimal tempCount = bigDecimal.multiply(bomDetail.getMaterialCount());
                bomDetail.setPlanNum(tempCount.multiply(pCount));
            } else {
                bomDetail.setPlanNum(new BigDecimal(subtaskCount));
            }
            //实际用料数
            QueryWrapper<WmsFlow> wmsFlowQueryWrapper = new QueryWrapper<>();
            wmsFlowQueryWrapper.eq("out_bound_order_id", produceOrderVo.getId());
            wmsFlowQueryWrapper.eq("flow_way", "out02");
            wmsFlowQueryWrapper.eq("examine_status", "1");
            wmsFlowQueryWrapper.eq("material_id", bomDetail.getMaterialId());
            List<WmsFlow> wmsFlows = wmsFlowMapper.selectList(wmsFlowQueryWrapper);
            if (wmsFlows != null && wmsFlows.size() > 0) {
                BigDecimal realNum = new BigDecimal(0);
                for (WmsFlow wmsFlow : wmsFlows) {
                    realNum = realNum.add(wmsFlow.getMaterialCount());
                }
                bomDetail.setRealNum(realNum);
            }
        }
        return;
    }



}

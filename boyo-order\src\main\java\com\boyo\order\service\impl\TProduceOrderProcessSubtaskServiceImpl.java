package com.boyo.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boyo.common.exception.ServiceException;
import com.boyo.common.helper.LoginHelper;
import com.boyo.common.storage.TenantStorage;
import com.boyo.common.utils.redis.RedisUtils;
import com.boyo.order.domain.*;
import com.boyo.order.domain.bo.ProduceOrderReportBo;
import com.boyo.order.domain.vo.BomVo;
import com.boyo.order.domain.vo.ProduceOrderVo;
import com.boyo.order.enums.MiddleMaterialStateType;
import com.boyo.order.mapper.*;
import com.boyo.order.service.IBomService;
import com.boyo.order.util.DigitalGenerationUtil;
import com.boyo.process.domain.MiddleMaterial;
import com.boyo.process.mapper.MiddleMaterialMapper;
import com.boyo.process.utils.SourceCodeUtils;
import com.boyo.system.domain.TenantUser;
import com.boyo.system.mapper.SysUserMapper;
import com.boyo.wms.domain.WmsFlow;
import com.boyo.wms.domain.WmsStock;
import com.boyo.wms.domain.bo.TStorageApplyReceiptBo;
import com.boyo.wms.domain.bo.TStorageApplyReceiptDetailBo;
import com.boyo.wms.domain.bo.WmsFlowBo;
import com.boyo.wms.mapper.TMaterialRequisitionDetailsMapper;
import com.boyo.wms.mapper.WmsFlowMapper;
import com.boyo.wms.mapper.WmsStockMapper;
import com.boyo.wms.service.ITStorageApplyReceiptDetailService;
import com.boyo.wms.service.ITStorageApplyReceiptService;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.boyo.order.domain.bo.TProduceOrderProcessSubtaskBo;
import com.boyo.order.domain.vo.TProduceOrderProcessSubtaskVo;
import com.boyo.order.service.ITProduceOrderProcessSubtaskService;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * 工单工序子任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-07
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class TProduceOrderProcessSubtaskServiceImpl implements ITProduceOrderProcessSubtaskService {

    private final TProduceOrderProcessSubtaskMapper baseMapper;
    private final TEquipmentShiftMapper equipmentShiftMapper;
    private final ProduceOrderProcessMapper produceOrderProcessMapper;
    private final ProduceOrderMapper orderMapper;
    private final SysUserMapper userMapper;
    private final BomMapper bomMapper;
    private final BomDetailMapper bomDetailMapper;
    private final BaseMaterialMapper baseMaterialMapper;
    private final WmsFlowMapper wmsFlowMapper;
    private final IBomService iBomService;
    private final TMaterialRequisitionDetailsMapper materialRequisitionDetailsMapper;
    private final WmsStockMapper wmsStockMapper;
    private final BaseUnitMapper baseUnitMapper;
    private final IdentifierGenerator generator;
    private final BaseProcessMapper baseProcessMapper;
    private final MiddleMaterialMapper middleMaterialMapper;
    private final TOrderEquipmentMapper orderEquipmentMapper;
    private final EquipmentLedgerMapper equipmentLedgerMapper;
    private final ITStorageApplyReceiptService iTStorageApplyReceiptService;
    private final ITStorageApplyReceiptDetailService iDetailService;

    /**
     * 查询工单工序子任务
     */
    @Override
    public TProduceOrderProcessSubtaskVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询工单工序子任务列表
     */
    @Override
    public TableDataInfo<TProduceOrderProcessSubtaskVo> queryPageList(TProduceOrderProcessSubtaskBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TProduceOrderProcessSubtask> lqw = buildQueryWrapper(bo);
        Page<TProduceOrderProcessSubtaskVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询工单工序子任务列表
     */
    @Override
    public List<TProduceOrderProcessSubtaskVo> queryList(TProduceOrderProcessSubtaskBo bo) {
        LambdaQueryWrapper<TProduceOrderProcessSubtask> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TProduceOrderProcessSubtask> buildQueryWrapper(TProduceOrderProcessSubtaskBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TProduceOrderProcessSubtask> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOrderEquipmentId() != null, TProduceOrderProcessSubtask::getOrderEquipmentId, bo.getOrderEquipmentId());
        lqw.eq(bo.getProduceOrderProcessId() != null, TProduceOrderProcessSubtask::getProduceOrderProcessId, bo.getProduceOrderProcessId());
        lqw.eq(StringUtils.isNotBlank(bo.getProcessUsers()), TProduceOrderProcessSubtask::getProcessUsers, bo.getProcessUsers());
        lqw.eq(StringUtils.isNotBlank(bo.getProcessStatus()), TProduceOrderProcessSubtask::getProcessStatus, bo.getProcessStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getRollCode()), TProduceOrderProcessSubtask::getRollCode, bo.getRollCode());
        lqw.eq(bo.getTenantId() != null, TProduceOrderProcessSubtask::getTenantId, bo.getTenantId());
        return lqw;
    }

    /**
     * 新增工单工序子任务
     */
    @Override
    public Boolean insertByBo(TProduceOrderProcessSubtaskBo bo) {
        TProduceOrderProcessSubtask add = BeanUtil.toBean(bo, TProduceOrderProcessSubtask.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改工单工序子任务
     */
    @Override
    public Boolean updateByBo(TProduceOrderProcessSubtaskBo bo) {
        TProduceOrderProcessSubtask update = BeanUtil.toBean(bo, TProduceOrderProcessSubtask.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TProduceOrderProcessSubtask entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除工单工序子任务
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public TableDataInfo<TProduceOrderProcessSubtaskVo> selectOwnList(TProduceOrderProcessSubtaskBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TProduceOrderProcessSubtask> lqw = buildQueryWrapper(bo);
        //如果用户不是admin
        if(!LoginHelper.isAdmin()){
            //就根据当前用户所在班次关联的设备id来查询
            List<Long> equipmentIdList = equipmentShiftMapper.getEquipmentIdByShiftId(LoginHelper.getUserId());
            if (equipmentIdList.size() == 0){
                Page<TProduceOrderProcessSubtaskVo> tProduceOrderProcessSubtaskVoPage = new Page<>();
                return TableDataInfo.build(tProduceOrderProcessSubtaskVoPage);
            }
            bo.setEquipmentLedgerIdList(equipmentIdList);
        }else {
            //是admin就根据所有设备来查询
//            List<Long> equipmentIdAll = equipmentShiftMapper.getEquipmentIdAll();
//            if (equipmentIdAll.size() == 0){
//                Page<TProduceOrderProcessSubtaskVo> tProduceOrderProcessSubtaskVoPage = new Page<>();
//                return TableDataInfo.build(tProduceOrderProcessSubtaskVoPage);
//            }
//            bo.setEquipmentLedgerIdList(equipmentIdAll);
        }
        Page<TProduceOrderProcessSubtaskVo> result = baseMapper.selectOwnList(pageQuery.build(), lqw,bo);
        return TableDataInfo.build(result);
    }

    @Override
    public List<BomDetail> materialRequisitionByProcessSubtask(Long id) {
        //查询与工序子任务关联的工序
        TProduceOrderProcessSubtask subtask = baseMapper.selectById(id);
        ProduceOrderProcess produceOrderProcess = produceOrderProcessMapper.selectById(subtask.getProduceOrderProcessId());
        // 查询与工序关联的物料
        ProduceOrder produceOrder = orderMapper.selectById(produceOrderProcess.getOrderId());
        ArrayList<BomDetail> detailArrayList = new ArrayList<>();

        // 遍历每个关联的物料，获取其BOM预览信息，并合并到明细列表中
        List<BomDetail> bomDetails = bomPreviewTwo(produceOrder.getId(), produceOrder.getMaterialId(),subtask.getSubtaskCount());
        if (bomDetails != null) {
            detailArrayList.addAll(bomDetails);
        }
        // 将每个BOM详情中的子项合并到一个新的列表中
        List<BomDetail> detailList = new ArrayList<>();
        for (BomDetail bomDetail : detailArrayList) {
            final Long id1 = bomDetail.getBomId();
            final BomVo bomVo = iBomService.queryById(id1);
            if (bomVo != null && bomVo.getChildren() != null) {
                bomVo.getChildren().forEach(child -> {
                    child.setPlanNum(BigDecimal.ZERO);
                    //退料要查出该工单关联的所有物料相加，再查出该工序报工的物料数量
                    //todo:暂时只返回物料，具体数量由员工填报
                    try {
//                        List<TMaterialRequisitionDetailsVo> rows = materialRequisitionDetailsMapper.queryListByXuandanNo(produceOrder.getBillCode());
//                        for (TMaterialRequisitionDetailsVo row : rows) {
//                            if (row.getMaterialId().equals(child.getMaterialId() + "")) {
//                                //审核后减去实际的审核数
//                                if (row.getIssuedQty() != null) {
//                                    child.setPlanNum(child.getPlanNum().subtract(row.getIssuedQty()));
//                                } else {//若未审核，减去申请数
//                                    child.setPlanNum(child.getPlanNum().subtract(row.getAppliedQty()));
//                                }
//                            }
//                        }
                        //查询物料仓库库存,同种物料个数相加即为总数量（一物一码）
                        QueryWrapper<WmsStock> wmsStockQueryWrapper = new QueryWrapper<>();
                        wmsStockQueryWrapper.eq("material_id", child.getMaterialId());
                        Long count = wmsStockMapper.selectCount(wmsStockQueryWrapper);
                        if (ObjectUtil.isNotNull(count)){
                            child.setMaterialCountWms(count);
                        }
                        BaseMaterial baseMaterial = baseMaterialMapper.selectById(child.getMaterialId());
                        if (Objects.isNull(baseMaterial.getUnitId())){

                        }else {
                            BaseUnit baseUnit = baseUnitMapper.selectById(baseMaterial.getUnitId());
                            child.setUnitName(baseUnit.getUnitName());
                        }

                    } catch (Exception e) {
                        e.printStackTrace();
                    }


                    detailList.add(child);
                });
            }
        }
        return detailList;
    }

    @Override
    public List<BomDetail> returnMaterialRequisitionByProcessSubtask(Long id) {
        //查询与工序子任务关联的工序
        TProduceOrderProcessSubtask subtask = baseMapper.selectById(id);
        ProduceOrderProcess produceOrderProcess = produceOrderProcessMapper.selectById(subtask.getProduceOrderProcessId());
        // 查询与工序关联的物料
        ProduceOrder produceOrder = orderMapper.selectById(produceOrderProcess.getOrderId());
        ArrayList<BomDetail> detailArrayList = new ArrayList<>();

        // 遍历每个关联的物料，获取其BOM预览信息，并合并到明细列表中
        List<BomDetail> bomDetails = bomPreviewTwo(produceOrder.getId(), produceOrder.getMaterialId(),subtask.getSubtaskCount());
        if (bomDetails != null) {
            detailArrayList.addAll(bomDetails);
        }
        // 将每个BOM详情中的子项合并到一个新的列表中
        List<BomDetail> detailList = new ArrayList<>();
        for (BomDetail bomDetail : detailArrayList) {
            final Long id1 = bomDetail.getBomId();
            final BomVo bomVo = iBomService.queryById(id1);
            if (bomVo != null && bomVo.getChildren() != null) {
                bomVo.getChildren().forEach(child -> {
                    child.setPlanNum(BigDecimal.ZERO);
                    //退料要查出该工单关联的所有物料相加，再查出该工序报工的物料数量
                    //todo:暂时只返回物料，具体数量由员工填报
                    try {
//                        List<TMaterialRequisitionDetailsVo> rows = materialRequisitionDetailsMapper.queryListByXuandanNo(produceOrder.getBillCode());
//                        for (TMaterialRequisitionDetailsVo row : rows) {
//                            if (row.getMaterialId().equals(child.getMaterialId() + "")) {
//                                //审核后减去实际的审核数
//                                if (row.getIssuedQty() != null) {
//                                    child.setPlanNum(child.getPlanNum().subtract(row.getIssuedQty()));
//                                } else {//若未审核，减去申请数
//                                    child.setPlanNum(child.getPlanNum().subtract(row.getAppliedQty()));
//                                }
//                            }
//                        }
                        BaseMaterial baseMaterial = baseMaterialMapper.selectById(child.getMaterialId());
                        if (Objects.isNull(baseMaterial.getUnitId())){

                        }else {
                            BaseUnit baseUnit = baseUnitMapper.selectById(baseMaterial.getUnitId());
                            child.setUnitName(baseUnit.getUnitName());
                        }

                    } catch (Exception e) {
                        e.printStackTrace();
                    }


                    detailList.add(child);
                });
            }
        }
        return detailList;
    }

    @Override
    @Transactional
    public Boolean completeOrderProcessSubtask(Long id,WmsFlowBo bo) {
        //完工按钮，
        //当最后一道工序点击完工，就修改工单，销售订单状态为已完工
        //同时更改下一道工序的任务状态为已下发
        //先查出当前所属工序
        TProduceOrderProcessSubtask subtask = baseMapper.selectById(id);
        //修改当前工序状态为已完工
        subtask.setProcessStatus("04");
        baseMapper.updateById(subtask);
        //新增出入库记录，根据工序生成质检任务或者直接入库
        addWmsFlow(subtask,bo);
        ProduceOrderProcess produceOrderProcess = produceOrderProcessMapper.selectById(subtask.getProduceOrderProcessId());
        //再查询后置工序
        QueryWrapper<ProduceOrderProcess> produceOrderProcessQueryWrapper = new QueryWrapper<>();
        produceOrderProcessQueryWrapper.eq("order_id",produceOrderProcess.getOrderId())
            .eq("process_sort",produceOrderProcess.getProcessSort()+1);
        ProduceOrderProcess produceOrderProcessAfter = produceOrderProcessMapper.selectOne(produceOrderProcessQueryWrapper);
        if (ObjectUtil.isNotNull(produceOrderProcessAfter)){
            if (produceOrderProcessAfter.getProcessStatus().equals("01")){
                produceOrderProcessAfter.setProcessStatus("02");
                produceOrderProcessMapper.updateById(produceOrderProcessAfter);
            }
            //再查询后置工序的子任务中处于新建状态的
            QueryWrapper<TProduceOrderProcessSubtask> tProduceOrderProcessSubtaskQueryWrapper = new QueryWrapper<>();
            tProduceOrderProcessSubtaskQueryWrapper.eq("produce_order_process_id",produceOrderProcessAfter.getId())
                .eq("process_status","01");
            List<TProduceOrderProcessSubtask> tProduceOrderProcessSubtaskList = baseMapper.selectList(tProduceOrderProcessSubtaskQueryWrapper);
            if (ObjectUtil.isNotNull(tProduceOrderProcessSubtaskList) && tProduceOrderProcessSubtaskList.size()>0){
                //随机开启第一条数据为已下发
                tProduceOrderProcessSubtaskList.get(0).setProcessStatus("02");
                baseMapper.updateById(tProduceOrderProcessSubtaskList.get(0));
            }
        }
        //判断当前工序的子任务是否全部是已完工状态，
        QueryWrapper<TProduceOrderProcessSubtask> tProduceOrderProcessSubtaskQueryWrapper = new QueryWrapper<>();
        tProduceOrderProcessSubtaskQueryWrapper.eq("produce_order_process_id",subtask.getProduceOrderProcessId());
        List<TProduceOrderProcessSubtask> tProduceOrderProcessSubtaskList = baseMapper.selectList(tProduceOrderProcessSubtaskQueryWrapper);
        if (ObjectUtil.isNotNull(tProduceOrderProcessSubtaskList) && tProduceOrderProcessSubtaskList.size()>0){
            boolean b = tProduceOrderProcessSubtaskList.stream().allMatch(TProduceOrderProcessSubtask::isTrue);
            if (b){
                //是就修改工单工序状态为已完工
                produceOrderProcess.setProcessStatus("04");
                produceOrderProcessMapper.updateById(produceOrderProcess);
            }
        }
        //查出所有工单工序
        QueryWrapper<ProduceOrderProcess> produceOrderProcessQueryWrapper1 = new QueryWrapper<>();
        produceOrderProcessQueryWrapper1.eq("order_id",produceOrderProcess.getOrderId());
        List<ProduceOrderProcess> produceOrderProcessList = produceOrderProcessMapper.selectList(produceOrderProcessQueryWrapper1);
        if (ObjectUtil.isNotNull(produceOrderProcessList) && produceOrderProcessList.size()>0){
            boolean b = produceOrderProcessList.stream().allMatch(ProduceOrderProcess::isTrue);
            //当前工单工序全部是完工状态，就修改工单状态为已完工
            if (b){
                ProduceOrder produceOrder = orderMapper.selectById(produceOrderProcess.getOrderId());
                produceOrder.setOrderStatus("04");
                orderMapper.updateById(produceOrder);
            }
        }
        //todo:同样方法查询工单是否全为完工，如果完工就修改一级工单、销售订单为完工
        return true;
    }

    @Override
    public List<MiddleMaterial> getSourceCodeAndSnCode(String rollCode) {
        QueryWrapper<MiddleMaterial> middleMaterialQueryWrapper = new QueryWrapper<>();
        middleMaterialQueryWrapper.eq("code",rollCode)
            .isNotNull("sn_code");
        List<MiddleMaterial> middleMaterialList = middleMaterialMapper.selectList(middleMaterialQueryWrapper);
        if (ObjectUtil.isNotNull(middleMaterialList) && middleMaterialList.size()>0){
            for (MiddleMaterial middleMaterial : middleMaterialList) {
                //循环生成base64,首先生成溯源码
                QRCodeWriter qrCodeWriter = new QRCodeWriter();
                BitMatrix bitMatrix;
                try {
                    bitMatrix = qrCodeWriter.encode(middleMaterial.getSourceCode(), BarcodeFormat.QR_CODE, 120, 120);
                    bitMatrix = updateBit(bitMatrix, 0);
                    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                    MatrixToImageWriter.writeToStream(bitMatrix, "PNG", outputStream);
                    Base64.Encoder encoder = Base64.getEncoder();
                    String  sourceCodeBase64= encoder.encodeToString(outputStream.toByteArray());
                    middleMaterial.setSourceCodeBase(sourceCodeBase64);
                    outputStream.close();
                } catch (WriterException e) {
                    e.printStackTrace();
                } catch (IOException e) {
                    e.printStackTrace();
                }
                QRCodeWriter qrCodeWriterTwo = new QRCodeWriter();
                BitMatrix bitMatrixTwo;
                try {
                    bitMatrixTwo = qrCodeWriterTwo.encode(middleMaterial.getSnCode(), BarcodeFormat.QR_CODE, 120, 120);
                    bitMatrixTwo = updateBit(bitMatrixTwo, 0);
                    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                    MatrixToImageWriter.writeToStream(bitMatrixTwo, "PNG", outputStream);
                    Base64.Encoder encoder = Base64.getEncoder();
                    String  snCodeBase64= encoder.encodeToString(outputStream.toByteArray());
                    middleMaterial.setSnCodeBase(snCodeBase64);
                    outputStream.close();
                } catch (WriterException e) {
                    e.printStackTrace();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return middleMaterialList;
    }

    private static BitMatrix updateBit(BitMatrix matrix, int margin) {
        int tempM = margin * 2;
        int[] rec = matrix.getEnclosingRectangle(); // 获取二维码图案的属性
        // 感兴趣可以进入这个getEnclosingRecting()方法看一下
        // rec[0]表示 left：二维码距离矩阵左边缘的距离
        // rec[1]表示 top：二维码距离矩阵上边缘的距离
        // rect[2]表示二维码的宽
        // rect[2]表示二维码的高
        int resWidth = rec[2] + tempM;
        int resHeight = rec[3] + tempM;
        BitMatrix resMatrix = new BitMatrix(resWidth, resHeight); // 按照自定义边框生成新的BitMatrix
        resMatrix.clear();
        for (int i = margin; i < resWidth - margin; i++) { // 循环，将二维码图案绘制到新的bitMatrix中
            for (int j = margin; j < resHeight - margin; j++) {
                if (matrix.get(i - margin + rec[0], j - margin + rec[1])) {
                    resMatrix.set(i, j);
                }
            }
        }
        return resMatrix;
    }


    public void addWmsFlow(TProduceOrderProcessSubtask subtask,WmsFlowBo bo){
        ProduceOrderProcess orderProcess = produceOrderProcessMapper.selectById(subtask.getProduceOrderProcessId());
        BaseProcess baseProcess = baseProcessMapper.selectById(orderProcess.getProcessId());
        if (baseProcess.getProcessName().equals("AOI质检")){
            //先查询出工单工序
            ProduceOrderProcess produceOrderProcess1 = produceOrderProcessMapper.selectById(subtask.getProduceOrderProcessId());
            if (ObjectUtil.isNotNull(produceOrderProcess1)){
                QueryWrapper<ProduceOrder> orderQueryWrapper = new QueryWrapper<>();
                orderQueryWrapper.eq("id",produceOrderProcess1.getOrderId());
                List<ProduceOrder> produceOrderList = orderMapper.selectList(orderQueryWrapper);
                //绑定订单号
                if (null !=produceOrderList && produceOrderList.size()>0){
                    bo.setOrderCode(produceOrderList.get(0).getBillCode());
                }
            }
            QueryWrapper<MiddleMaterial> middleMaterialQueryWrapper = new QueryWrapper<>();
            middleMaterialQueryWrapper.eq("code",subtask.getRollCode())
                .isNull("sn_code");
            MiddleMaterial middleMaterial = middleMaterialMapper.selectOne(middleMaterialQueryWrapper);
            if (ObjectUtil.isNotNull(middleMaterial)){
                QueryWrapper<MiddleMaterial> middleMaterialQueryWrapperUp = new QueryWrapper<>();
                middleMaterialQueryWrapperUp.eq("c_id",middleMaterial.getId());
                List<MiddleMaterial> middleMaterialList = middleMaterialMapper.selectList(middleMaterialQueryWrapperUp);
                if (ObjectUtil.isNotNull(middleMaterialList) && middleMaterialList.size()>0){
                    //AOI质检是多卷
                    for (MiddleMaterial material : middleMaterialList) {
                        TOrderEquipment tOrderEquipment = orderEquipmentMapper.selectById(subtask.getOrderEquipmentId());
                        EquipmentLedger equipmentLedger = equipmentLedgerMapper.selectById(tOrderEquipment.getEquipmentLedgerId());
                        //BaseMaterial baseMaterial = baseMaterialMapper.selectById(wmsFlow.getMaterialId());
//                    if (ObjectUtil.isNotNull(baseMaterial)){
//
//                    }
                        //todo:目数和厚度取不出来，后期维护到物料表
                        //AOI质检不需要生成溯源码
                        MiddleMaterial middleMaterial1 = new MiddleMaterial();
                        middleMaterial1.setBaseMaterialId(material.getBaseMaterialId());
                        middleMaterial1.setCode(subtask.getRollCode());
                        middleMaterial1.setSourceCode(material.getSourceCode());
                        middleMaterial1.setCheckUser(LoginHelper.getNickName());
                        middleMaterial1.setProcess("AOI质检");
                        middleMaterial1.setTenantId(TenantStorage.getTenantId());
                        middleMaterial1.setState(MiddleMaterialStateType.THEN_INSPECTED.getState());
                        middleMaterial1.setProduceOrderCode(bo.getOrderCode());
                        middleMaterial1.setDeviceCode(equipmentLedger.getEquipmentCode());
                        middleMaterial1.setSnCode(material.getSnCode());
                        middleMaterialMapper.insert(middleMaterial1);
                    }
                }else {
                    log.info("AOI质检任务单号:{},未关联分切溯源码",subtask.getRollCode());
                }
            }
        }else {
            //如果选择入库，就根据入库数量循环创建
            for (int i = 0; i < bo.getReportCount().intValue(); i++) {
                // 1.生成审核通过后得入库记录
                long wmsId = generator.nextId(NetUtil.getLocalMacAddress()).longValue();
                //System.out.println("创建时，入库单id"+wmsId);
                WmsFlow wmsFlow = BeanUtil.toBean(bo, WmsFlow.class);
                //入库数量
                wmsFlow.setMaterialCount(new BigDecimal(1));
                wmsFlow.setExamineStatus(1);
                wmsFlow.setId(wmsId);
                wmsFlow.setFlowType("0");
                //wmsFlow.setImperfectionsCount(bo.getImperfectCount());
                //工序报工如果选择入库按钮，直接入库
                wmsFlow.setStorageStatus(1);
                wmsFlow.setProcessId(subtask.getProduceOrderProcessId());
                wmsFlow.setExecutorId(LoginHelper.getUserId());
                wmsFlow.setOrderProcessId(subtask.getProduceOrderProcessId());
                //报产类型  1 工序报产  2设备报产
                wmsFlow.setYieldType(1);
                if (StringUtils.isNotBlank(subtask.getRollCode())){
                    //关联工单号
                    wmsFlow.setAssociatedWorkOrder(subtask.getRollCode());
                }
                if (StrUtil.isEmpty(wmsFlow.getFlowOpenid())){
                    wmsFlow.setFlowOpenid("I-" + DateUtil.format(new Date(), "yyMMddHHmmss") + "-" + StrUtil.padPre(Convert.toStr(RedisUtils.incrAtomicValue(TenantStorage.getTenantId() + DateUtil.format(new Date(), "yyMMdd") + "_nextNumber")), 4, "0"));
                }
                if (StrUtil.isEmpty(wmsFlow.getFlowBatch())) {
                    wmsFlow.setFlowBatch(DateUtil.format(new Date(), "yyMMdd") + StrUtil.padPre(Convert.toStr(RedisUtils.incrAtomicValue(TenantStorage.getTenantId() + DateUtil.format(new Date(), "yyMMdd") + "_flowBatch")), 5, "0"));
                }
                //先查询出工单工序
                ProduceOrderProcess produceOrderProcess = produceOrderProcessMapper.selectById(subtask.getProduceOrderProcessId());
                if (ObjectUtil.isNotNull(produceOrderProcess)){
                    QueryWrapper<ProduceOrder> orderQueryWrapper = new QueryWrapper<>();
                    orderQueryWrapper.eq("id",produceOrderProcess.getOrderId());
                    List<ProduceOrder> produceOrderList = orderMapper.selectList(orderQueryWrapper);
                    //绑定订单号
                    if (null !=produceOrderList && produceOrderList.size()>0){
                        wmsFlow.setOrderCode(produceOrderList.get(0).getBillCode());
                    }
                }
                //插入操作根据后面判断
                //int insert = wmsFlowMapper.insert(wmsFlow);
                //2.新增库存，生成溯源码
                addmiddleMaterialByProcess(wmsFlow,subtask);
            }
        }
    }

    public void addmiddleMaterialByProcess(WmsFlow wmsFlow, TProduceOrderProcessSubtask subtask){
        ProduceOrderProcess produceOrderProcess = produceOrderProcessMapper.selectById(subtask.getProduceOrderProcessId());
        BaseProcess baseProcess = baseProcessMapper.selectById(produceOrderProcess.getProcessId());
        //然后在仓库里新增一个新生成得编码得物料,获取溯源码通过溯源表的code，当前传来的任务单号去查询，
        //获取到溯源表的数据，然后通过这条数据去查询c_id,符合这个条件的数据的溯源码
        //2.生成溯源码
        if (baseProcess.getProcessName().equals("整经")){
            QueryWrapper<MiddleMaterial> middleMaterialQueryWrapper = new QueryWrapper<>();
            middleMaterialQueryWrapper.eq("code",subtask.getRollCode());
            MiddleMaterial middleMaterial = middleMaterialMapper.selectOne(middleMaterialQueryWrapper);
            if (ObjectUtil.isNotNull(middleMaterial)){
                QueryWrapper<MiddleMaterial> middleMaterialQueryWrapperUp = new QueryWrapper<>();
                middleMaterialQueryWrapperUp.eq("c_id",middleMaterial.getId());
                List<MiddleMaterial> middleMaterialList = middleMaterialMapper.selectList(middleMaterialQueryWrapperUp);
                if (ObjectUtil.isNotNull(middleMaterialList) && middleMaterialList.size()>0){
                    TOrderEquipment tOrderEquipment = orderEquipmentMapper.selectById(subtask.getOrderEquipmentId());
                    EquipmentLedger equipmentLedger = equipmentLedgerMapper.selectById(tOrderEquipment.getEquipmentLedgerId());
                    String keyPart = equipmentLedger.getEquipmentCode();
                    String code = SourceCodeUtils.updateInnerCode(middleMaterialList.get(0).getSourceCode(), keyPart);
                    //整经修改质检任务的状态为未质检
                    middleMaterial.setState(MiddleMaterialStateType.NOT_INSPECTED.getState());
                    middleMaterial.setSourceCode(code);
                    middleMaterialMapper.updateById(middleMaterial);
                    //在整经质检处质检通过生成入库申请单入库
                }else {
                    log.info("整经任务单号:{},未关联经轴原材料",subtask.getRollCode());
                }
            }
        }else if (baseProcess.getProcessName().equals("掏综穿扣")){
            QueryWrapper<MiddleMaterial> middleMaterialQueryWrapper = new QueryWrapper<>();
            middleMaterialQueryWrapper.eq("code",subtask.getRollCode());
            MiddleMaterial middleMaterial = middleMaterialMapper.selectOne(middleMaterialQueryWrapper);
            if (ObjectUtil.isNotNull(middleMaterial)){
                QueryWrapper<MiddleMaterial> middleMaterialQueryWrapperUp = new QueryWrapper<>();
                //掏综穿扣关联了经线和纬线，经线是整经做的，所以通过任务单号不为空的确定
                middleMaterialQueryWrapperUp.eq("c_id",middleMaterial.getId())
                    .isNotNull("code");
                List<MiddleMaterial> middleMaterialList = middleMaterialMapper.selectList(middleMaterialQueryWrapperUp);
                if (ObjectUtil.isNotNull(middleMaterialList) && middleMaterialList.size()>0){
                    TOrderEquipment tOrderEquipment = orderEquipmentMapper.selectById(subtask.getOrderEquipmentId());
                    EquipmentLedger equipmentLedger = equipmentLedgerMapper.selectById(tOrderEquipment.getEquipmentLedgerId());
                    String keyPart = equipmentLedger.getEquipmentCode();
                    String code = SourceCodeUtils.updateInnerCode(middleMaterialList.get(0).getSourceCode(), keyPart);
                    //掏综穿扣需要质检
                    middleMaterial.setState(MiddleMaterialStateType.NOT_INSPECTED.getState());
                    middleMaterial.setSourceCode(code);
                    middleMaterialMapper.updateById(middleMaterial);
                    //掏综穿扣不需要入库（但是不入库无法关联）
                    addWmsStock(wmsFlow,code);
                }else {
                    log.info("掏综穿扣任务单号:{},未关联经线纬线",subtask.getRollCode());
                }
            }
        }else if (baseProcess.getProcessName().equals("编织")){
            QueryWrapper<MiddleMaterial> middleMaterialQueryWrapper = new QueryWrapper<>();
            middleMaterialQueryWrapper.eq("code",subtask.getRollCode());
            MiddleMaterial middleMaterial = middleMaterialMapper.selectOne(middleMaterialQueryWrapper);
            if (ObjectUtil.isNotNull(middleMaterial)){
                QueryWrapper<MiddleMaterial> middleMaterialQueryWrapperUp = new QueryWrapper<>();
                middleMaterialQueryWrapperUp.eq("c_id",middleMaterial.getId())
                    .isNotNull("code");
                List<MiddleMaterial> middleMaterialList = middleMaterialMapper.selectList(middleMaterialQueryWrapperUp);
                if (ObjectUtil.isNotNull(middleMaterialList) && middleMaterialList.size()>0){
                    TOrderEquipment tOrderEquipment = orderEquipmentMapper.selectById(subtask.getOrderEquipmentId());
                    EquipmentLedger equipmentLedger = equipmentLedgerMapper.selectById(tOrderEquipment.getEquipmentLedgerId());
                    String sourceCode = middleMaterialList.get(0).getSourceCode();
                    //机台号+母卷序列号
                    String keyPart = equipmentLedger.getEquipmentCode() + sourceCode.substring(sourceCode.length() - 3);
                    String code = SourceCodeUtils.updateInnerCode(sourceCode, keyPart);
                    //编织不需要质检
                    middleMaterial.setState(MiddleMaterialStateType.THEN_INVENTORY.getState());
                    middleMaterial.setSourceCode(code);
                    middleMaterialMapper.updateById(middleMaterial);
                    //生成出入库记录
                    int insert = wmsFlowMapper.insert(wmsFlow);
                    //直接入库
                    addWmsStock(wmsFlow,code);
                }else {
                    log.info("编织任务单号:{},未关联纬线",subtask.getRollCode());
                }
            }
        }else if (baseProcess.getProcessName().equals("整平、初检")){
            QueryWrapper<MiddleMaterial> middleMaterialQueryWrapper = new QueryWrapper<>();
            middleMaterialQueryWrapper.eq("code",subtask.getRollCode());
            MiddleMaterial middleMaterial = middleMaterialMapper.selectOne(middleMaterialQueryWrapper);
            if (ObjectUtil.isNotNull(middleMaterial)){
                QueryWrapper<MiddleMaterial> middleMaterialQueryWrapperUp = new QueryWrapper<>();
                middleMaterialQueryWrapperUp.eq("c_id",middleMaterial.getId());
                List<MiddleMaterial> middleMaterialList = middleMaterialMapper.selectList(middleMaterialQueryWrapperUp);
                if (ObjectUtil.isNotNull(middleMaterialList) && middleMaterialList.size()>0){
                    TOrderEquipment tOrderEquipment = orderEquipmentMapper.selectById(subtask.getOrderEquipmentId());
                    EquipmentLedger equipmentLedger = equipmentLedgerMapper.selectById(tOrderEquipment.getEquipmentLedgerId());
                    String sourceCode = middleMaterialList.get(0).getSourceCode();
                    //机台号
                    String keyPart = equipmentLedger.getEquipmentCode();
                    String code = SourceCodeUtils.updateInnerCode(sourceCode, keyPart);
                    //整平、初检不需要在溯源表中质检，而是在领料时生成一个待填写的卷网检验报告
                    middleMaterial.setState(MiddleMaterialStateType.NOT_INSPECTED.getState());
                    middleMaterial.setSourceCode(code);
                    middleMaterialMapper.updateById(middleMaterial);
                    //整平、初检需要生成入库申请单
                    addStorageApplyReceipt(wmsFlow,subtask);
                }else {
                    log.info("整平、初检任务单号:{},未关联半成品溯源码",subtask.getRollCode());
                }
            }
        }else if (baseProcess.getProcessName().equals("分切")){
            QueryWrapper<MiddleMaterial> middleMaterialQueryWrapper = new QueryWrapper<>();
            middleMaterialQueryWrapper.eq("code",subtask.getRollCode())
                .isNull("sn_code");
            MiddleMaterial middleMaterial = middleMaterialMapper.selectOne(middleMaterialQueryWrapper);
            if (ObjectUtil.isNotNull(middleMaterial)){
                QueryWrapper<MiddleMaterial> middleMaterialQueryWrapperUp = new QueryWrapper<>();
                middleMaterialQueryWrapperUp.eq("c_id",middleMaterial.getId());
                List<MiddleMaterial> middleMaterialList = middleMaterialMapper.selectList(middleMaterialQueryWrapperUp);
                if (ObjectUtil.isNotNull(middleMaterialList) && middleMaterialList.size()>0){
                    String sourceCode = middleMaterialList.get(0).getSourceCode();
                    TOrderEquipment tOrderEquipment = orderEquipmentMapper.selectById(subtask.getOrderEquipmentId());
                    EquipmentLedger equipmentLedger = equipmentLedgerMapper.selectById(tOrderEquipment.getEquipmentLedgerId());
                    BaseMaterial baseMaterial = baseMaterialMapper.selectById(wmsFlow.getMaterialId());
//                    if (ObjectUtil.isNotNull(baseMaterial)){
//
//                    }
                    //todo:目数和厚度取不出来，后期维护到物料表
                    //机台号+目数+厚度+小卷序列号也是上一道工序的序列号
                    String keyPart = equipmentLedger.getEquipmentCode() + sourceCode.substring(sourceCode.length() - 3);
                    String code = SourceCodeUtils.updateInnerCode(sourceCode, keyPart);
                    //分切溯源表中没有质检
                    //middleMaterial.setState(MiddleMaterialStateType.NOT_INSPECTED.getState());
                    middleMaterial.setSourceCode(code);
                    middleMaterialMapper.updateById(middleMaterial);
                    //分切后要新增一个小卷往溯源表中增加
                    MiddleMaterial middleMaterialPartOff = new MiddleMaterial();
                    middleMaterialPartOff.setProcess("分切");
                    middleMaterialPartOff.setTenantId(TenantStorage.getTenantId());
                    middleMaterialPartOff.setBaseMaterialId(wmsFlow.getMaterialId());
//                  ProduceOrderProcess produceOrderProcess = produceOrderProcessMapper.selectById(bo.getOrderProcessId());
                    middleMaterialPartOff.setProduceOrderCode(wmsFlow.getOrderCode());
                    middleMaterialPartOff.setDeviceCode(equipmentLedger.getEquipmentCode());
                    middleMaterialPartOff.setCode(subtask.getRollCode());
                    middleMaterialPartOff.setSourceCode(code);
                    middleMaterialPartOff.setPId(middleMaterial.getId());
                    //aoi质检传来的数据有sn码，通过sn码查询溯源码，然后更改状态
                    middleMaterialPartOff.setState(MiddleMaterialStateType.NOT_INSPECTED.getState());
                    //新增sn码
                    middleMaterialPartOff.setSnCode(getSnCode());
                    middleMaterialMapper.insert(middleMaterialPartOff);
                    //生成出入库记录
                    int insert = wmsFlowMapper.insert(wmsFlow);
                    //todo:分切后如果要贴码，就把生成的溯源码单独存到某个表中或者直接根据任务查询溯源表中的编码
                    addWmsStock(wmsFlow,code);
                }else {
                    log.info("分切任务单号:{},未关联成品溯源码",subtask.getRollCode());
                }
            }
        }else if (baseProcess.getProcessName().equals("AOI质检")){
            QueryWrapper<MiddleMaterial> middleMaterialQueryWrapper = new QueryWrapper<>();
            middleMaterialQueryWrapper.eq("code",subtask.getRollCode());
            MiddleMaterial middleMaterial = middleMaterialMapper.selectOne(middleMaterialQueryWrapper);
            if (ObjectUtil.isNotNull(middleMaterial)){
                QueryWrapper<MiddleMaterial> middleMaterialQueryWrapperUp = new QueryWrapper<>();
                middleMaterialQueryWrapperUp.eq("c_id",middleMaterial.getId());
                List<MiddleMaterial> middleMaterialList = middleMaterialMapper.selectList(middleMaterialQueryWrapperUp);
                if (ObjectUtil.isNotNull(middleMaterialList) && middleMaterialList.size()>0){
                    String sourceCode = middleMaterialList.get(0).getSourceCode();
                    TOrderEquipment tOrderEquipment = orderEquipmentMapper.selectById(subtask.getOrderEquipmentId());
                    EquipmentLedger equipmentLedger = equipmentLedgerMapper.selectById(tOrderEquipment.getEquipmentLedgerId());
                    BaseMaterial baseMaterial = baseMaterialMapper.selectById(wmsFlow.getMaterialId());
//                    if (ObjectUtil.isNotNull(baseMaterial)){
//
//                    }
                    //todo:目数和厚度取不出来，后期维护到物料表
                    //机台号+目数+厚度+小卷序列号也是上一道工序的序列号
                    String keyPart = equipmentLedger.getEquipmentCode() + sourceCode.substring(sourceCode.length() - 3);
                    String code = SourceCodeUtils.updateInnerCode(sourceCode, keyPart);
                    //分切溯源表中没有质检
                    //middleMaterial.setState(MiddleMaterialStateType.NOT_INSPECTED.getState());
                    middleMaterial.setSourceCode(code);
                    middleMaterialMapper.updateById(middleMaterial);
                    //分切后要新增一个小卷往溯源表中增加
                    MiddleMaterial middleMaterialPartOff = new MiddleMaterial();
                    middleMaterialPartOff.setProcess("分切");
                    middleMaterialPartOff.setTenantId(TenantStorage.getTenantId());
                    middleMaterialPartOff.setBaseMaterialId(wmsFlow.getMaterialId());
//                  ProduceOrderProcess produceOrderProcess = produceOrderProcessMapper.selectById(bo.getOrderProcessId());
                    middleMaterialPartOff.setProduceOrderCode(wmsFlow.getOrderCode());
                    middleMaterialPartOff.setDeviceCode(equipmentLedger.getEquipmentCode());
                    middleMaterialPartOff.setCode(subtask.getRollCode());
                    middleMaterialPartOff.setSourceCode(code);
                    middleMaterialPartOff.setPId(middleMaterial.getId());
                    //aoi质检传来的数据有sn码，通过sn码查询溯源码，然后更改状态
                    middleMaterialPartOff.setState(MiddleMaterialStateType.NOT_INSPECTED.getState());
                    //新增sn码
                    middleMaterialPartOff.setSnCode(getSnCode());
                    middleMaterialMapper.insert(middleMaterialPartOff);
                    //生成出入库记录
                    int insert = wmsFlowMapper.insert(wmsFlow);
                    //todo:分切后如果要贴码，就把生成的溯源码单独存到某个表中或者直接根据任务查询溯源表中的编码
                    addWmsStock(wmsFlow,code);
                }else {
                    log.info("分切任务单号:{},未关联成品溯源码",subtask.getRollCode());
                }
            }
        }
    }

    /**
      * <AUTHOR>
      * @param
      * @return java.lang.String
      * @remark 暂时写死sn码
      * @date 2025/6/15 16:45
     */
    public static String getSnCode() {
        String key = "572HHPC";
        return key + String.format("%03d", RedisUtils.incrAtomicValue(key)) +"W00014KZ+000108";
    }

    public void addWmsStock(WmsFlow wmsFlow,String materialProductCode){
        WmsStock wmsStock = new WmsStock();
        //wmsStock.setMaterialCode(wmsFlow.getMaterialCode());
        wmsStock.setMaterialId(wmsFlow.getMaterialId());
        wmsStock.setWarehouseId(wmsFlow.getWarehouseId());
        wmsStock.setAreaId(wmsFlow.getAreaId());
        wmsStock.setAllocationId(wmsFlow.getAllocationId());
        //wmsStock.setAllocationCode();
        //todo:报工的批次号怎么来？
        //wmsStock.setFlowBatch(tStorageApplyReceiptDetail.getBatchNumber());
        wmsStock.setMaterialProductCode(materialProductCode);
        wmsStockMapper.insert(wmsStock);
    }

    //生成入库单
    public void addStorageApplyReceipt(WmsFlow wmsFlow,TProduceOrderProcessSubtask subtask){
        TStorageApplyReceiptBo tStorageApplyReceiptBo = new TStorageApplyReceiptBo();
        tStorageApplyReceiptBo.setTenantId(TenantStorage.getTenantId());
        tStorageApplyReceiptBo.setOrderCode(wmsFlow.getOrderCode());
        // 获取一个雪花算法实例（默认配置）
        Snowflake snowflake = IdUtil.getSnowflake();
        // 生成一个ID
        final long id = snowflake.nextId();
        tStorageApplyReceiptBo.setId(id);
        tStorageApplyReceiptBo.setFlowType("in");
        tStorageApplyReceiptBo.setFlowWay("生产入库");
        tStorageApplyReceiptBo.setSubmitState(1L);
        tStorageApplyReceiptBo.setSubmitDate(new Date());
        tStorageApplyReceiptBo.setRollCode(subtask.getRollCode());
        tStorageApplyReceiptBo.setNumberId(DigitalGenerationUtil.getReceiptsCode("ZJRKD"));
        Boolean aBoolean = iTStorageApplyReceiptService.insertByBo(tStorageApplyReceiptBo);
        if (aBoolean) {
            // 如果主表插入成功，插入明细信息
            TStorageApplyReceiptDetailBo detailBo = new TStorageApplyReceiptDetailBo();
            detailBo.setReceiptId(id);
            detailBo.setTenantId(TenantStorage.getTenantId());
            BaseMaterial baseMaterial = baseMaterialMapper.selectById(wmsFlow.getMaterialId());
            if (ObjectUtil.isNotNull(baseMaterial)){
                detailBo.setMaterialCode(baseMaterial.getMaterialCode());
            }
            detailBo.setOrderCode(tStorageApplyReceiptBo.getOrderCode());
            detailBo.setCheckStatus(0);
            detailBo.setFlowType(tStorageApplyReceiptBo.getFlowType());
            detailBo.setMaterialId(wmsFlow.getMaterialId().toString());
            detailBo.setMaterialCode(wmsFlow.getMaterialId().toString());
            detailBo.setWorkshopId(wmsFlow.getWorkshopId());
            detailBo.setReceivableCount(1L);
            final Boolean insertResult = iDetailService.insertByBo(detailBo);
            if (!insertResult) {
                log.info("整平报工后入库申请单明细插入失败，任务单号:{}",subtask.getRollCode());
            }
        } else {
            log.info("整平报工后入库申请单主表插入失败，任务单号:{}",subtask.getRollCode());
        }
    }



    public List<BomDetail> bomPreviewTwo(Long orderId, Long materialId,Long subtaskCount) {
        //查询生产订单中的物料id、计划数量
        ProduceOrderVo produceOrderVo = orderMapper.selectVoById(orderId);
        if (produceOrderVo.getIsFirst() == 1) {
            //订单为第一级仍用原来的逻辑
            //根据物料id，查询此物料的bom
            QueryWrapper<Bom> bomQueryWrapper = new QueryWrapper<>();
            bomQueryWrapper.eq("material_id", produceOrderVo.getMaterialId());
            bomQueryWrapper.eq("bom_status", "0");//状态为开启的bom
            List<BomVo> bomVos = bomMapper.selectVoList(bomQueryWrapper);
            if (bomVos != null && bomVos.size() > 0) {
                if (bomVos.size() > 1) {
                    throw new ServiceException("此订单的" + bomVos.get(0).getMaterialName() + "(" + bomVos.get(0).getMaterialCode() + ")" + "物料Bom不唯一，请关闭其他版本的Bom");
                }
                // 根据bom查询bom详情
                List<BomDetail> bomDetails = bomDetailMapper.queryBomMaterialListByBomAndMaterial(bomVos.get(0).getId(), null);
                if (bomDetails != null && bomDetails.size() > 0) {
                    recursion(produceOrderVo, bomDetails, new BigDecimal(1),subtaskCount);
                }
                return bomDetails;
            } else {
                QueryWrapper<BaseMaterial> baseMaterialQueryWrapper = new QueryWrapper<>();
                baseMaterialQueryWrapper.eq("id", produceOrderVo.getMaterialId());
                BaseMaterial baseMaterial = baseMaterialMapper.selectOne(baseMaterialQueryWrapper);
                throw new ServiceException("此订单的" + baseMaterial.getMaterialName() + "(" + baseMaterial.getMaterialCode() + ")" + "物料没有创建BOM，请先在基础数据中创建");
            }
        } else {
            QueryWrapper<Bom> bomQueryWrapper = new QueryWrapper<>();
            bomQueryWrapper.eq("material_id", materialId);
            bomQueryWrapper.eq("bom_status", "0");//状态为开启的bom
            List<BomVo> bomVos = bomMapper.selectVoList(bomQueryWrapper);
            if (bomVos != null && bomVos.size() > 0) {
                if (bomVos.size() > 1) {
                    throw new ServiceException("此订单的" + bomVos.get(0).getMaterialName() + "(" + bomVos.get(0).getMaterialCode() + ")" + "物料Bom不唯一，请关闭其他版本的Bom");
                }
                //根据当前物料id和父bom_id查询出当前的bom详情
                List<BomDetail> bomDetails1 = bomDetailMapper.queryBomMaterialListByBomAndMaterial(bomVos.get(0).getId(), null);
                recursion(produceOrderVo, bomDetails1, new BigDecimal(1),subtaskCount);
                return bomDetails1;
            } else {
                QueryWrapper<BaseMaterial> baseMaterialQueryWrapper = new QueryWrapper<>();
                baseMaterialQueryWrapper.eq("id", produceOrderVo.getMaterialId());
                BaseMaterial baseMaterial = baseMaterialMapper.selectOne(baseMaterialQueryWrapper);
                throw new ServiceException("此订单的" + baseMaterial.getMaterialName() + "(" + baseMaterial.getMaterialCode() + ")" + "物料没有创建BOM，请先在基础数据中创建");
            }
        }

    }

    private void recursion(ProduceOrderVo produceOrderVo, List<BomDetail> bomDetails, BigDecimal pCount,Long subtaskCount) {
        for (BomDetail bomDetail : bomDetails) {
            List<BomDetail> bomDetails1 = bomDetailMapper.queryBomMaterialListByBomAndPid(bomDetail.getBomId(), bomDetail.getId());
            if (bomDetails1 != null && bomDetails1.size() > 0) {
                recursion(produceOrderVo, bomDetails1, bomDetail.getMaterialCount(),subtaskCount);
                bomDetail.setChildren(bomDetails1);
            }
            //计划用料数
            if (ObjectUtil.isNotNull(bomDetail.getMaterialCount())) {
                BigDecimal bigDecimal = new BigDecimal(subtaskCount);
                BigDecimal tempCount = bigDecimal.multiply(bomDetail.getMaterialCount());
                bomDetail.setPlanNum(tempCount.multiply(pCount));
            } else {
                bomDetail.setPlanNum(new BigDecimal(subtaskCount));
            }
            //实际用料数
            QueryWrapper<WmsFlow> wmsFlowQueryWrapper = new QueryWrapper<>();
            wmsFlowQueryWrapper.eq("out_bound_order_id", produceOrderVo.getId());
            wmsFlowQueryWrapper.eq("flow_way", "out02");
            wmsFlowQueryWrapper.eq("examine_status", "1");
            wmsFlowQueryWrapper.eq("material_id", bomDetail.getMaterialId());
            List<WmsFlow> wmsFlows = wmsFlowMapper.selectList(wmsFlowQueryWrapper);
            if (wmsFlows != null && wmsFlows.size() > 0) {
                BigDecimal realNum = new BigDecimal(0);
                for (WmsFlow wmsFlow : wmsFlows) {
                    realNum = realNum.add(wmsFlow.getMaterialCount());
                }
                bomDetail.setRealNum(realNum);
            }
        }
        return;
    }



}

package com.boyo.web.controller.common;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.core.domain.R;
import com.boyo.common.core.domain.entity.SysUser;
import com.boyo.common.utils.redis.RedisUtils;
import com.boyo.framework.wechat.WxMaConfiguration;
import com.boyo.system.mapper.SysUserMapper;
import com.boyo.system.service.ISysConfigService;
import lombok.AllArgsConstructor;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;


@RestController
@RequestMapping("/app/wechat")
@AllArgsConstructor
public class WechatController {

    private ISysConfigService configService;
    private final WxMaService wxMaService;

    private final SysUserMapper userMapper;


    /**
     * 获取发布状态 0：审核中 1：审核完成
     *
     * @return
     */
    @GetMapping("/getDeployStatus")
    private R<String> getDeployStatus() {
        return R.ok(configService.selectConfigByKey("sys.mini.access"));
    }

    @GetMapping("/getSecret")
    public R<String> getSecret(String appid) {
        if (appid.equals("wx0287611a974fef37")) {
            return R.ok("2d5c9d77b452ed7be1d2587e34e83748");
        } else if (appid.equals("wx214a15f873445daf")) {
            return R.ok("345cb34c6491d052cff3d585e6f6cd94");
        }
        return null;
    }

    @GetMapping("/getOpenid")
    public R<JSONObject> getOpenid(String code) {
        JSONObject result = new JSONObject();
        try {
            WxMaJscode2SessionResult obj = wxMaService.getUserService().getSessionInfo(code);
            result.put("union", obj.getUnionid());
            result.put("openid", obj.getOpenid());
            RedisUtils.setCacheObject(obj.getUnionid(), "1", Duration.ofSeconds(10));
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
        return R.ok(result);
    }

    @PostMapping("/registerWechat")
    public R<Boolean> registerWechat(@RequestBody JSONObject json) {
//    public R<JSONObject> registeWechat(String openid,String code){
        String openid = json.getString("openid");
        String code = json.getString("code");
        WxMaPhoneNumberInfo info = null;
        try {
            info = wxMaService.getUserService().getPhoneNoInfo(code);
//            wxMaService.getUserService().getUserInfo(openid);
            String phone = info.getPhoneNumber();
//            String accessToken = wxMpService.getAccessToken();
//            WxMpUser wxuser = wxMpService.getUserService().userInfo(openid, accessToken);
//            System.out.println(JSONObject.toJSONString(wxuser));
            SysUser user = userMapper.selectOne(new QueryWrapper<SysUser>().eq("phonenumber", phone));
            if (ObjectUtil.isNotNull(user)){
                user.setWechatId(openid);
            }else{
                user = new SysUser();
                user.setPhonenumber(phone);
                user.setUserName(phone);
//                user.setNickName(phone);
                user.setUserType("real_user");
                user.setPassword(BCrypt.hashpw(phone));
                user.setWechatId(openid);
            }
            userMapper.insertOrUpdate(user);
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
        return R.ok(true);
    }

}

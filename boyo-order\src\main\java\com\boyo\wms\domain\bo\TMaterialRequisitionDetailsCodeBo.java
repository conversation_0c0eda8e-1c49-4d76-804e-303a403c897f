package com.boyo.wms.domain.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;
import com.boyo.common.core.domain.BaseEntity;
import com.boyo.common.core.validate.AddGroup;
import com.boyo.common.core.validate.EditGroup;

/**
 * 领料单详情物料业务对象 t_material_requisition_details_code
 *
 * <AUTHOR>
 * @date 2025-06-10
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TMaterialRequisitionDetailsCodeBo extends BaseEntity {

    /**
     * 
     */
    //@NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 
     */
    //@NotNull(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long tenantId;

    /**
     * 物料名称
     */
    //@NotBlank(message = "物料名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String materialName;

    /**
     * 领料单详情id
     */
    //@NotNull(message = "领料单详情id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long materialRequisitionDetailsId;

    /**
     * 物料的具体成品编码
     */
    //@NotBlank(message = "物料的具体成品编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String materialProductCode;


}

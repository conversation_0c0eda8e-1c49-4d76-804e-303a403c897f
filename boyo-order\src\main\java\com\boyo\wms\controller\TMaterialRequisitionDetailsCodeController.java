package com.boyo.wms.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.boyo.common.annotation.RepeatSubmit;
import com.boyo.common.annotation.Log;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.PageQuery;
import com.boyo.common.core.domain.R;
import com.boyo.common.core.validate.AddGroup;
import com.boyo.common.core.validate.EditGroup;
import com.boyo.common.enums.BusinessType;
import com.boyo.common.utils.poi.ExcelUtil;
import com.boyo.wms.domain.vo.TMaterialRequisitionDetailsCodeVo;
import com.boyo.wms.domain.bo.TMaterialRequisitionDetailsCodeBo;
import com.boyo.wms.service.ITMaterialRequisitionDetailsCodeService;
import com.boyo.common.core.page.TableDataInfo;

/**
 * 领料单详情物料
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/wms/materialRequisitionDetailsCode")
public class TMaterialRequisitionDetailsCodeController extends BaseController {

    private final ITMaterialRequisitionDetailsCodeService iTMaterialRequisitionDetailsCodeService;

    /**
     * 查询领料单详情物料列表
     */
    @SaCheckPermission("wms:materialRequisitionDetailsCode:list")
    @GetMapping("/list")
    public TableDataInfo<TMaterialRequisitionDetailsCodeVo> list(TMaterialRequisitionDetailsCodeBo bo, PageQuery pageQuery) {
        return iTMaterialRequisitionDetailsCodeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出领料单详情物料列表
     */
    @SaCheckPermission("wms:materialRequisitionDetailsCode:export")
    @Log(title = "领料单详情物料", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TMaterialRequisitionDetailsCodeBo bo, HttpServletResponse response) {
        List<TMaterialRequisitionDetailsCodeVo> list = iTMaterialRequisitionDetailsCodeService.queryList(bo);
        ExcelUtil.exportExcel(list, "领料单详情物料", TMaterialRequisitionDetailsCodeVo.class, response);
    }

    /**
     * 获取领料单详情物料详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("wms:materialRequisitionDetailsCode:query")
    @GetMapping("/{id}")
    public R<TMaterialRequisitionDetailsCodeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iTMaterialRequisitionDetailsCodeService.queryById(id));
    }

    /**
     * 新增领料单详情物料
     */
    @SaCheckPermission("wms:materialRequisitionDetailsCode:add")
    @Log(title = "领料单详情物料", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TMaterialRequisitionDetailsCodeBo bo) {
        return toAjax(iTMaterialRequisitionDetailsCodeService.insertByBo(bo));
    }

    /**
     * 修改领料单详情物料
     */
    @SaCheckPermission("wms:materialRequisitionDetailsCode:edit")
    @Log(title = "领料单详情物料", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TMaterialRequisitionDetailsCodeBo bo) {
        return toAjax(iTMaterialRequisitionDetailsCodeService.updateByBo(bo));
    }

    /**
     * 删除领料单详情物料
     *
     * @param ids 主键串
     */
    @SaCheckPermission("wms:materialRequisitionDetailsCode:remove")
    @Log(title = "领料单详情物料", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iTMaterialRequisitionDetailsCodeService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}

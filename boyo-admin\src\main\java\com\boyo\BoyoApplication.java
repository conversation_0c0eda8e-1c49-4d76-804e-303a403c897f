package com.boyo;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 启动程序
 *
 * <AUTHOR>
 */

@SpringBootApplication(scanBasePackages = {"org.jeecg.modules.jmreport","com.boyo"})
@EnableScheduling
public class BoyoApplication {

    public static void main(String[] args) {
        System.setProperty("spring.devtools.restart.enabled" , "false");
        SpringApplication application = new SpringApplication(BoyoApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  boyo-Flowable-Plus启动成功   ლ(´ڡ`ლ)ﾞ");
    }

}

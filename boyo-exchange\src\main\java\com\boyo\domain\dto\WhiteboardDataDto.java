package com.boyo.domain.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 白板显示数据DTO
 * 对应白板上的AOI生产日报表格式
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
public class WhiteboardDataDto {
    
    /**
     * 设备序列号(A-1, A-2, B-1, B-2, C-1, C-2, D-1, D-2)
     */
    private String machineSn;
    
    /**
     * 生产时间状态
     */
    private String productionTimeStatus;
    
    /**
     * 打料数量(投入检测数量)
     */
    private Integer inputCount;
    
    /**
     * 投产数量(实际投产数量)
     */
    private Integer productionCount;
    
    /**
     * OK数量(合格品数量)
     */
    private Integer okCount;
    
    /**
     * NG数量(不合格品数量)
     */
    private Integer ngCount;
    
    /**
     * 受控良率(%)
     */
    private BigDecimal controlledYield;
    
    /**
     * NG项前一
     */
    private String ngTop1;
    
    /**
     * NG项前二
     */
    private String ngTop2;
    
    /**
     * NG项前三
     */
    private String ngTop3;
    
    /**
     * 机台检率(%)
     */
    private BigDecimal machineDetectionRate;
    
    /**
     * 异常信息
     */
    private String abnormalInfo;
    
    /**
     * 设备状态(运行/停机/维护)
     */
    private String equipmentStatus;
    
    /**
     * 班次类型(白班/夜班)
     */
    private String shiftType;
    
    /**
     * 操作员姓名
     */
    private String operatorName;
    
    /**
     * 质检员姓名
     */
    private String qualityInspector;
    
    /**
     * 班长姓名
     */
    private String shiftLeader;
    
    /**
     * 计算良率百分比显示
     * @return 格式化的良率字符串
     */
    public String getYieldDisplay() {
        if (controlledYield != null) {
            return controlledYield.toString() + "%";
        }
        return "0%";
    }
    
    /**
     * 计算检测率百分比显示
     * @return 格式化的检测率字符串
     */
    public String getDetectionRateDisplay() {
        if (machineDetectionRate != null) {
            return machineDetectionRate.toString() + "%";
        }
        return "0%";
    }
    
    /**
     * 获取NG项前三的组合显示
     * @return NG项前三的字符串
     */
    public String getNgTopDisplay() {
        StringBuilder sb = new StringBuilder();
        if (ngTop1 != null && !ngTop1.trim().isEmpty()) {
            sb.append("1.").append(ngTop1);
        }
        if (ngTop2 != null && !ngTop2.trim().isEmpty()) {
            if (sb.length() > 0) sb.append(" ");
            sb.append("2.").append(ngTop2);
        }
        if (ngTop3 != null && !ngTop3.trim().isEmpty()) {
            if (sb.length() > 0) sb.append(" ");
            sb.append("3.").append(ngTop3);
        }
        return sb.toString();
    }
    
    /**
     * 获取设备状态显示样式
     * @return CSS样式类名
     */
    public String getStatusStyle() {
        if (equipmentStatus == null) {
            return "status-unknown";
        }
        switch (equipmentStatus) {
            case "运行":
                return "status-running";
            case "停机":
                return "status-stopped";
            case "维护":
                return "status-maintenance";
            default:
                return "status-unknown";
        }
    }
    
    /**
     * 获取良率状态样式
     * @return CSS样式类名
     */
    public String getYieldStyle() {
        if (controlledYield == null) {
            return "yield-unknown";
        }
        if (controlledYield.compareTo(new BigDecimal("98")) >= 0) {
            return "yield-excellent";  // 优秀 >= 98%
        } else if (controlledYield.compareTo(new BigDecimal("95")) >= 0) {
            return "yield-good";       // 良好 >= 95%
        } else if (controlledYield.compareTo(new BigDecimal("90")) >= 0) {
            return "yield-normal";     // 一般 >= 90%
        } else {
            return "yield-poor";       // 较差 < 90%
        }
    }
    
    /**
     * 判断是否有异常
     * @return 是否有异常
     */
    public boolean hasAbnormal() {
        return abnormalInfo != null && !abnormalInfo.trim().isEmpty();
    }
    
    /**
     * 获取总检测数量
     * @return 总检测数量
     */
    public Integer getTotalCount() {
        int ok = okCount != null ? okCount : 0;
        int ng = ngCount != null ? ngCount : 0;
        return ok + ng;
    }
}

package com.boyo.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.boyo.common.annotation.ExcelDictFormat;
import com.boyo.common.convert.ExcelDictConvert;
import lombok.Data;

import java.util.Date;


/**
 * 工单工序子任务视图对象 t_produce_order_process_subtask
 *
 * <AUTHOR>
 * @date 2025-06-07
 */
@Data
@ExcelIgnoreUnannotated
public class TProduceOrderProcessSubtaskVo {

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 设备派工id
     */
    @ExcelProperty(value = "设备派工id")
    private Long orderEquipmentId;

    /**
     * 工单工序id
     */
    @ExcelProperty(value = "工单工序id")
    private Long produceOrderProcessId;

    /**
     * 报工人，为空时不限制
     */
    @ExcelProperty(value = "报工人，为空时不限制")
    private String processUsers;

    /**
     * 工序状态
     */
    @ExcelProperty(value = "工序状态")
    private String processStatus;

    /**
     * 编码
     */
    @ExcelProperty(value = "编码")
    private String rollCode;

    /**
     * 所属企业
     */
    @ExcelProperty(value = "所属企业")
    private Long tenantId;

    /**
     * 设备台账id
     */
    private Long equipmentLedgerId;


    private String processName;


    private Double reportCount;


    private Double imperfectCount;


    private String userNames;


    private String orderCode;

    private String materialName;

    private Double orderCount;

    private Date orderStart;

    private Date orderEnd;

    private String workshopName;

    private String equipmentName;
    private String billCode;
    private String materialId;
    private String materialCode;
    private Date planStart;
    private Date planEnd;
    /**
     * 子任务计划生产数
     */
    private Long subtaskCount;


    private String materialNorms;

    /**
     * 计划生产经轴长度
     */
    private Long spindleLength;

    private String equipmentCode;

}

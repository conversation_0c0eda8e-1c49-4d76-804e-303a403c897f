<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mapper.AoiDefectDetailMapper">

    <!-- 根据核心表ID列表批量查询缺陷详情 -->
    <select id="selectByReportCoreIds" resultType="com.boyo.domain.AoiDefectDetail">
        SELECT * FROM t_aoi_defect_detail 
        WHERE report_core_id IN
        <foreach collection="reportCoreIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND del_flag = 0
    </select>

    <!-- 统计缺陷分布 -->
    <select id="selectDefectDistribution" resultType="map">
        SELECT 
            'web_blemish' as defect_type,
            '网疤' as defect_name,
            SUM(d.web_blemish_count) as total_count,
            ROUND(AVG(d.web_blemish_rate), 2) as avg_rate
        FROM t_aoi_defect_detail d
        INNER JOIN t_aoi_daily_report_core c ON d.report_core_id = c.id
        WHERE c.report_date = #{reportDate}
        <if test="machineSn != null and machineSn != ''">
            AND c.machine_sn = #{machineSn}
        </if>
        AND d.del_flag = 0 AND c.del_flag = 0
        
        UNION ALL
        
        SELECT 
            'pillar' as defect_type,
            '柱道' as defect_name,
            SUM(d.pillar_count) as total_count,
            ROUND(AVG(d.pillar_rate), 2) as avg_rate
        FROM t_aoi_defect_detail d
        INNER JOIN t_aoi_daily_report_core c ON d.report_core_id = c.id
        WHERE c.report_date = #{reportDate}
        <if test="machineSn != null and machineSn != ''">
            AND c.machine_sn = #{machineSn}
        </if>
        AND d.del_flag = 0 AND c.del_flag = 0
        
        UNION ALL
        
        SELECT 
            'hole' as defect_type,
            '破洞' as defect_name,
            SUM(d.hole_count) as total_count,
            ROUND(AVG(d.hole_rate), 2) as avg_rate
        FROM t_aoi_defect_detail d
        INNER JOIN t_aoi_daily_report_core c ON d.report_core_id = c.id
        WHERE c.report_date = #{reportDate}
        <if test="machineSn != null and machineSn != ''">
            AND c.machine_sn = #{machineSn}
        </if>
        AND d.del_flag = 0 AND c.del_flag = 0
        
        ORDER BY total_count DESC
    </select>

    <!-- 从原始数据生成缺陷详情 -->
    <insert id="generateFromRawData">
        INSERT INTO t_aoi_defect_detail (
            report_core_id,
            web_blemish_count, pillar_count, hole_count, double_line_count, knot_count,
            oxidation_count, oil_stain_count, foreign_object_count, deformation_count, crack_count,
            discoloration_count, hairiness_count, connectorlug_count,
            mesh_count, thickness_count, markdot_count,
            create_time, update_time, del_flag
        )
        SELECT
            #{reportCoreId} as report_core_id,
            SUM(CASE WHEN web_blemish_defect IS NOT NULL AND web_blemish_defect != '' THEN CAST(web_blemish_defect AS UNSIGNED) ELSE 0 END) as web_blemish_count,
            SUM(CASE WHEN pillar_defect IS NOT NULL AND pillar_defect != '' THEN CAST(pillar_defect AS UNSIGNED) ELSE 0 END) as pillar_count,
            SUM(CASE WHEN hole_defect IS NOT NULL AND hole_defect != '' THEN CAST(hole_defect AS UNSIGNED) ELSE 0 END) as hole_count,
            SUM(CASE WHEN double_line_defect IS NOT NULL AND double_line_defect != '' THEN CAST(double_line_defect AS UNSIGNED) ELSE 0 END) as double_line_count,
            SUM(CASE WHEN knot_defect IS NOT NULL AND knot_defect != '' THEN CAST(knot_defect AS UNSIGNED) ELSE 0 END) as knot_count,
            SUM(CASE WHEN oxidation_defect IS NOT NULL AND oxidation_defect != '' THEN CAST(oxidation_defect AS UNSIGNED) ELSE 0 END) as oxidation_count,
            SUM(CASE WHEN oil_stain_defect IS NOT NULL AND oil_stain_defect != '' THEN CAST(oil_stain_defect AS UNSIGNED) ELSE 0 END) as oil_stain_count,
            SUM(CASE WHEN foreign_object_defect IS NOT NULL AND foreign_object_defect != '' THEN CAST(foreign_object_defect AS UNSIGNED) ELSE 0 END) as foreign_object_count,
            SUM(CASE WHEN deformation_defect IS NOT NULL AND deformation_defect != '' THEN CAST(deformation_defect AS UNSIGNED) ELSE 0 END) as deformation_count,
            SUM(CASE WHEN crack_defect IS NOT NULL AND crack_defect != '' THEN CAST(crack_defect AS UNSIGNED) ELSE 0 END) as crack_count,
            SUM(CASE WHEN discoloration_defect IS NOT NULL AND discoloration_defect != '' THEN CAST(discoloration_defect AS UNSIGNED) ELSE 0 END) as discoloration_count,
            SUM(CASE WHEN hairiness_defect IS NOT NULL AND hairiness_defect != '' THEN CAST(hairiness_defect AS UNSIGNED) ELSE 0 END) as hairiness_count,
            0 as connectorlug_count,
            SUM(CASE WHEN mesh IS NOT NULL AND mesh != '' THEN CAST(mesh AS UNSIGNED) ELSE 0 END) as mesh_count,
            SUM(CASE WHEN thickness IS NOT NULL AND thickness != '' THEN CAST(thickness AS UNSIGNED) ELSE 0 END) as thickness_count,
            SUM(CASE WHEN markdot_defect IS NOT NULL AND markdot_defect != '' THEN CAST(markdot_defect AS UNSIGNED) ELSE 0 END) as markdot_count,
            NOW() as create_time,
            NOW() as update_time,
            0 as del_flag
        FROM t_check_device_secret
        WHERE DATE(test_time) = #{reportDate}
        AND machine_sn = #{machineSn}
        ON DUPLICATE KEY UPDATE
            web_blemish_count = VALUES(web_blemish_count),
            pillar_count = VALUES(pillar_count),
            hole_count = VALUES(hole_count),
            double_line_count = VALUES(double_line_count),
            knot_count = VALUES(knot_count),
            oxidation_count = VALUES(oxidation_count),
            oil_stain_count = VALUES(oil_stain_count),
            foreign_object_count = VALUES(foreign_object_count),
            deformation_count = VALUES(deformation_count),
            crack_count = VALUES(crack_count),
            discoloration_count = VALUES(discoloration_count),
            hairiness_count = VALUES(hairiness_count),
            connectorlug_count = VALUES(connectorlug_count),
            mesh_count = VALUES(mesh_count),
            thickness_count = VALUES(thickness_count),
            markdot_count = VALUES(markdot_count),
            update_time = NOW()
    </insert>

    <!-- 计算并更新缺陷率 -->
    <update id="calculateAndUpdateDefectRates">
        UPDATE t_aoi_defect_detail d
        INNER JOIN t_aoi_daily_report_core c ON d.report_core_id = c.id
        SET 
            d.web_blemish_rate = CASE WHEN c.input_count > 0 THEN ROUND(d.web_blemish_count * 100.0 / c.input_count, 2) ELSE 0 END,
            d.pillar_rate = CASE WHEN c.input_count > 0 THEN ROUND(d.pillar_count * 100.0 / c.input_count, 2) ELSE 0 END,
            d.hole_rate = CASE WHEN c.input_count > 0 THEN ROUND(d.hole_count * 100.0 / c.input_count, 2) ELSE 0 END,
            d.double_line_rate = CASE WHEN c.input_count > 0 THEN ROUND(d.double_line_count * 100.0 / c.input_count, 2) ELSE 0 END,
            d.knot_rate = CASE WHEN c.input_count > 0 THEN ROUND(d.knot_count * 100.0 / c.input_count, 2) ELSE 0 END,
            d.oxidation_rate = CASE WHEN c.input_count > 0 THEN ROUND(d.oxidation_count * 100.0 / c.input_count, 2) ELSE 0 END,
            d.oil_stain_rate = CASE WHEN c.input_count > 0 THEN ROUND(d.oil_stain_count * 100.0 / c.input_count, 2) ELSE 0 END,
            d.foreign_object_rate = CASE WHEN c.input_count > 0 THEN ROUND(d.foreign_object_count * 100.0 / c.input_count, 2) ELSE 0 END,
            d.deformation_rate = CASE WHEN c.input_count > 0 THEN ROUND(d.deformation_count * 100.0 / c.input_count, 2) ELSE 0 END,
            d.crack_rate = CASE WHEN c.input_count > 0 THEN ROUND(d.crack_count * 100.0 / c.input_count, 2) ELSE 0 END,
            d.discoloration_rate = CASE WHEN c.input_count > 0 THEN ROUND(d.discoloration_count * 100.0 / c.input_count, 2) ELSE 0 END,
            d.hairiness_rate = CASE WHEN c.input_count > 0 THEN ROUND(d.hairiness_count * 100.0 / c.input_count, 2) ELSE 0 END,
            d.connectorlug_rate = CASE WHEN c.input_count > 0 THEN ROUND(d.connectorlug_count * 100.0 / c.input_count, 2) ELSE 0 END,
            d.mesh_rate = CASE WHEN c.input_count > 0 THEN ROUND(d.mesh_count * 100.0 / c.input_count, 2) ELSE 0 END,
            d.thickness_rate = CASE WHEN c.input_count > 0 THEN ROUND(d.thickness_count * 100.0 / c.input_count, 2) ELSE 0 END,
            d.markdot_rate = CASE WHEN c.input_count > 0 THEN ROUND(d.markdot_count * 100.0 / c.input_count, 2) ELSE 0 END,
            d.update_time = NOW()
        WHERE d.report_core_id = #{reportCoreId}
    </update>

    <!-- 计算NG前三缺陷 -->
    <select id="calculateNgTop3" resultType="map">
        SELECT 
            defect_type,
            defect_name,
            defect_count
        FROM (
            SELECT 'web_blemish' as defect_type, '网疤' as defect_name, web_blemish_count as defect_count FROM t_aoi_defect_detail WHERE report_core_id = #{reportCoreId}
            UNION ALL
            SELECT 'pillar' as defect_type, '柱道' as defect_name, pillar_count as defect_count FROM t_aoi_defect_detail WHERE report_core_id = #{reportCoreId}
            UNION ALL
            SELECT 'hole' as defect_type, '破洞' as defect_name, hole_count as defect_count FROM t_aoi_defect_detail WHERE report_core_id = #{reportCoreId}
            UNION ALL
            SELECT 'double_line' as defect_type, '双线' as defect_name, double_line_count as defect_count FROM t_aoi_defect_detail WHERE report_core_id = #{reportCoreId}
            UNION ALL
            SELECT 'knot' as defect_type, '打结' as defect_name, knot_count as defect_count FROM t_aoi_defect_detail WHERE report_core_id = #{reportCoreId}
            UNION ALL
            SELECT 'oxidation' as defect_type, '氧化' as defect_name, oxidation_count as defect_count FROM t_aoi_defect_detail WHERE report_core_id = #{reportCoreId}
            UNION ALL
            SELECT 'oil_stain' as defect_type, '油污' as defect_name, oil_stain_count as defect_count FROM t_aoi_defect_detail WHERE report_core_id = #{reportCoreId}
            UNION ALL
            SELECT 'foreign_object' as defect_type, '异物' as defect_name, foreign_object_count as defect_count FROM t_aoi_defect_detail WHERE report_core_id = #{reportCoreId}
            UNION ALL
            SELECT 'deformation' as defect_type, '变形' as defect_name, deformation_count as defect_count FROM t_aoi_defect_detail WHERE report_core_id = #{reportCoreId}
            UNION ALL
            SELECT 'crack' as defect_type, '裂口' as defect_name, crack_count as defect_count FROM t_aoi_defect_detail WHERE report_core_id = #{reportCoreId}
            UNION ALL
            SELECT 'discoloration' as defect_type, '异色' as defect_name, discoloration_count as defect_count FROM t_aoi_defect_detail WHERE report_core_id = #{reportCoreId}
            UNION ALL
            SELECT 'hairiness' as defect_type, '毛丝' as defect_name, hairiness_count as defect_count FROM t_aoi_defect_detail WHERE report_core_id = #{reportCoreId}
            UNION ALL
            SELECT 'mesh' as defect_type, '目数' as defect_name, mesh_count as defect_count FROM t_aoi_defect_detail WHERE report_core_id = #{reportCoreId}
            UNION ALL
            SELECT 'thickness' as defect_type, '厚度' as defect_name, thickness_count as defect_count FROM t_aoi_defect_detail WHERE report_core_id = #{reportCoreId}
            UNION ALL
            SELECT 'markdot' as defect_type, '标记点' as defect_name, markdot_count as defect_count FROM t_aoi_defect_detail WHERE report_core_id = #{reportCoreId}
        ) defects
        WHERE defect_count > 0
        ORDER BY defect_count DESC
        LIMIT 3
    </select>

</mapper>

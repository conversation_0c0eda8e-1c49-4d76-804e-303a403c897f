package com.boyo.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * AOI设备状态表
 * @TableName t_aoi_equipment_status
 * <AUTHOR>
 * @date 2024-01-15
 */
@TableName(value = "t_aoi_equipment_status")
@Data
public class AoiEquipmentStatus implements Serializable {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联核心日报表ID
     */
    @TableField(value = "report_core_id")
    private Long reportCoreId;

    // ===== 时间段统计字段 =====
    
    /**
     * 班次类型(白班/夜班)
     */
    @TableField(value = "shift_type")
    private String shiftType;

    /**
     * 开始时间
     */
    @TableField(value = "start_time")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField(value = "end_time")
    private Date endTime;

    /**
     * 生产时长(分钟)
     */
    @TableField(value = "production_duration")
    private Integer productionDuration;

    // ===== 设备状态字段 =====
    
    /**
     * 设备状态(运行/停机/维护)
     */
    @TableField(value = "equipment_status")
    private String equipmentStatus;

    /**
     * 停机原因
     */
    @TableField(value = "downtime_reason")
    private String downtimeReason;

    /**
     * 停机时长(分钟)
     */
    @TableField(value = "downtime_duration")
    private Integer downtimeDuration;

    // ===== 质量指标字段 =====

    /**
     * 一次通过率(%)
     */
    @TableField(value = "first_pass_yield")
    private BigDecimal firstPassYield;

    /**
     * 缺陷密度(个/万)
     */
    @TableField(value = "defect_density")
    private BigDecimal defectDensity;

    /**
     * 设备综合效率(%)
     */
    @TableField(value = "oee")
    private BigDecimal oee;

    // ===== 系统字段 =====

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 删除标志(0:正常,1:删除)
     */
    @TableField(value = "del_flag")
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

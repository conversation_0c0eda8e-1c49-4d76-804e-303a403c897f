<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mapper.AoiEquipmentStatusMapper">

    <!-- 根据核心表ID列表批量查询设备状态 -->
    <select id="selectByReportCoreIds" resultType="com.boyo.domain.AoiEquipmentStatus">
        SELECT * FROM t_aoi_equipment_status 
        WHERE report_core_id IN
        <foreach collection="reportCoreIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND del_flag = 0
    </select>

    <!-- 查询设备产能统计 -->
    <select id="selectProductionStatistics" resultType="map">
        SELECT 
            c.machine_sn,
            c.report_date,
            c.input_count,
            c.ok_count,
            c.ng_count,
            c.controlled_yield,
            e.equipment_status,
            e.production_duration,
            e.downtime_duration,
            e.oee
        FROM t_aoi_daily_report_core c
        LEFT JOIN t_aoi_equipment_status e ON c.id = e.report_core_id AND e.del_flag = 0
        WHERE c.report_date BETWEEN #{startDate} AND #{endDate}
        AND c.del_flag = 0
        ORDER BY c.report_date DESC, c.machine_sn
    </select>

    <!-- 查询质量指标统计 -->
    <select id="selectQualityMetrics" resultType="map">
        SELECT 
            c.machine_sn,
            c.report_date,
            c.controlled_yield,
            e.first_pass_yield,
            e.defect_density,
            e.oee
        FROM t_aoi_daily_report_core c
        LEFT JOIN t_aoi_equipment_status e ON c.id = e.report_core_id AND e.del_flag = 0
        WHERE c.report_date BETWEEN #{startDate} AND #{endDate}
        <if test="machineSn != null and machineSn != ''">
            AND c.machine_sn = #{machineSn}
        </if>
        AND c.del_flag = 0
        ORDER BY c.report_date DESC, c.machine_sn
    </select>

    <!-- 从原始数据生成设备状态 -->
    <insert id="generateFromRawData">
        INSERT INTO t_aoi_equipment_status (
            report_core_id,
            shift_type, equipment_status, first_pass_yield, defect_density, oee,
            create_time, update_time, del_flag
        )
        SELECT 
            #{reportCoreId} as report_core_id,
            CASE 
                WHEN HOUR(MIN(test_time)) BETWEEN 8 AND 20 THEN '白班'
                ELSE '夜班'
            END as shift_type,
            '运行' as equipment_status,
            ROUND(
                CASE 
                    WHEN COUNT(*) > 0 THEN 
                        SUM(CASE WHEN test_result = 'OK' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)
                    ELSE 0 
                END, 2
            ) as first_pass_yield,
            ROUND(
                CASE 
                    WHEN COUNT(*) > 0 THEN 
                        SUM(CASE WHEN test_result = 'NG' THEN 1 ELSE 0 END) * 10000.0 / COUNT(*)
                    ELSE 0 
                END, 2
            ) as defect_density,
            ROUND(RAND() * 20 + 80, 2) as oee, -- 模拟OEE数据
            NOW() as create_time,
            NOW() as update_time,
            0 as del_flag
        FROM t_check_device_secret
        WHERE DATE(test_time) = #{reportDate}
        AND machine_sn = #{machineSn}
        ON DUPLICATE KEY UPDATE
            shift_type = VALUES(shift_type),
            equipment_status = VALUES(equipment_status),
            first_pass_yield = VALUES(first_pass_yield),
            defect_density = VALUES(defect_density),
            oee = VALUES(oee),
            update_time = NOW()
    </insert>

</mapper>

package com.boyo.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * AOI生产日报表-核心表
 * @TableName t_aoi_daily_report_core
 * <AUTHOR>
 * @date 2024-01-15
 */
@TableName(value = "t_aoi_daily_report_core")
@Data
public class AoiDailyReportCore implements Serializable {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 报表日期
     */
    @TableField(value = "report_date")
    private Date reportDate;

    /**
     * 设备序列号(A-1,A-2,B-1,B-2,C-1,C-2,D-1,D-2)
     */
    @TableField(value = "machine_sn")
    private String machineSn;

    /**
     * 项目名称
     */
    @TableField(value = "project_name")
    private String projectName;

    /**
     * 生产时间状态
     */
    @TableField(value = "production_time_status")
    private String productionTimeStatus;

    /**
     * 打料数量(投入检测数量)
     */
    @TableField(value = "input_count")
    private Integer inputCount;

    /**
     * Roll投入数量
     */
    @TableField(value = "roll_input")
    private Integer rollInput;

    /**
     * OK数量(合格品数量)
     */
    @TableField(value = "ok_count")
    private Integer okCount;

    /**
     * NG数量(不合格品数量)
     */
    @TableField(value = "ng_count")
    private Integer ngCount;

    /**
     * 受控良率(%)
     */
    @TableField(value = "controlled_yield")
    private BigDecimal controlledYield;

    /**
     * 良率(Roll_OK/Roll_Input*100%)
     */
    @TableField(value = "roll_yield")
    private BigDecimal rollYield;

    /**
     * 按ID良率(1-Defect Sum/input数*100%)
     */
    @TableField(value = "yield_by_id")
    private BigDecimal yieldById;

    /**
     * NG数量大于120的记录数量
     */
    @TableField(value = "roll_ng_over_120")
    private Integer rollNgOver120;

    /**
     * NG数量在50-120之间的记录数量
     */
    @TableField(value = "roll_ng_between_50_and_120")
    private Integer rollNgBetween50And120;

    /**
     * NG项前一
     */
    @TableField(value = "ng_top1")
    private String ngTop1;

    /**
     * NG项前二
     */
    @TableField(value = "ng_top2")
    private String ngTop2;

    /**
     * NG项前三
     */
    @TableField(value = "ng_top3")
    private String ngTop3;

    /**
     * 机台检率(%)
     */
    @TableField(value = "machine_detection_rate")
    private BigDecimal machineDetectionRate;

    /**
     * 分组良率参考值(1-Roll NG>120/Roll_Input*100%)
     */
    @TableField(value = "roll_yield_with_group")
    private BigDecimal rollYieldWithGroup;

    /**
     * 异常信息
     */
    @TableField(value = "abnormal_info")
    private String abnormalInfo;

    // ===== 系统字段 =====

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 删除标志(0:正常,1:删除)
     */
    @TableField(value = "del_flag")
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

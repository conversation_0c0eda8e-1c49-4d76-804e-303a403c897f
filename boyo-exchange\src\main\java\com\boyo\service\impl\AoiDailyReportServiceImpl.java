package com.boyo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.domain.AoiDailyReport;
import com.boyo.mapper.AoiDailyReportMapper;
import com.boyo.service.IAoiDailyReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AOI生产日报表Service实现类
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Service
public class AoiDailyReportServiceImpl extends ServiceImpl<AoiDailyReportMapper, AoiDailyReport>
        implements IAoiDailyReportService {

    @Autowired
    private AoiDailyReportMapper aoiDailyReportMapper;

    @Override
    public AoiDailyReport getByDateAndMachine(Date reportDate, String machineSn) {
        return aoiDailyReportMapper.selectByDateAndMachine(reportDate, machineSn);
    }

    @Override
    public List<AoiDailyReport> getByDate(Date reportDate) {
        return aoiDailyReportMapper.selectByDate(reportDate);
    }

    @Override
    public List<Map<String, Object>> getByDateLight(Date reportDate) {
        return aoiDailyReportMapper.selectByDateLight(reportDate);
    }

    @Override
    public List<AoiDailyReport> getByDateRange(Date startDate, Date endDate, String machineSn) {
        return aoiDailyReportMapper.selectByDateRange(startDate, endDate, machineSn);
    }

    @Override
    public List<Map<String, Object>> getWhiteboardData(Date reportDate) {
        return aoiDailyReportMapper.selectWhiteboardData(reportDate);
    }

    @Override
    public List<Map<String, Object>> getYieldTrend(String machineSn, Integer days) {
        return aoiDailyReportMapper.selectYieldTrend(machineSn, days);
    }

    @Override
    public List<Map<String, Object>> getDefectDistribution(Date reportDate, String machineSn) {
        return aoiDailyReportMapper.selectDefectDistribution(reportDate, machineSn);
    }

    @Override
    @Transactional
    public boolean generateFromRawData(Date reportDate, String machineSn) {
        try {
            int result = aoiDailyReportMapper.generateFromRawData(reportDate, machineSn);
            if (result > 0) {
                // 查询生成的记录并计算NG前三和缺陷率
                AoiDailyReport report = getByDateAndMachine(reportDate, machineSn);
                if (report != null) {
                    calculateNgTop3(report.getId());
                    calculateDefectRates(report.getId());
                }
            }
            return result > 0;
        } catch (Exception e) {
            log.error("生成日报失败: reportDate={}, machineSn={}", reportDate, machineSn, e);
            return false;
        }
    }

    @Override
    @Transactional
    public int batchGenerateFromRawData(Date reportDate) {
        try {
            int result = aoiDailyReportMapper.batchGenerateFromRawData(reportDate);
            
            // 批量计算NG前三和缺陷率
            List<AoiDailyReport> reports = getByDate(reportDate);
            for (AoiDailyReport report : reports) {
                calculateNgTop3(report.getId());
                calculateDefectRates(report.getId());
            }
            
            return result;
        } catch (Exception e) {
            log.error("批量生成日报失败: reportDate={}", reportDate, e);
            return 0;
        }
    }

    @Override
    public List<Map<String, Object>> getProductionStatistics(Date startDate, Date endDate) {
        return aoiDailyReportMapper.selectProductionStatistics(startDate, endDate);
    }

    @Override
    public List<Map<String, Object>> getQualityMetrics(Date startDate, Date endDate, String machineSn) {
        return aoiDailyReportMapper.selectQualityMetrics(startDate, endDate, machineSn);
    }

    @Override
    @Transactional
    public boolean calculateNgTop3(Long reportId) {
        try {
            // 使用SQL查询获取NG前三缺陷
            List<Map<String, Object>> defectList = aoiDailyReportMapper.calculateNgTop3(reportId);

            if (defectList.isEmpty()) {
                log.warn("未找到缺陷数据: reportId={}", reportId);
                return false;
            }

            // 提取前三缺陷类型
            String ngTop1 = null, ngTop2 = null, ngTop3 = null;

            for (int i = 0; i < defectList.size() && i < 3; i++) {
                Map<String, Object> defect = defectList.get(i);
                String defectType = (String) defect.get("defectType");
                Integer count = ((Number) defect.get("count")).intValue();

                // 只记录数量大于0的缺陷
                if (count > 0) {
                    switch (i) {
                        case 0:
                            ngTop1 = defectType;
                            break;
                        case 1:
                            ngTop2 = defectType;
                            break;
                        case 2:
                            ngTop3 = defectType;
                            break;
                    }
                }
            }

            // 更新数据库
            int result = aoiDailyReportMapper.updateNgTop3(reportId, ngTop1, ngTop2, ngTop3);

            if (result > 0) {
                log.info("成功更新NG前三: reportId={}, ngTop1={}, ngTop2={}, ngTop3={}",
                        reportId, ngTop1, ngTop2, ngTop3);
                return true;
            } else {
                log.warn("更新NG前三失败: reportId={}", reportId);
                return false;
            }
        } catch (Exception e) {
            log.error("计算NG前三失败: reportId={}", reportId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean calculateDefectRates(Long reportId) {
        try {
            // 使用SQL直接计算并更新缺陷率
            int result = aoiDailyReportMapper.calculateAndUpdateDefectRates(reportId);

            if (result > 0) {
                log.info("成功计算并更新缺陷率: reportId={}", reportId);
                return true;
            } else {
                log.warn("计算缺陷率失败，可能是总投入量为0: reportId={}", reportId);
                return false;
            }
        } catch (Exception e) {
            log.error("计算缺陷率失败: reportId={}", reportId, e);
            return false;
        }
    }

    @Override
    public Map<String, Object> syncRawDataToReport(Date reportDate) {
        Map<String, Object> result = new HashMap<>();
        try {
            int count = batchGenerateFromRawData(reportDate);
            result.put("success", true);
            result.put("count", count);
            result.put("message", "同步成功，共处理 " + count + " 条记录");
        } catch (Exception e) {
            log.error("同步原始数据失败: reportDate={}", reportDate, e);
            result.put("success", false);
            result.put("count", 0);
            result.put("message", "同步失败: " + e.getMessage());
        }
        return result;
    }

    @Override
    public List<String> getMachineList() {
        LambdaQueryWrapper<AoiDailyReport> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(AoiDailyReport::getMachineSn)
                .groupBy(AoiDailyReport::getMachineSn)
                .orderByAsc(AoiDailyReport::getMachineSn);
        
        List<AoiDailyReport> reports = list(wrapper);
        return reports.stream()
                .map(AoiDailyReport::getMachineSn)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> exportReportData(Date startDate, Date endDate, String machineSn) {
        List<AoiDailyReport> reports = getByDateRange(startDate, endDate, machineSn);
        
        return reports.stream().map(report -> {
            Map<String, Object> data = new HashMap<>();
            data.put("reportDate", report.getReportDate());
            data.put("machineSn", report.getMachineSn());
            data.put("projectName", report.getProjectName());
            data.put("inputCount", report.getInputCount());
            data.put("okCount", report.getOkCount());
            data.put("ngCount", report.getNgCount());
            data.put("controlledYield", report.getControlledYield());
            data.put("ngTop1", report.getNgTop1());
            data.put("ngTop2", report.getNgTop2());
            data.put("ngTop3", report.getNgTop3());
            data.put("machineDetectionRate", report.getMachineDetectionRate());
            data.put("abnormalInfo", report.getAbnormalInfo());
            return data;
        }).collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> getReportSummary(Date reportDate) {
        List<AoiDailyReport> reports = getByDate(reportDate);
        
        Map<String, Object> summary = new HashMap<>();
        
        if (reports.isEmpty()) {
            summary.put("totalMachines", 0);
            summary.put("totalInput", 0);
            summary.put("totalOk", 0);
            summary.put("totalNg", 0);
            summary.put("avgYield", 0);
            return summary;
        }
        
        int totalInput = reports.stream().mapToInt(r -> r.getInputCount() != null ? r.getInputCount() : 0).sum();
        int totalOk = reports.stream().mapToInt(r -> r.getOkCount() != null ? r.getOkCount() : 0).sum();
        int totalNg = reports.stream().mapToInt(r -> r.getNgCount() != null ? r.getNgCount() : 0).sum();
        
        BigDecimal avgYield = reports.stream()
                .filter(r -> r.getControlledYield() != null)
                .map(AoiDailyReport::getControlledYield)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .divide(new BigDecimal(reports.size()), 2, RoundingMode.HALF_UP);
        
        summary.put("totalMachines", reports.size());
        summary.put("totalInput", totalInput);
        summary.put("totalOk", totalOk);
        summary.put("totalNg", totalNg);
        summary.put("avgYield", avgYield);
        summary.put("reportDate", reportDate);
        
        return summary;
    }
}

//package com.boyo.common.utils.wechat;
//
//import cn.hutool.core.util.ArrayUtil;
//import cn.hutool.core.util.StrUtil;
//import com.boyo.common.config.WxCpConfiguration;
//import com.boyo.common.core.domain.entity.SysUser;
//import me.chanjar.weixin.cp.api.WxCpService;
//import me.chanjar.weixin.cp.bean.message.WxCpMessage;
//import me.chanjar.weixin.cp.bean.templatecard.HorizontalContent;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@Component
//public class WechatMessageUtil {
//    public void sendMessageToOneUser(SysUser user, String content, String orderName, String orderCode, String msg) {
//        WxCpService wxCpService = WxCpConfiguration.getCpService(1000012);
//        try {
//            String userId = user.getWechatId();
//            if (StrUtil.isEmpty(userId)) {
//                userId = wxCpService.getUserService().getUserId(user.getPhonenumber());
//            }
//            if (StrUtil.isEmpty(userId)) {
//                return;
//            }
//            WxCpMessage wxCpMessage = new WxCpMessage();
//            wxCpMessage.setMsgType("template_card");
//            wxCpMessage.setCardType("text_notice");
//            wxCpMessage.setToUser(userId);
//            wxCpMessage.setAgentId(1000012);
//            wxCpMessage.setCardType("text_notice");
//            wxCpMessage.setMainTitleTitle("CRM操作通知");
//            wxCpMessage.setSourceDesc("CRM系统");
//            wxCpMessage.setEmphasisContentTitle(content);
//            wxCpMessage.setEmphasisContentDesc("操作信息");
//            List<HorizontalContent> horizontalContents = new ArrayList<>();
//            {
//                HorizontalContent horizontalContent = new HorizontalContent();
//                horizontalContent.setKeyname("订单名称");
//                horizontalContent.setValue(orderName);
//                horizontalContents.add(horizontalContent);
//            }
//            {
//                HorizontalContent horizontalContent = new HorizontalContent();
//                horizontalContent.setKeyname("订单编号");
//                horizontalContent.setValue(orderCode);
//                horizontalContents.add(horizontalContent);
//            }
//            if (StrUtil.isNotEmpty(msg)) {
//                HorizontalContent horizontalContent = new HorizontalContent();
//                horizontalContent.setKeyname("备注");
//                horizontalContent.setValue(msg);
//                horizontalContents.add(horizontalContent);
//            }
//            wxCpMessage.setHorizontalContents(horizontalContents);
//            wxCpMessage.setCardActionType(1);
//            wxCpMessage.setCardActionUrl("http://www.baidu.com");
//            wxCpService.getMessageService().send(wxCpMessage);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    public void sendMsgToUsers(List<String> userIds, String content) {
//        WxCpService wxCpService = WxCpConfiguration.getCpService(1000012);
//        try {
//
//            WxCpMessage wxCpMessage = new WxCpMessage();
//            wxCpMessage.setMsgType("text");
//            wxCpMessage.setToUser(ArrayUtil.join(userIds.toArray(), "|"));
//            wxCpMessage.setContent(content);
//            wxCpService.getMessageService().send(wxCpMessage);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//}

package com.boyo.web.controller.system;

import cn.dev33.satoken.annotation.SaIgnore;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.constant.Constants;
import com.boyo.common.core.domain.R;
import com.boyo.common.core.domain.entity.SysMenu;
import com.boyo.common.core.domain.entity.SysRole;
import com.boyo.common.core.domain.entity.SysUser;
import com.boyo.common.core.domain.model.LoginBody;
import com.boyo.common.core.domain.model.LoginUser;
import com.boyo.common.core.domain.model.SmsLoginBody;
import com.boyo.common.helper.LoginHelper;
import com.boyo.common.storage.TenantStorage;
import com.boyo.system.domain.TenantUser;
import com.boyo.system.domain.UserRole;
import com.boyo.system.domain.vo.RouterVo;
import com.boyo.system.mapper.SysRoleMapper;
import com.boyo.system.mapper.TenantUserMapper;
import com.boyo.system.mapper.UserRoleMapper;
import com.boyo.system.service.*;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 登录验证
 *
 * <AUTHOR> Li
 */
@Validated
@RequiredArgsConstructor
@RestController
public class SysLoginController {

    private final SysLoginService loginService;
    private final ISysMenuService menuService;
    private final ISysUserService userService;
    private final UserRoleMapper userRoleMapper;
    private final SysRoleMapper roleMapper;
    private final SysPermissionService permissionService;
    private final TenantUserMapper tenantUserMapper;



    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/login")
    public R<Map<String, Object>> login(@Validated @RequestBody LoginBody loginBody) {
        Map<String, Object> ajax = new HashMap<>();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
            loginBody.getUuid(), loginBody.getType(), loginBody);
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }

    /**
     * 短信登录(示例)
     *
     * @param smsLoginBody 登录信息
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/smsLogin")
    public R<Map<String, Object>> smsLogin(@Validated @RequestBody SmsLoginBody smsLoginBody) {
        Map<String, Object> ajax = new HashMap<>();
        // 生成令牌
        String token = loginService.smsLogin(smsLoginBody.getPhonenumber(), smsLoginBody.getSmsCode());
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }

    /**
     * @param code 小程序code
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/xcxLogin")
    public R<Map<String, Object>> xcxLogin(@RequestBody String code) {
        Map<String, Object> ajax = new HashMap<>();
        // 生成令牌

        String token = null;
        try {
            token = loginService.xcxLogin(code);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }

    /**
     * 退出登录
     */
    @SaIgnore
    @PostMapping("/logout")
    public R<Void> logout() {
        loginService.logout();
        return R.ok("退出成功");
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public R<Map<String, Object>> getInfo() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        SysUser user = userService.selectUserById(loginUser.getUserId());
        Map<String, Object> ajax = new HashMap<>();
        ajax.put("user", user);
        ajax.put("roles", permissionService.getRolePermission(user));
        ajax.put("permissions", permissionService.getMenuPermission(user));
//        loginUser.setMenuPermission(permissionService.getMenuPermission(user));
//        loginUser.setRolePermission(permissionService.getRolePermission(user));
        try {
            if (TenantStorage.getTenantId() != null) {
                List<UserRole> roleList = userRoleMapper.selectList(new QueryWrapper<UserRole>().eq("user_id", loginUser.getUserId()));
                if (roleList != null && roleList.size() > 0) {
                    List<Long> ids = roleList.stream().map(UserRole::getRoleId).collect(Collectors.toList());
                    ajax.put("appRoles", roleMapper.selectBatchIds(ids).stream().map(SysRole::getRoleKey).collect(Collectors.toList()));
                } else {
                    ajax.put("appRoles", null);
                }
            } else {
                ajax.put("appRoles", null);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.ok(ajax);
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public R<List<RouterVo>> getRouters() {
        if (LoginHelper.getLoginUser().getUserType().equals("sys_user")) {
            Long userId = LoginHelper.getUserId();
            List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
            return R.ok(menuService.buildMenus(menus));
        } else {
            Long userId = LoginHelper.getUserId();
            if (TenantStorage.getTenantId() != null) {

            } else {
                List<TenantUser> tenantUserList = tenantUserMapper.listUserTenant(userId);
                if (tenantUserList != null && tenantUserList.size() > 0) {
                    TenantStorage.setTenantId(tenantUserList.get(0).getTenantId());
                }
            }
            List<SysMenu> menus = menuService.selectMenuTreeCustomer(userId);
            return R.ok(menuService.buildMenus(menus));
        }
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mapper.AoiDailyReportCoreMapper">

    <!-- 通用结果映射 -->
    <resultMap id="BaseResultMap" type="com.boyo.domain.AoiDailyReportCore">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="report_date" property="reportDate" jdbcType="DATE"/>
        <result column="machine_sn" property="machineSn" jdbcType="VARCHAR"/>
        <result column="project_name" property="projectName" jdbcType="VARCHAR"/>
        <result column="production_time_status" property="productionTimeStatus" jdbcType="VARCHAR"/>
        <result column="input_count" property="inputCount" jdbcType="INTEGER"/>
        <result column="production_count" property="rollInput" jdbcType="INTEGER"/>
        <result column="ok_count" property="okCount" jdbcType="INTEGER"/>
        <result column="ng_count" property="ngCount" jdbcType="INTEGER"/>
        <result column="controlled_yield" property="controlledYield" jdbcType="DECIMAL"/>
        <result column="roll_yield" property="rollYield" jdbcType="DECIMAL"/>
        <result column="yield_by_id" property="yieldById" jdbcType="DECIMAL"/>
        <result column="roll_ng_over_120" property="rollNgOver120" jdbcType="INTEGER"/>
        <result column="roll_ng_between_50_and_120" property="rollNgBetween50And120" jdbcType="INTEGER"/>
        <result column="ng_top1" property="ngTop1" jdbcType="VARCHAR"/>
        <result column="ng_top2" property="ngTop2" jdbcType="VARCHAR"/>
        <result column="ng_top3" property="ngTop3" jdbcType="VARCHAR"/>
        <result column="machine_detection_rate" property="machineDetectionRate" jdbcType="DECIMAL"/>
        <result column="roll_yield_with_group" property="rollYieldWithGroup" jdbcType="DECIMAL"/>
        <result column="abnormal_info" property="abnormalInfo" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="del_flag" property="delFlag" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 查询白板显示数据 -->
    <select id="selectWhiteboardData" resultType="map">
        SELECT 
            machine_sn as machineSn,
            production_time_status as productionTimeStatus,
            input_count as inputCount,
            roll_input as rollInput,
            ok_count as okCount,
            ng_count as ngCount,
            controlled_yield as controlledYield,
            roll_yield as rollYield,
            yield_by_id as yieldById,
            roll_ng_over_120 as rollNgOver120,
            roll_ng_between_50_and_120 as rollNgBetween50And120,
            ng_top1 as ngTop1,
            ng_top2 as ngTop2,
            ng_top3 as ngTop3,
            machine_detection_rate as machineDetectionRate,
            abnormal_info as abnormalInfo
        FROM t_aoi_daily_report_core
        WHERE report_date = #{reportDate} AND del_flag = 0
        ORDER BY machine_sn
    </select>

    <!-- 根据日期范围查询核心日报 -->
    <select id="selectByDateRange" resultMap="BaseResultMap">
        SELECT * FROM t_aoi_daily_report_core
        WHERE report_date BETWEEN #{startDate} AND #{endDate}
        <if test="machineSn != null and machineSn != ''">
            AND machine_sn = #{machineSn}
        </if>
        AND del_flag = 0
        ORDER BY report_date DESC, machine_sn
    </select>

    <!-- 从原始数据生成核心日报 -->
    <insert id="generateFromRawData">
        INSERT INTO t_aoi_daily_report_core (
            report_date, machine_sn, project_name, production_time_status,
            input_count, roll_input, ok_count, ng_count, controlled_yield, roll_yield, yield_by_id,
            roll_ng_over_120, roll_ng_between_50_and_120,
            machine_detection_rate, roll_yield_with_group, abnormal_info,
            create_time, update_time, create_by, del_flag
        )
        SELECT
            #{reportDate} as report_date,
            #{machineSn} as machine_sn,
            project_name,
            '正常' as production_time_status,
            SUM(CASE WHEN total REGEXP '^[0-9]+$' THEN CAST(total AS UNSIGNED) ELSE 0 END) as input_count,
            COUNT(*) as roll_input,
            SUM(CASE WHEN test_result IN ('OK', 'Pass', 'PASS') THEN 1 ELSE 0 END) as ok_count,
            SUM(CASE WHEN test_result IN ('NG', 'Fail', 'FAIL') THEN 1 ELSE 0 END) as ng_count,
            ROUND(
                CASE
                    WHEN COUNT(*) &gt; 0 THEN
                        SUM(CASE WHEN test_result IN ('OK', 'Pass', 'PASS') THEN 1 ELSE 0 END) * 100.0 / COUNT(*)
                    ELSE 0
                END, 2
            ) as controlled_yield,
            ROUND(
                CASE
                    WHEN COUNT(*) &gt; 0 THEN
                        SUM(CASE WHEN test_result IN ('OK', 'Pass', 'PASS') THEN 1 ELSE 0 END) * 100.0 / COUNT(*)
                    ELSE 0
                END, 2
            ) as roll_yield,
            0.00 as yield_by_id,
            SUM(CASE WHEN ng_count &gt; 120 THEN 1 ELSE 0 END) as roll_ng_over_120,
            SUM(CASE WHEN ng_count &gt;= 50 AND ng_count &lt;= 120 THEN 1 ELSE 0 END) as roll_ng_between_50_and_120,
            ROUND(
                CASE
                    WHEN COUNT(*) &gt; 0 THEN
                        (SUM(CASE WHEN test_result IN ('NG', 'Fail', 'FAIL') THEN 1 ELSE 0 END) * 100.0 / COUNT(*))
                    ELSE 0
                END, 2
            ) as machine_detection_rate,
            ROUND(
                CASE
                    WHEN COUNT(*) &gt; 0 THEN
                        100.0 - (SUM(CASE WHEN ng_count &gt; 120 THEN 1 ELSE 0 END) * 100.0 / COUNT(*))
                    ELSE 100.0
                END, 2
            ) as roll_yield_with_group,
            CASE
                WHEN SUM(CASE WHEN test_result IN ('NG', 'Fail', 'FAIL') THEN 1 ELSE 0 END) &gt; COUNT(*) * 0.05
                THEN '缺陷率偏高，需要关注'
                ELSE NULL
            END as abnormal_info,
            NOW() as create_time,
            NOW() as update_time,
            'SYSTEM' as create_by,
            0 as del_flag
        FROM t_check_device_secret
        WHERE DATE(test_time) = #{reportDate}
        AND machine_sn = #{machineSn}
        GROUP BY machine_sn, project_name
        ON DUPLICATE KEY UPDATE
            input_count = VALUES(input_count),
            roll_input = VALUES(roll_input),
            ok_count = VALUES(ok_count),
            ng_count = VALUES(ng_count),
            controlled_yield = VALUES(controlled_yield),
            roll_yield = VALUES(roll_yield),
            yield_by_id = VALUES(yield_by_id),
            roll_ng_over_120 = VALUES(roll_ng_over_120),
            roll_ng_between_50_and_120 = VALUES(roll_ng_between_50_and_120),
            machine_detection_rate = VALUES(machine_detection_rate),
            roll_yield_with_group = VALUES(roll_yield_with_group),
            abnormal_info = VALUES(abnormal_info),
            update_time = NOW(),
            update_by = 'SYSTEM'
    </insert>

    <!-- 批量生成核心日报 -->
    <insert id="batchGenerateFromRawData">
        INSERT INTO t_aoi_daily_report_core (
            report_date, machine_sn, project_name, production_time_status,
            input_count, roll_input, ok_count, ng_count, controlled_yield, roll_yield, yield_by_id,
            roll_ng_over_120, roll_ng_between_50_and_120,
            machine_detection_rate, roll_yield_with_group, abnormal_info,
            create_time, update_time, create_by, del_flag
        )
        SELECT
            #{reportDate} as report_date,
            machine_sn,
            project_name,
            '正常' as production_time_status,
            SUM(CASE WHEN total REGEXP '^[0-9]+$' THEN CAST(total AS UNSIGNED) ELSE 0 END) as input_count,
            COUNT(*) as roll_input,
            SUM(CASE WHEN test_result = 'OK' THEN 1 ELSE 0 END) as ok_count,
            SUM(CASE WHEN test_result = 'NG' THEN 1 ELSE 0 END) as ng_count,
            ROUND(
                CASE
                    WHEN COUNT(*) &gt; 0 THEN
                        SUM(CASE WHEN test_result = 'OK' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)
                    ELSE 0
                END, 2
            ) as controlled_yield,
            ROUND(
                CASE
                    WHEN COUNT(*) &gt; 0 THEN
                        SUM(CASE WHEN test_result = 'OK' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)
                    ELSE 0
                END, 2
            ) as roll_yield,
            0.00 as yield_by_id,
            SUM(CASE WHEN ng_count &gt; 120 THEN 1 ELSE 0 END) as roll_ng_over_120,
            SUM(CASE WHEN ng_count &gt;= 50 AND ng_count &lt;= 120 THEN 1 ELSE 0 END) as roll_ng_between_50_and_120,
            ROUND(
                CASE 
                    WHEN COUNT(*) &gt; 0 THEN
                        (SUM(CASE WHEN test_result = 'NG' THEN 1 ELSE 0 END) * 100.0 / COUNT(*))
                    ELSE 0 
                END, 2
            ) as machine_detection_rate,
            ROUND(
                CASE
                    WHEN COUNT(*) &gt; 0 THEN
                        100.0 - (SUM(CASE WHEN ng_count &gt; 120 THEN 1 ELSE 0 END) * 100.0 / COUNT(*))
                    ELSE 100.0
                END, 2
            ) as roll_yield_with_group,
            CASE
                WHEN SUM(CASE WHEN test_result = 'NG' THEN 1 ELSE 0 END) &gt; COUNT(*) * 0.05
                THEN '缺陷率偏高，需要关注'
                ELSE NULL
            END as abnormal_info,
            NOW() as create_time,
            NOW() as update_time,
            'SYSTEM' as create_by,
            0 as del_flag
        FROM t_check_device_secret
        WHERE DATE(test_time) = #{reportDate}
        GROUP BY machine_sn, project_name
        ON DUPLICATE KEY UPDATE
            input_count = VALUES(input_count),
            roll_input = VALUES(roll_input),
            ok_count = VALUES(ok_count),
            ng_count = VALUES(ng_count),
            controlled_yield = VALUES(controlled_yield),
            roll_yield = VALUES(roll_yield),
            yield_by_id = VALUES(yield_by_id),
            roll_ng_over_120 = VALUES(roll_ng_over_120),
            roll_ng_between_50_and_120 = VALUES(roll_ng_between_50_and_120),
            machine_detection_rate = VALUES(machine_detection_rate),
            roll_yield_with_group = VALUES(roll_yield_with_group),
            abnormal_info = VALUES(abnormal_info),
            update_time = NOW(),
            update_by = 'SYSTEM'
    </insert>

    <!-- 更新NG前三 -->
    <update id="updateNgTop3">
        UPDATE t_aoi_daily_report_core
        SET ng_top1 = #{ngTop1},
            ng_top2 = #{ngTop2},
            ng_top3 = #{ngTop3},
            update_time = NOW(),
            update_by = 'SYSTEM'
        WHERE id = #{id}
    </update>

    <!-- 更新按ID良率 -->
    <update id="updateYieldById">
        UPDATE t_aoi_daily_report_core core
        LEFT JOIN t_aoi_defect_detail defect ON core.id = defect.report_core_id AND defect.del_flag = 0
        SET core.yield_by_id = CASE
            WHEN core.input_count &gt; 0 THEN
                ROUND(100.0 - (
                    (IFNULL(defect.web_blemish_count, 0) +
                     IFNULL(defect.pillar_count, 0) +
                     IFNULL(defect.hole_count, 0) +
                     IFNULL(defect.double_line_count, 0) +
                     IFNULL(defect.knot_count, 0) +
                     IFNULL(defect.oxidation_count, 0) +
                     IFNULL(defect.oil_stain_count, 0) +
                     IFNULL(defect.foreign_object_count, 0) +
                     IFNULL(defect.deformation_count, 0) +
                     IFNULL(defect.crack_count, 0) +
                     IFNULL(defect.discoloration_count, 0) +
                     IFNULL(defect.hairiness_count, 0) +
                     IFNULL(defect.connectorlug_count, 0)
                    ) * 100.0 / core.input_count
                ), 2)
            ELSE 100.00
        END,
        core.update_time = NOW(),
        core.update_by = 'SYSTEM'
        WHERE core.id = #{coreId}
    </update>

    <!-- 查询前端表格完整数据 -->
    <select id="selectTableData" resultType="java.util.Map">
        SELECT
            -- 核心表字段
            core.id,
            core.report_date AS reportDate,
            core.machine_sn AS machineSn,
            core.project_name AS projectName,
            core.production_time_status AS productionTimeStatus,
            core.input_count AS inputCount,
            core.roll_input AS rollInput,
            core.ok_count AS okCount,
            core.ng_count AS ngCount,
            core.controlled_yield AS controlledYield,
            core.roll_yield AS rollYield,
            core.yield_by_id AS yieldById,
            core.roll_ng_over_120 AS rollNgOver120,
            core.roll_ng_between_50_and_120 AS rollNgBetween50And120,
            IFNULL(core.ng_top1, '暂无') AS ngTop1,
            IFNULL(core.ng_top2, '暂无') AS ngTop2,
            IFNULL(core.ng_top3, '暂无') AS ngTop3,
            core.machine_detection_rate AS machineDetectionRate,
            core.roll_yield_with_group AS rollYieldWithGroup,
            core.abnormal_info AS abnormalInfo,

            -- 缺陷详情字段
            IFNULL(defect.web_blemish_count, 0) AS webBlemishCount,
            IFNULL(defect.pillar_count, 0) AS pillarCount,
            IFNULL(defect.hole_count, 0) AS holeCount,
            IFNULL(defect.double_line_count, 0) AS doubleLineCount,
            IFNULL(defect.knot_count, 0) AS knotCount,
            IFNULL(defect.oxidation_count, 0) AS oxidationCount,
            IFNULL(defect.oil_stain_count, 0) AS oilStainCount,
            IFNULL(defect.foreign_object_count, 0) AS foreignObjectCount,
            IFNULL(defect.deformation_count, 0) AS deformationCount,
            IFNULL(defect.crack_count, 0) AS crackCount,
            IFNULL(defect.discoloration_count, 0) AS discolorationCount,
            IFNULL(defect.hairiness_count, 0) AS hairinessCount,
            IFNULL(defect.connectorlug_count, 0) AS connectorlugCount,

            -- 缺陷率字段
            IFNULL(defect.web_blemish_rate, 0.00) AS webBlemishRate,
            IFNULL(defect.pillar_rate, 0.00) AS pillarRate,
            IFNULL(defect.hole_rate, 0.00) AS holeRate,
            IFNULL(defect.double_line_rate, 0.00) AS doubleLineRate,
            IFNULL(defect.knot_rate, 0.00) AS knotRate,
            IFNULL(defect.oxidation_rate, 0.00) AS oxidationRate,
            IFNULL(defect.oil_stain_rate, 0.00) AS oilStainRate,
            IFNULL(defect.foreign_object_rate, 0.00) AS foreignObjectRate,
            IFNULL(defect.deformation_rate, 0.00) AS deformationRate,
            IFNULL(defect.crack_rate, 0.00) AS crackRate,
            IFNULL(defect.discoloration_rate, 0.00) AS discolorationRate,
            IFNULL(defect.hairiness_rate, 0.00) AS hairinessRate,
            IFNULL(defect.connectorlug_rate, 0.00) AS connectorlugRate

        FROM t_aoi_daily_report_core core
        LEFT JOIN t_aoi_defect_detail defect ON core.id = defect.report_core_id AND defect.del_flag = 0

        WHERE core.report_date = #{reportDate}
        AND core.del_flag = 0
        <if test="machineSn != null and machineSn != ''">
            AND core.machine_sn = #{machineSn}
        </if>
        ORDER BY core.machine_sn
    </select>

    <!-- 分页查询前端表格完整数据 -->
    <select id="selectTableDataWithPaging" resultType="java.util.Map">
        SELECT
            -- 核心表字段
            core.id,
            core.report_date AS reportDate,
            core.machine_sn AS machineSn,
            core.project_name AS projectName,
            core.production_time_status AS productionTimeStatus,
            core.input_count AS inputCount,
            core.roll_input AS rollInput,
            core.ok_count AS okCount,
            core.ng_count AS ngCount,
            core.controlled_yield AS controlledYield,
            core.roll_yield AS rollYield,
            core.yield_by_id AS yieldById,
            core.roll_ng_over_120 AS rollNgOver120,
            core.roll_ng_between_50_and_120 AS rollNgBetween50And120,
            IFNULL(core.ng_top1, '暂无') AS ngTop1,
            IFNULL(core.ng_top2, '暂无') AS ngTop2,
            IFNULL(core.ng_top3, '暂无') AS ngTop3,
            core.machine_detection_rate AS machineDetectionRate,
            core.roll_yield_with_group AS rollYieldWithGroup,
            core.abnormal_info AS abnormalInfo,

            -- 缺陷详情字段
            IFNULL(defect.web_blemish_count, 0) AS webBlemishCount,
            IFNULL(defect.pillar_count, 0) AS pillarCount,
            IFNULL(defect.hole_count, 0) AS holeCount,
            IFNULL(defect.double_line_count, 0) AS doubleLineCount,
            IFNULL(defect.knot_count, 0) AS knotCount,
            IFNULL(defect.oxidation_count, 0) AS oxidationCount,
            IFNULL(defect.oil_stain_count, 0) AS oilStainCount,
            IFNULL(defect.foreign_object_count, 0) AS foreignObjectCount,
            IFNULL(defect.deformation_count, 0) AS deformationCount,
            IFNULL(defect.crack_count, 0) AS crackCount,
            IFNULL(defect.discoloration_count, 0) AS discolorationCount,
            IFNULL(defect.hairiness_count, 0) AS hairinessCount,
            IFNULL(defect.connectorlug_count, 0) AS connectorlugCount,
            IFNULL(defect.mesh_count, 0) AS meshCount,
            IFNULL(defect.thickness_count, 0) AS thicknessCount,
            IFNULL(defect.markdot_count, 0) AS markdotCount,

            -- 缺陷率字段
            IFNULL(defect.web_blemish_rate, 0.00) AS webBlemishRate,
            IFNULL(defect.pillar_rate, 0.00) AS pillarRate,
            IFNULL(defect.hole_rate, 0.00) AS holeRate,
            IFNULL(defect.double_line_rate, 0.00) AS doubleLineRate,
            IFNULL(defect.knot_rate, 0.00) AS knotRate,
            IFNULL(defect.oxidation_rate, 0.00) AS oxidationRate,
            IFNULL(defect.oil_stain_rate, 0.00) AS oilStainRate,
            IFNULL(defect.foreign_object_rate, 0.00) AS foreignObjectRate,
            IFNULL(defect.deformation_rate, 0.00) AS deformationRate,
            IFNULL(defect.crack_rate, 0.00) AS crackRate,
            IFNULL(defect.discoloration_rate, 0.00) AS discolorationRate,
            IFNULL(defect.hairiness_rate, 0.00) AS hairinessRate,
            IFNULL(defect.connectorlug_rate, 0.00) AS connectorlugRate,
            IFNULL(defect.mesh_rate, 0.00) AS meshRate,
            IFNULL(defect.thickness_rate, 0.00) AS thicknessRate,
            IFNULL(defect.markdot_rate, 0.00) AS markdotRate,

            -- 统计汇总字段
            IFNULL(stats.avg_yield, 0.00) AS avgYield

        FROM t_aoi_daily_report_core core
        LEFT JOIN t_aoi_defect_detail defect ON core.id = defect.report_core_id AND defect.del_flag = 0
        LEFT JOIN t_aoi_statistics_summary stats ON core.id = stats.report_core_id AND stats.del_flag = 0

        WHERE core.del_flag = 0
        <if test="reportDate != null">
            AND core.report_date = #{reportDate}
        </if>
        <if test="machineSn != null and machineSn != ''">
            AND core.machine_sn = #{machineSn}
        </if>
        <if test="projectName != null and projectName != ''">
            AND core.project_name = #{projectName}
        </if>
        ORDER BY core.project_name, core.report_date DESC, core.machine_sn
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 统计表格数据总数 -->
    <select id="countTableData" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_aoi_daily_report_core core
        WHERE core.del_flag = 0
        <if test="reportDate != null">
            AND core.report_date = #{reportDate}
        </if>
        <if test="machineSn != null and machineSn != ''">
            AND core.machine_sn = #{machineSn}
        </if>
        <if test="projectName != null and projectName != ''">
            AND core.project_name = #{projectName}
        </if>
    </select>

    <!-- 统计NG数量分级数据 -->
    <select id="countNgLevelStatistics" resultType="java.util.Map">
        SELECT
            SUM(core.roll_ng_over_120) as rollNgOver120,
            SUM(core.roll_ng_between_50_and_120) as rollNgBetween50And120,
            COUNT(*) as totalRecords,
            ROUND(AVG(core.controlled_yield), 2) as avgYield
        FROM t_aoi_daily_report_core core
        WHERE core.del_flag = 0
        <if test="reportDate != null">
            AND core.report_date = #{reportDate}
        </if>
        <if test="machineSn != null and machineSn != ''">
            AND core.machine_sn = #{machineSn}
        </if>
        <if test="projectName != null and projectName != ''">
            AND core.project_name = #{projectName}
        </if>
    </select>

</mapper>

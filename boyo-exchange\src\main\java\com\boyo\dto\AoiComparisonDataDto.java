package com.boyo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * AOI对比数据DTO
 * 用于返回多个版本/批次的对比数据
 */
@Data
public class AoiComparisonDataDto {
    

    
    /**
     * 累计良率VS4
     */
    private String accumYieldVs4;
    
    /**
     * 累计良率VS3
     */
    private String accumYieldVs3;
    
    /**
     * SH (可能是班次或其他标识)
     */
    private String sh;
    
    /**
     * 投入VS3
     */
    private Integer inputVs3;
    
    /**
     * 缺陷总和VS3
     */
    private Integer defectSumVs3;
    
    /**
     * Roll_OK_VS3
     */
    private Integer rollOkVs3;
    
    /**
     * Roll_NG_VS3
     */
    private Integer rollNgVs3;
    
    /**
     * Roll_Input_VS3
     */
    private Integer rollInputVs3;
    
    /**
     * Roll_Yield_VS3
     */
    private String rollYieldVs3;
    
    /**
     * 投入VS4
     */
    private Integer inputVs4;
    
    /**
     * 缺陷总和VS4
     */
    private Integer defectSumVs4;
    
    /**
     * Roll_OK_VS4
     */
    private Integer rollOkVs4;
    
    /**
     * Roll_NG_VS4
     */
    private Integer rollNgVs4;
    
    /**
     * Roll_Input_VS4
     */
    private Integer rollInputVs4;
    
    /**
     * Roll_Yield_VS4
     */
    private String rollYieldVs4;
    
    /**
     * 基础信息
     */
    private Long id;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date reportDate;
    
    private String machineSn;
    private String projectName;
    
    /**
     * 版本对比数据构造器
     */
    public static class ComparisonBuilder {
        private AoiComparisonDataDto dto = new AoiComparisonDataDto();
        

        
        public ComparisonBuilder vs3Data(Integer input, Integer defectSum, Integer rollOk, 
                                       Integer rollNg, Integer rollInput, String rollYield, String accumYield) {
            dto.setInputVs3(input);
            dto.setDefectSumVs3(defectSum);
            dto.setRollOkVs3(rollOk);
            dto.setRollNgVs3(rollNg);
            dto.setRollInputVs3(rollInput);
            dto.setRollYieldVs3(rollYield);
            dto.setAccumYieldVs3(accumYield);
            return this;
        }
        
        public ComparisonBuilder vs4Data(Integer input, Integer defectSum, Integer rollOk, 
                                       Integer rollNg, Integer rollInput, String rollYield, String accumYield) {
            dto.setInputVs4(input);
            dto.setDefectSumVs4(defectSum);
            dto.setRollOkVs4(rollOk);
            dto.setRollNgVs4(rollNg);
            dto.setRollInputVs4(rollInput);
            dto.setRollYieldVs4(rollYield);
            dto.setAccumYieldVs4(accumYield);
            return this;
        }
        
        public ComparisonBuilder baseInfo(Long id, Date reportDate, String machineSn, String projectName, String sh) {
            dto.setId(id);
            dto.setReportDate(reportDate);
            dto.setMachineSn(machineSn);
            dto.setProjectName(projectName);
            dto.setSh(sh);
            return this;
        }
        
        public AoiComparisonDataDto build() {
            return dto;
        }
    }
    
    public static ComparisonBuilder builder() {
        return new ComparisonBuilder();
    }
}

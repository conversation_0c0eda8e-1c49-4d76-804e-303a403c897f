package com.boyo.wms.service;

import com.boyo.wms.domain.TMaterialRequisitionDetailsCode;
import com.boyo.wms.domain.vo.TMaterialRequisitionDetailsCodeVo;
import com.boyo.wms.domain.bo.TMaterialRequisitionDetailsCodeBo;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 领料单详情物料Service接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface ITMaterialRequisitionDetailsCodeService {

    /**
     * 查询领料单详情物料
     */
    TMaterialRequisitionDetailsCodeVo queryById(Long id);

    /**
     * 查询领料单详情物料列表
     */
    TableDataInfo<TMaterialRequisitionDetailsCodeVo> queryPageList(TMaterialRequisitionDetailsCodeBo bo, PageQuery pageQuery);

    /**
     * 查询领料单详情物料列表
     */
    List<TMaterialRequisitionDetailsCodeVo> queryList(TMaterialRequisitionDetailsCodeBo bo);

    /**
     * 新增领料单详情物料
     */
    Boolean insertByBo(TMaterialRequisitionDetailsCodeBo bo);

    /**
     * 修改领料单详情物料
     */
    Boolean updateByBo(TMaterialRequisitionDetailsCodeBo bo);

    /**
     * 校验并批量删除领料单详情物料信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}

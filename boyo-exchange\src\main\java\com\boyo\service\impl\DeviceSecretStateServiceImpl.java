package com.boyo.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.domain.DeviceSecretState;
import com.boyo.mapper.DeviceSecretStateMapper;
import com.boyo.service.DeviceSecretStateService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【t_device_secret_state(保密设备状态)】的数据库操作Service实现
* @createDate 2025-06-09 14:16:41
*/
@Service
public class DeviceSecretStateServiceImpl extends ServiceImpl<DeviceSecretStateMapper, DeviceSecretState>
    implements DeviceSecretStateService {

}





FROM registry.cn-qingdao.aliyuncs.com/boyocloud/alpine-java
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone

RUN mkdir -p /boyo/server/logs \
    /boyo/server/temp \
    /boyo/skywalking/agent

WORKDIR /boyo/server

ENV SERVER_PORT=8080

EXPOSE ${SERVER_PORT}

ADD ./target/boyo-admin.jar ./app.jar

ENTRYPOINT ["java", \
            "-Djava.security.egd=file:/dev/./urandom", \
            "-Dserver.port=${SERVER_PORT}", \
            "-Dspring.profiles.active=prod", \
            # 应用名称 如果想区分集群节点监控 改成不同的名称即可
#            "-Dskywalking.agent.service_name=boyo-server", \
#            "-javaagent:/boyo/skywalking/agent/skywalking-agent.jar", \
            "-jar", "app.jar"]

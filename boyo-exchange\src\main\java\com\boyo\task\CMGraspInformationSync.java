package com.boyo.task;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.boyo.client.CMGraspApiUtils;
import com.boyo.order.domain.BaseMaterial;
import com.boyo.order.domain.Bom;
import com.boyo.order.domain.BomDetail;
import com.boyo.order.domain.dto.BaseMaterialDto;
import com.boyo.order.domain.dto.BomDetailDto;
import com.boyo.order.domain.dto.BomDto;
import com.boyo.order.mapper.BaseMaterialMapper;
import com.boyo.order.mapper.BomDetailMapper;
import com.boyo.order.mapper.BomMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27 14:13
 */
@Slf4j
@Component
public class CMGraspInformationSync {
    @Autowired
    private CMGraspApiUtils cmGraspApiUtils;
    @Resource
    private BomMapper bomMapper;
    @Resource
    private BomDetailMapper bomDetailMapper;
    @Resource
    private BaseMaterialMapper baseMaterialMapper;


}

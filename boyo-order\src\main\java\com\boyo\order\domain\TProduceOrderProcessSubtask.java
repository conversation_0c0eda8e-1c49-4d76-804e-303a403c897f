package com.boyo.order.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.boyo.common.core.domain.BaseEntity;

import java.util.Date;


/**
 * 工单工序子任务对象 t_produce_order_process_subtask
 *
 * <AUTHOR>
 * @date 2025-06-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_produce_order_process_subtask")
public class TProduceOrderProcessSubtask extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 设备派工id
     */
    private Long orderEquipmentId;
    /**
     * 工单工序id
     */
    private Long produceOrderProcessId;
    /**
     * 报工人，为空时不限制
     */
    private String processUsers;
    /**
     * 工序状态
     */
    private String processStatus;
    /**
     * 编码
     */
    private String rollCode;
    /**
     * 所属企业
     */
    private Long tenantId;

    /**
     * 设备台账id
     */
    private Long equipmentLedgerId;

    /**
     * 子任务计划生产数
     */
    private Long subtaskCount;

    /**
     * 计划生产经轴长度
     */
    private Long spindleLength;

    @TableField(exist = false)
    private String processName;

    @TableField(exist = false)
    private Double reportCount;

    @TableField(exist = false)
    private Double imperfectCount;

    @TableField(exist = false)
    private String userNames;

    @TableField(exist = false)
    private String orderCode;
    @TableField(exist = false)
    private String materialName;
    @TableField(exist = false)
    private Double orderCount;
    @TableField(exist = false)
    private Date orderStart;
    @TableField(exist = false)
    private Date orderEnd;
    @TableField(exist = false)
    private String workshopName;
    @TableField(exist = false)
    private String equipmentName;
    @TableField(exist = false)
    private String billCode;
    @TableField(exist = false)
    private String materialId;
    @TableField(exist = false)
    private String materialCode;
    @TableField(exist = false)
    private Date planStart;
    @TableField(exist = false)
    private Date planEnd;
    @TableField(exist = false)
    private String materialNorms;
    @TableField(exist = false)
    private String equipmentCode;

    public Boolean isTrue(){
        if (this.processStatus.equals("04"))
            return true;
        return false;
    }

}

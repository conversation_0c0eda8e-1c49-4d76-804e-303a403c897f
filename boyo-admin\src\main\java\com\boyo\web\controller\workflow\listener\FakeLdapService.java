package com.boyo.web.controller.workflow.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.boyo.common.core.domain.entity.SysDept;
import com.boyo.common.core.domain.entity.SysUser;
import com.boyo.common.core.domain.model.LoginUser;
import com.boyo.common.exception.LdapServiceException;
import com.boyo.common.helper.LoginHelper;
import com.boyo.system.service.ISysDeptService;
import com.boyo.system.service.ISysRoleService;
import com.boyo.system.service.ISysUserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Component(value = "ldapService")
public class FakeLdapService {


    @Resource
    private ISysUserService userService;

    @Resource
    private ISysRoleService roleService;

    @Resource
    private ISysDeptService deptService;

    /**
     * 根绝角色查找本部门审核人
     *
     * @param roleKey
     * @return
     */
    public String findUserByRoleKey(String roleKey) {
        System.out.println(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> findDeptManagerByRoleKey");
        System.out.println(roleKey);
        // 根据角色选择部门负责人
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser != null && StringUtils.isNotBlank(roleKey)) {
            System.out.println(loginUser.getDeptId());

            // 查询用户所属部门及所有父级部门
            List<Long> deptIdList = new ArrayList<>();
            SysDept dept = deptService.selectDeptById(loginUser.getDeptId());
            if (dept != null) {
                deptIdList.add(dept.getDeptId());
                Long[] deptIds = Convert.toLongArray(dept.getAncestors());
                deptIdList.addAll(CollectionUtil.reverse(Arrays.asList(deptIds)));
            }
            // 根据部门和角色查询用户列表
            SysUser user = new SysUser();
            user.setDeptIdList(deptIdList);
            user.setRoleKey(roleKey);
            List<SysUser> userList = userService.selectUserByRoleAndDeptId(user);
            if (userList != null && !userList.isEmpty()) {
                List<SysUser> subUserList = null;
                // 按照部门层级筛选
                for (Long aLong : deptIdList) {
                    subUserList = userList.stream().filter(user1 -> ObjectUtil.equals(user1.getDeptId(), aLong)).collect(Collectors.toList());
                    if (!subUserList.isEmpty()) {
                        break;
                    }
                }
                if (subUserList != null && !subUserList.isEmpty()) {
                    return CollectionUtil.join(subUserList.stream().map(o -> o.getUserId().toString()).collect(Collectors.toList()), ",");
                }
            }
        } else {
            throw new LdapServiceException("未找到审核人");
        }
        return null;
    }
}

package com.boyo.order.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class TEquipmentShiftDto {
    private static final long serialVersionUID=1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;
    /**
     *
     */
    private Long equipmentId;
    /**
     *
     */
    private Long shiftId;
    /**
     *
     */
    private Long tenantId;
    /**
     * 1:启用 0:停用
     */
    private Long status;

    public TEquipmentShiftDto(){

    }

}

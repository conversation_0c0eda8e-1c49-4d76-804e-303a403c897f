package com.boyo.service;

import com.boyo.domain.AoiDailyReport;
import com.boyo.mapper.AoiDailyReportMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AOI数据智能分析服务
 * 提供自动聚合、智能分析等功能
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Service
public class AoiDataAnalysisService {

    @Autowired
    private AoiDailyReportMapper aoiDailyReportMapper;

    @Autowired
    private IAoiDailyReportService aoiDailyReportService;

    /**
     * 智能分析并完善日报数据
     * @param reportId 日报ID
     * @return 分析结果
     */
    @Transactional
    public Map<String, Object> intelligentAnalysis(Long reportId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            AoiDailyReport report = aoiDailyReportService.getById(reportId);
            if (report == null) {
                result.put("success", false);
                result.put("message", "日报记录不存在");
                return result;
            }

            log.info("开始智能分析日报数据: reportId={}, machineSn={}, reportDate={}", 
                    reportId, report.getMachineSn(), report.getReportDate());

            // 1. 计算NG前三缺陷
            boolean ngTop3Success = aoiDailyReportService.calculateNgTop3(reportId);
            
            // 2. 计算缺陷率
            boolean defectRatesSuccess = aoiDailyReportService.calculateDefectRates(reportId);
            
            // 3. 计算质量指标
            boolean qualityMetricsSuccess = calculateQualityMetrics(reportId);
            
            // 4. 分析异常情况
            String abnormalInfo = analyzeAbnormalSituations(report);
            if (abnormalInfo != null && !abnormalInfo.trim().isEmpty()) {
                report.setAbnormalInfo(abnormalInfo);
                aoiDailyReportService.updateById(report);
            }

            // 5. 计算机台检率
            boolean detectionRateSuccess = calculateMachineDetectionRate(reportId);

            result.put("success", true);
            result.put("ngTop3Success", ngTop3Success);
            result.put("defectRatesSuccess", defectRatesSuccess);
            result.put("qualityMetricsSuccess", qualityMetricsSuccess);
            result.put("detectionRateSuccess", detectionRateSuccess);
            result.put("abnormalInfo", abnormalInfo);
            result.put("message", "智能分析完成");

            log.info("智能分析完成: reportId={}, 结果={}", reportId, result);
            
        } catch (Exception e) {
            log.error("智能分析失败: reportId={}", reportId, e);
            result.put("success", false);
            result.put("message", "智能分析失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 计算质量指标
     * @param reportId 日报ID
     * @return 是否成功
     */
    @Transactional
    public boolean calculateQualityMetrics(Long reportId) {
        try {
            AoiDailyReport report = aoiDailyReportService.getById(reportId);
            if (report == null) {
                return false;
            }

            // 计算一次通过率 (与良率相同)
            if (report.getControlledYield() != null) {
                report.setFirstPassYield(report.getControlledYield());
            }

            // 计算缺陷密度 (缺陷数/万件)
            if (report.getTotalInput() != null && report.getTotalInput() > 0 && report.getTotalNg() != null) {
                BigDecimal defectDensity = new BigDecimal(report.getTotalNg())
                        .divide(new BigDecimal(report.getTotalInput()), 6, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(10000));
                report.setDefectDensity(defectDensity);
            }

            // 计算OEE (设备综合效率) - 简化计算，基于良率
            if (report.getControlledYield() != null) {
                // 假设可用率95%，性能效率95%，质量效率=良率
                BigDecimal availability = new BigDecimal("95.00");
                BigDecimal performance = new BigDecimal("95.00");
                BigDecimal quality = report.getControlledYield();
                
                BigDecimal oee = availability.multiply(performance).multiply(quality)
                        .divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP);
                report.setOee(oee);
            }

            aoiDailyReportService.updateById(report);
            log.info("成功计算质量指标: reportId={}", reportId);
            return true;
            
        } catch (Exception e) {
            log.error("计算质量指标失败: reportId={}", reportId, e);
            return false;
        }
    }

    /**
     * 分析异常情况
     * @param report 日报数据
     * @return 异常信息
     */
    public String analyzeAbnormalSituations(AoiDailyReport report) {
        List<String> abnormalList = new ArrayList<>();
        
        try {
            // 1. 良率异常检查
            if (report.getControlledYield() != null) {
                if (report.getControlledYield().compareTo(new BigDecimal("90")) < 0) {
                    abnormalList.add("良率过低(" + report.getControlledYield() + "%)");
                } else if (report.getControlledYield().compareTo(new BigDecimal("99.5")) > 0) {
                    abnormalList.add("良率异常高(" + report.getControlledYield() + "%)");
                }
            }

            // 2. NG数量异常检查
            if (report.getTotalNg() != null && report.getTotalInput() != null && report.getTotalInput() > 0) {
                double ngRate = (double) report.getTotalNg() / report.getTotalInput() * 100;
                if (ngRate > 10) {
                    abnormalList.add("NG率过高(" + String.format("%.2f", ngRate) + "%)");
                }
            }

            // 3. 单一缺陷占比过高检查
            if (report.getTotalNg() != null && report.getTotalNg() > 0) {
                checkSingleDefectRatio(report, abnormalList);
            }

            // 4. 批次数异常检查
            if (report.getTotalBatches() != null) {
                if (report.getTotalBatches() < 5) {
                    abnormalList.add("批次数过少(" + report.getTotalBatches() + "批次)");
                } else if (report.getTotalBatches() > 50) {
                    abnormalList.add("批次数过多(" + report.getTotalBatches() + "批次)");
                }
            }

            // 5. 投入量异常检查
            if (report.getTotalInput() != null) {
                if (report.getTotalInput() < 1000) {
                    abnormalList.add("投入量过少(" + report.getTotalInput() + "件)");
                } else if (report.getTotalInput() > 50000) {
                    abnormalList.add("投入量过多(" + report.getTotalInput() + "件)");
                }
            }

        } catch (Exception e) {
            log.error("分析异常情况时发生错误", e);
            abnormalList.add("异常分析出错");
        }

        return abnormalList.isEmpty() ? null : String.join("; ", abnormalList);
    }

    /**
     * 检查单一缺陷占比
     */
    private void checkSingleDefectRatio(AoiDailyReport report, List<String> abnormalList) {
        Map<String, Integer> defectCounts = new HashMap<>();
        defectCounts.put("网疤", report.getWebBlemishCount() != null ? report.getWebBlemishCount() : 0);
        defectCounts.put("柱道", report.getPillarCount() != null ? report.getPillarCount() : 0);
        defectCounts.put("破洞", report.getHoleCount() != null ? report.getHoleCount() : 0);
        defectCounts.put("双线", report.getDoubleLineCount() != null ? report.getDoubleLineCount() : 0);
        defectCounts.put("打结", report.getKnotCount() != null ? report.getKnotCount() : 0);
        defectCounts.put("氧化", report.getOxidationCount() != null ? report.getOxidationCount() : 0);
        defectCounts.put("油污", report.getOilStainCount() != null ? report.getOilStainCount() : 0);
        defectCounts.put("异物", report.getForeignObjectCount() != null ? report.getForeignObjectCount() : 0);
        defectCounts.put("变形", report.getDeformationCount() != null ? report.getDeformationCount() : 0);
        defectCounts.put("裂口", report.getCrackCount() != null ? report.getCrackCount() : 0);
        defectCounts.put("异色", report.getDiscolorationCount() != null ? report.getDiscolorationCount() : 0);
        defectCounts.put("毛丝", report.getHairinessCount() != null ? report.getHairinessCount() : 0);
        defectCounts.put("接线头", report.getConnectorlugCount() != null ? report.getConnectorlugCount() : 0);

        for (Map.Entry<String, Integer> entry : defectCounts.entrySet()) {
            if (entry.getValue() > 0) {
                double ratio = (double) entry.getValue() / report.getTotalNg() * 100;
                if (ratio > 70) {
                    abnormalList.add(entry.getKey() + "缺陷占比过高(" + String.format("%.1f", ratio) + "%)");
                }
            }
        }
    }

    /**
     * 计算机台检率
     * @param reportId 日报ID
     * @return 是否成功
     */
    @Transactional
    public boolean calculateMachineDetectionRate(Long reportId) {
        try {
            AoiDailyReport report = aoiDailyReportService.getById(reportId);
            if (report == null) {
                return false;
            }

            // 机台检率 = (检出的缺陷数 / 总投入数) * 100
            if (report.getTotalInput() != null && report.getTotalInput() > 0 && report.getTotalNg() != null) {
                BigDecimal detectionRate = new BigDecimal(report.getTotalNg())
                        .divide(new BigDecimal(report.getTotalInput()), 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(100));
                report.setMachineDetectionRate(detectionRate);
                
                aoiDailyReportService.updateById(report);
                log.info("成功计算机台检率: reportId={}, detectionRate={}%", reportId, detectionRate);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            log.error("计算机台检率失败: reportId={}", reportId, e);
            return false;
        }
    }

    /**
     * 批量智能分析
     * @param reportDate 报表日期
     * @return 分析结果
     */
    @Transactional
    public Map<String, Object> batchIntelligentAnalysis(Date reportDate) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<AoiDailyReport> reports = aoiDailyReportService.getByDate(reportDate);
            
            if (reports.isEmpty()) {
                result.put("success", false);
                result.put("message", "未找到指定日期的日报数据");
                return result;
            }

            int successCount = 0;
            int failCount = 0;
            List<String> errorMessages = new ArrayList<>();

            for (AoiDailyReport report : reports) {
                try {
                    Map<String, Object> analysisResult = intelligentAnalysis(report.getId());
                    if ((Boolean) analysisResult.get("success")) {
                        successCount++;
                    } else {
                        failCount++;
                        errorMessages.add(report.getMachineSn() + ": " + analysisResult.get("message"));
                    }
                } catch (Exception e) {
                    failCount++;
                    errorMessages.add(report.getMachineSn() + ": " + e.getMessage());
                }
            }

            result.put("success", failCount == 0);
            result.put("totalCount", reports.size());
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("errorMessages", errorMessages);
            result.put("message", String.format("批量分析完成: 成功%d个，失败%d个", successCount, failCount));

            log.info("批量智能分析完成: reportDate={}, 总数={}, 成功={}, 失败={}", 
                    reportDate, reports.size(), successCount, failCount);

        } catch (Exception e) {
            log.error("批量智能分析失败: reportDate={}", reportDate, e);
            result.put("success", false);
            result.put("message", "批量分析失败: " + e.getMessage());
        }

        return result;
    }
}

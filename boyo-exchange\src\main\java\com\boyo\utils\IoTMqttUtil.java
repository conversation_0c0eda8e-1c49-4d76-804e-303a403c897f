package com.boyo.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson2.JSONObject;
import com.boyo.iot.domain.IorealData;
import com.boyo.iot.utils.IoTDBUtil;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@ConditionalOnBean(IoTDBUtil.class)
@ConditionalOnProperty(value = "mqtt.enable", havingValue = "true")
@Component
@Slf4j
public class IoTMqttUtil implements MqttCallback {
    @Autowired
    private IoTDBUtil ioTDBUtil;

    @Value("${mqtt.url}")
    public String HOST;
    @Value("${mqtt.topic}")
    public String TOPIC;
    @Value("${mqtt.username}")
    private String name;
    @Value("${mqtt.password}")
    private String passWord;


    private MqttClient client;
    private MqttConnectOptions options;
    String clientid = String.valueOf(System.currentTimeMillis());

    @PostConstruct
    public void connect() {
        try {
            // host为主机名，clientid即连接MQTT的客户端ID，一般以唯一标识符表示，MemoryPersistence设置clientid的保存形式，默认为以内存保存
            client = new MqttClient(HOST, clientid, new MemoryPersistence());
            // MQTT的连接设置
            options = new MqttConnectOptions();
            // 设置是否清空session,这里如果设置为false表示服务器会保留客户端的连接记录，这里设置为true表示每次连接到服务器都以新的身份连接
            options.setCleanSession(false);
            // 设置连接的用户名
            options.setUserName(name);
            // 设置连接的密码
            options.setPassword(passWord.toCharArray());
            // 设置超时时间 单位为秒
            options.setConnectionTimeout(10);
            // 设置会话心跳时间 单位为秒 服务器会每隔1.5*20秒的时间向客户端发送个消息判断客户端是否在线，但这个方法并没有重连的机制
            options.setKeepAliveInterval(3600);
            log.info("mqtt用户名:" + name + "  password:" + passWord);
            // 设置回调
            client.setCallback(this);
            client.connect(options);
            //订阅消息，这里与主题数量是一一对应的关系
            int[] Qos = {1};
            String[] topic1 = TOPIC.split(",");
            client.subscribe(topic1, Qos);
            log.info("连接成功");
        } catch (Exception e) {
            e.printStackTrace();
            log.info("ReportMqtt客户端连接异常，异常信息：" + e);
        }

    }

    @Override
    public void connectionLost(Throwable throwable) {
        try {
            throwable.printStackTrace();
            log.info("程序出现异常，DReportMqtt断线！正在重新连接...:");
            client.close();
            this.connect();
            log.info("ReportMqtt重新连接成功");
        } catch (MqttException e) {
            log.info(e.getMessage());
        }
    }

    ExecutorService executorService = Executors.newFixedThreadPool(10);

    @Override
    public void messageArrived(String topic, MqttMessage message) {
        // 在线程池中执行代码
        executorService.execute(() -> {
            try {
                log.info("topic:" + topic + "  message:" + new String(message.getPayload()));
                if (topic.equals("/SK/A")) {
                    executeHatong(topic, message);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    private void executeHatong(String topic, MqttMessage message) {
        String[] topics = topic.split("/");
        String enterpriseCode = topics[1];
        String deviceCode = topics[2];
        try {
            JSONObject msg = JSONObject.parseObject(new String(message.getPayload()));
            if (msg.isEmpty()) {
                return;
            }
            // 获取到的所有数据
            List<IorealData> tempList = new ArrayList<>();
            deviceCode += msg.getString("JITAI");
            // 处理数据
            for (Map.Entry<String, Object> entry : msg.entrySet()) {
                IorealData obj = new IorealData();
                obj.setTag(entry.getKey());
                obj.setDeviceCode(deviceCode);
                obj.setVal("'" + Convert.toStr(entry.getValue()).replaceAll("[\t\n\r]", "") + "'");
                obj.setKey(deviceCode + "-" + entry.getKey());
                tempList.add(obj);
            }
            if (CollUtil.isNotEmpty(tempList)) {
                ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, tempList);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {
        //log.info("消息发送成功");
    }
}

package com.boyo.order.service;

import com.boyo.order.domain.BomDetail;
import com.boyo.order.domain.TProduceOrderProcessSubtask;
import com.boyo.order.domain.vo.TProduceOrderProcessSubtaskVo;
import com.boyo.order.domain.bo.TProduceOrderProcessSubtaskBo;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 工单工序子任务Service接口
 *
 * <AUTHOR>
 * @date 2025-06-07
 */
public interface ITProduceOrderProcessSubtaskService {

    /**
     * 查询工单工序子任务
     */
    TProduceOrderProcessSubtaskVo queryById(Long id);

    /**
     * 查询工单工序子任务列表
     */
    TableDataInfo<TProduceOrderProcessSubtaskVo> queryPageList(TProduceOrderProcessSubtaskBo bo, PageQuery pageQuery);

    /**
     * 查询工单工序子任务列表
     */
    List<TProduceOrderProcessSubtaskVo> queryList(TProduceOrderProcessSubtaskBo bo);

    /**
     * 新增工单工序子任务
     */
    Boolean insertByBo(TProduceOrderProcessSubtaskBo bo);

    /**
     * 修改工单工序子任务
     */
    Boolean updateByBo(TProduceOrderProcessSubtaskBo bo);

    /**
     * 校验并批量删除工单工序子任务信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
    /**
     * 查询我的任务(app用)
     */
    TableDataInfo<TProduceOrderProcessSubtaskVo> selectOwnList(TProduceOrderProcessSubtaskBo bo, PageQuery pageQuery);

    /**
     * 根据工序子任务领料
     */
    List<BomDetail> materialRequisitionByProcessSubtask(Long id);
    /**
     * 根据工序子任务退料
     */
    List<BomDetail> returnMaterialRequisitionByProcessSubtask(Long id);

    /**
     * 子任务完工
     */
    Boolean completeOrderProcessSubtask(Long id);
}

package com.boyo.wms.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class TStorageApplyReceiptDetailInput {

    /**
     * 铜线产商代码
     */
    @ExcelProperty(value = "铜线厂商代码")
    private String copperWireManufacturer;

    /**
     * 原材厂商代码
     */
    @ExcelProperty(value = "原材厂商代码")
    private String rawMaterialManufacturer;

    /**
     * 箱号
     */
    // @ExcelProperty(value = "货位")
    // private String allocationCode;

    /**
     * 箱号
     */
    @ExcelProperty(value = "箱号")
    private String boxNumber;
    /**
     * 轴号
     */
    @ExcelProperty(value = "轴号")
    private String axleNumber;
    /**
     * 毛重
     */
    @ExcelProperty(value = "毛重")
    private Double grossWeight;
    /**
     * 轴重
     */
    @ExcelProperty(value = "轴重")
    private Double axleWeight;
    /**
     * 纸重
     */
    @ExcelProperty(value = "纸重")
    private Double tareWeight;
    /**
     * 净重
     */
    @ExcelProperty(value = "净重")
    private Double netWeight;
    /**
     * 操作员
     */
    @ExcelProperty(value = "操作员")
    private String operator;
    /**
     * 过磅员
     */
    @ExcelProperty(value = "过磅员")
    private String weigher;
}

package com.boyo.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * AOI生产日报表
 * @TableName t_aoi_daily_report
 * <AUTHOR>
 * @date 2024-01-15
 */
@TableName(value = "t_aoi_daily_report")
@Data
public class AoiDailyReport implements Serializable {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 报表日期
     */
    @TableField(value = "report_date")
    private Date reportDate;

    /**
     * 设备序列号(A-1,A-2,B-1,B-2,C-1,C-2,D-1,D-2)
     */
    @TableField(value = "machine_sn")
    private String machineSn;

    /**
     * 项目名称
     */
    @TableField(value = "project_name")
    private String projectName;

    // ===== 白板显示的核心字段 =====
    
    /**
     * 生产时间状态
     */
    @TableField(value = "production_time_status")
    private String productionTimeStatus;

    /**
     * 打料数量(投入检测数量)
     */
    @TableField(value = "input_count")
    private Integer inputCount;

    /**
     * 投产数量(实际投产数量)
     */
    @TableField(value = "production_count")
    private Integer productionCount;

    /**
     * OK数量(合格品数量)
     */
    @TableField(value = "ok_count")
    private Integer okCount;

    /**
     * NG数量(不合格品数量)
     */
    @TableField(value = "ng_count")
    private Integer ngCount;

    /**
     * 受控良率(%)
     */
    @TableField(value = "controlled_yield")
    private BigDecimal controlledYield;

    /**
     * NG项前一
     */
    @TableField(value = "ng_top1")
    private String ngTop1;

    /**
     * NG项前二
     */
    @TableField(value = "ng_top2")
    private String ngTop2;

    /**
     * NG项前三
     */
    @TableField(value = "ng_top3")
    private String ngTop3;

    /**
     * 机台检率(%)
     */
    @TableField(value = "machine_detection_rate")
    private BigDecimal machineDetectionRate;

    /**
     * 异常信息
     */
    @TableField(value = "abnormal_info")
    private String abnormalInfo;

    // ===== Excel表格的统计汇总字段 =====
    
    /**
     * 总批次数
     */
    @TableField(value = "total_batches")
    private Integer totalBatches;

    /**
     * 平均投入量
     */
    @TableField(value = "avg_input")
    private BigDecimal avgInput;

    /**
     * 平均OK数量
     */
    @TableField(value = "avg_ok")
    private BigDecimal avgOk;

    /**
     * 平均NG数量
     */
    @TableField(value = "avg_ng")
    private BigDecimal avgNg;

    /**
     * 平均良率(%)
     */
    @TableField(value = "avg_yield")
    private BigDecimal avgYield;

    /**
     * 总投入量
     */
    @TableField(value = "total_input")
    private Integer totalInput;

    /**
     * 总OK数量
     */
    @TableField(value = "total_ok")
    private Integer totalOk;

    /**
     * 总NG数量
     */
    @TableField(value = "total_ng")
    private Integer totalNg;

    // ===== 详细缺陷统计字段 =====
    
    /**
     * 网疤缺陷数量
     */
    @TableField(value = "web_blemish_count")
    private Integer webBlemishCount;

    /**
     * 柱道缺陷数量
     */
    @TableField(value = "pillar_count")
    private Integer pillarCount;

    /**
     * 破洞缺陷数量
     */
    @TableField(value = "hole_count")
    private Integer holeCount;

    /**
     * 双线缺陷数量
     */
    @TableField(value = "double_line_count")
    private Integer doubleLineCount;

    /**
     * 打结缺陷数量
     */
    @TableField(value = "knot_count")
    private Integer knotCount;

    /**
     * 氧化缺陷数量
     */
    @TableField(value = "oxidation_count")
    private Integer oxidationCount;

    /**
     * 油污缺陷数量
     */
    @TableField(value = "oil_stain_count")
    private Integer oilStainCount;

    /**
     * 异物缺陷数量
     */
    @TableField(value = "foreign_object_count")
    private Integer foreignObjectCount;

    /**
     * 变形缺陷数量
     */
    @TableField(value = "deformation_count")
    private Integer deformationCount;

    /**
     * 裂口缺陷数量
     */
    @TableField(value = "crack_count")
    private Integer crackCount;

    /**
     * 异色缺陷数量
     */
    @TableField(value = "discoloration_count")
    private Integer discolorationCount;

    /**
     * 毛丝缺陷数量
     */
    @TableField(value = "hairiness_count")
    private Integer hairinessCount;

    /**
     * 接线头缺陷数量
     */
    @TableField(value = "connectorlug_count")
    private Integer connectorlugCount;

    // ===== 缺陷率统计字段 =====
    
    /**
     * 网疤缺陷率(%)
     */
    @TableField(value = "web_blemish_rate")
    private BigDecimal webBlemishRate;

    /**
     * 柱道缺陷率(%)
     */
    @TableField(value = "pillar_rate")
    private BigDecimal pillarRate;

    /**
     * 破洞缺陷率(%)
     */
    @TableField(value = "hole_rate")
    private BigDecimal holeRate;

    /**
     * 双线缺陷率(%)
     */
    @TableField(value = "double_line_rate")
    private BigDecimal doubleLineRate;

    /**
     * 打结缺陷率(%)
     */
    @TableField(value = "knot_rate")
    private BigDecimal knotRate;

    /**
     * 氧化缺陷率(%)
     */
    @TableField(value = "oxidation_rate")
    private BigDecimal oxidationRate;

    /**
     * 油污缺陷率(%)
     */
    @TableField(value = "oil_stain_rate")
    private BigDecimal oilStainRate;

    /**
     * 异物缺陷率(%)
     */
    @TableField(value = "foreign_object_rate")
    private BigDecimal foreignObjectRate;

    /**
     * 变形缺陷率(%)
     */
    @TableField(value = "deformation_rate")
    private BigDecimal deformationRate;

    /**
     * 裂口缺陷率(%)
     */
    @TableField(value = "crack_rate")
    private BigDecimal crackRate;

    /**
     * 异色缺陷率(%)
     */
    @TableField(value = "discoloration_rate")
    private BigDecimal discolorationRate;

    /**
     * 毛丝缺陷率(%)
     */
    @TableField(value = "hairiness_rate")
    private BigDecimal hairinessRate;

    /**
     * 接线头缺陷率(%)
     */
    @TableField(value = "connectorlug_rate")
    private BigDecimal connectorlugRate;

    // ===== 时间段统计字段 =====
    
    /**
     * 班次类型(白班/夜班)
     */
    @TableField(value = "shift_type")
    private String shiftType;

    /**
     * 开始时间
     */
    @TableField(value = "start_time")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField(value = "end_time")
    private Date endTime;

    /**
     * 生产时长(分钟)
     */
    @TableField(value = "production_duration")
    private Integer productionDuration;

    // ===== 设备状态字段 =====
    
    /**
     * 设备状态(运行/停机/维护)
     */
    @TableField(value = "equipment_status")
    private String equipmentStatus;

    /**
     * 停机原因
     */
    @TableField(value = "downtime_reason")
    private String downtimeReason;

    /**
     * 停机时长(分钟)
     */
    @TableField(value = "downtime_duration")
    private Integer downtimeDuration;

    // ===== 质量指标字段 =====

    /**
     * 一次通过率(%)
     */
    @TableField(value = "first_pass_yield")
    private BigDecimal firstPassYield;

    /**
     * 缺陷密度(个/万)
     */
    @TableField(value = "defect_density")
    private BigDecimal defectDensity;

    /**
     * 设备综合效率(%)
     */
    @TableField(value = "oee")
    private BigDecimal oee;

    // ===== 操作员信息字段 =====

    /**
     * 操作员姓名
     */
    @TableField(value = "operator_name")
    private String operatorName;

    /**
     * 质检员姓名
     */
    @TableField(value = "quality_inspector")
    private String qualityInspector;

    /**
     * 班长姓名
     */
    @TableField(value = "shift_leader")
    private String shiftLeader;

    // ===== 备注和扩展字段 =====

    /**
     * 备注信息
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 数据来源(AUTO:自动生成,MANUAL:手工录入)
     */
    @TableField(value = "data_source")
    private String dataSource;

    /**
     * 同步状态(PENDING:待同步,SYNCED:已同步,FAILED:同步失败)
     */
    @TableField(value = "sync_status")
    private String syncStatus;

    /**
     * 原始数据条数
     */
    @TableField(value = "raw_data_count")
    private Integer rawDataCount;

    // ===== 系统字段 =====

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 删除标志(0:正常,1:删除)
     */
    @TableField(value = "del_flag")
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

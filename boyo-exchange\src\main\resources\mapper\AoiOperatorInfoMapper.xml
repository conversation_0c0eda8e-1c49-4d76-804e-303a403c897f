<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mapper.AoiOperatorInfoMapper">

    <!-- 根据核心表ID列表批量查询操作员信息 -->
    <select id="selectByReportCoreIds" resultType="com.boyo.domain.AoiOperatorInfo">
        SELECT * FROM t_aoi_operator_info 
        WHERE report_core_id IN
        <foreach collection="reportCoreIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND del_flag = 0
    </select>

    <!-- 从原始数据生成操作员信息 -->
    <insert id="generateFromRawData">
        INSERT INTO t_aoi_operator_info (
            report_core_id,
            operator_name, quality_inspector, shift_leader,
            data_source, sync_status, raw_data_count,
            create_time, update_time, del_flag
        )
        SELECT 
            #{reportCoreId} as report_core_id,
            '系统操作员' as operator_name,
            '系统质检员' as quality_inspector,
            '系统班长' as shift_leader,
            'AUTO' as data_source,
            'SYNCED' as sync_status,
            COUNT(*) as raw_data_count,
            NOW() as create_time,
            NOW() as update_time,
            0 as del_flag
        FROM t_check_device_secret
        WHERE DATE(test_time) = #{reportDate}
        AND machine_sn = #{machineSn}
        ON DUPLICATE KEY UPDATE
            raw_data_count = VALUES(raw_data_count),
            sync_status = 'SYNCED',
            update_time = NOW()
    </insert>

</mapper>

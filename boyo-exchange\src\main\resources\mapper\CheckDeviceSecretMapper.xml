<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mapper.CheckDeviceSecretMapper">

    <resultMap id="BaseResultMap" type="com.boyo.domain.CheckDeviceSecret">
            <id property="ppid" column="ppid" jdbcType="INTEGER"/>
            <result property="partId" column="part_id" jdbcType="INTEGER"/>
            <result property="vendor" column="vendor" jdbcType="VARCHAR"/>
            <result property="wk" column="wk" jdbcType="INTEGER"/>
            <result property="testTime" column="test_time" jdbcType="TIMESTAMP"/>
            <result property="testResult" column="test_result" jdbcType="VARCHAR"/>
            <result property="deptId" column="dept_id" jdbcType="INTEGER"/>
            <result property="lineId" column="line_id" jdbcType="INTEGER"/>
            <result property="moId" column="mo_id" jdbcType="INTEGER"/>
            <result property="testStation" column="test_station" jdbcType="VARCHAR"/>
            <result property="machineSn" column="machine_sn" jdbcType="VARCHAR"/>
            <result property="testChannelId" column="test_channel_id" jdbcType="INTEGER"/>
            <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
            <result property="okCount" column="ok_count" jdbcType="INTEGER"/>
            <result property="ngCount" column="ng_count" jdbcType="INTEGER"/>
            <result property="total" column="total" jdbcType="INTEGER"/>
            <result property="yield" column="yield" jdbcType="DECIMAL"/>
            <result property="mesh" column="mesh" jdbcType="INTEGER"/>
            <result property="thickness" column="thickness" jdbcType="INTEGER"/>
            <result property="webBlemishDefect" column="web_blemish_defect" jdbcType="INTEGER"/>
            <result property="pillarDefect" column="pillar_defect" jdbcType="INTEGER"/>
            <result property="holeDefect" column="hole_defect" jdbcType="INTEGER"/>
            <result property="doubleLineDefect" column="double_line_defect" jdbcType="INTEGER"/>
            <result property="knotDefect" column="knot_defect" jdbcType="INTEGER"/>
            <result property="oxidationDefect" column="oxidation_defect" jdbcType="INTEGER"/>
            <result property="oilStainDefect" column="oil_stain_defect" jdbcType="INTEGER"/>
            <result property="foreignObjectDefect" column="foreign_object_defect" jdbcType="INTEGER"/>
            <result property="deformationDefect" column="deformation_defect" jdbcType="INTEGER"/>
            <result property="crackDefect" column="crack_defect" jdbcType="INTEGER"/>
            <result property="discolorationDefect" column="discoloration_defect" jdbcType="INTEGER"/>
            <result property="hairinessDefect" column="hairiness_defect" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ppid,part_id,vendor,
        wk,test_time,test_result,
        dept_id,line_id,mo_id,
        test_station,machine_sn,test_channel_id,
        project_name,ok_count,ng_count,
        total,yield,mesh,
        thickness,web_blemish_defect,pillar_defect,
        hole_defect,double_line_defect,knot_defect,
        oxidation_defect,oil_stain_defect,foreign_object_defect,
        deformation_defect,crack_defect,discoloration_defect,
        hairiness_defect,create_time
    </sql>

    <!-- 分页查询原始检测数据 -->
    <select id="selectRawDataWithPaging" resultType="java.util.Map">
        SELECT
            ppid,
            part_id as partId,
            vendor,
            wk,
            test_time as testTime,
            test_result as testResult,
            dept_id as deptId,
            line_id as lineId,
            mo_id as moId,
            test_station as testStation,
            machine_sn as machineSn,
            test_channel_id as testChannelId,
            project_name as projectName,
            ok_count as okCount,
            ng_count as ngCount,
            total,
            yield,
            mesh,
            thickness,
            web_blemish_defect as webBlemishDefect,
            pillar_defect as pillarDefect,
            hole_defect as holeDefect,
            double_line_defect as doubleLineDefect,
            knot_defect as knotDefect,
            oxidation_defect as oxidationDefect,
            oil_stain_defect as oilStainDefect,
            foreign_object_defect as foreignObjectDefect,
            deformation_defect as deformationDefect,
            crack_defect as crackDefect,
            discoloration_defect as discolorationDefect,
            hairiness_defect as hairinessDefect,
            connectorlug_defect as connectorlugDefect,
            markdot_defect as markdotDefect,
            alog_version as alogVersion,
            create_time as createTime
        FROM t_check_device_secret
        WHERE 1=1
        <if test="testDate != null">
            AND DATE(create_time) = DATE(#{testDate})
        </if>
        <if test="machineSn != null and machineSn != ''">
            AND machine_sn = #{machineSn}
        </if>
        <if test="projectName != null and projectName != ''">
            AND project_name = #{projectName}
        </if>
        <if test="testStation != null and testStation != ''">
            AND test_station = #{testStation}
        </if>
        <if test="testResult != null and testResult != ''">
            AND test_result = #{testResult}
        </if>
        ORDER BY create_time DESC, project_name, machine_sn
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 统计原始检测数据总数 -->
    <select id="countRawData" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_check_device_secret
        WHERE 1=1
        <if test="testDate != null">
            AND DATE(create_time) = DATE(#{testDate})
        </if>
        <if test="machineSn != null and machineSn != ''">
            AND machine_sn = #{machineSn}
        </if>
        <if test="projectName != null and projectName != ''">
            AND project_name = #{projectName}
        </if>
        <if test="testStation != null and testStation != ''">
            AND test_station = #{testStation}
        </if>
        <if test="testResult != null and testResult != ''">
            AND test_result = #{testResult}
        </if>
    </select>

    <!-- 获取原始检测数据统计信息 -->
    <select id="getRawDataStatistics" resultType="java.util.Map">
        SELECT
            COUNT(*) as totalRecords,
            SUM(CASE WHEN test_result = 'OK' THEN 1 ELSE 0 END) as okCount,
            SUM(CASE WHEN test_result = 'NG' THEN 1 ELSE 0 END) as ngCount,
            ROUND(
                CASE
                    WHEN COUNT(*) > 0 THEN
                        SUM(CASE WHEN test_result = 'OK' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)
                    ELSE 0
                END, 2
            ) as yieldRate,
            COUNT(DISTINCT machine_sn) as machineCount,
            COUNT(DISTINCT project_name) as projectCount,
            COUNT(DISTINCT test_station) as stationCount,
            SUM(CAST(COALESCE(web_blemish_defect, '0') AS UNSIGNED)) as totalWebBlemishDefect,
            SUM(CAST(COALESCE(pillar_defect, '0') AS UNSIGNED)) as totalPillarDefect,
            SUM(CAST(COALESCE(hole_defect, '0') AS UNSIGNED)) as totalHoleDefect,
            SUM(CAST(COALESCE(double_line_defect, '0') AS UNSIGNED)) as totalDoubleLineDefect,
            SUM(CAST(COALESCE(knot_defect, '0') AS UNSIGNED)) as totalKnotDefect,
            SUM(CAST(COALESCE(oxidation_defect, '0') AS UNSIGNED)) as totalOxidationDefect,
            SUM(CAST(COALESCE(oil_stain_defect, '0') AS UNSIGNED)) as totalOilStainDefect,
            SUM(CAST(COALESCE(foreign_object_defect, '0') AS UNSIGNED)) as totalForeignObjectDefect,
            SUM(CAST(COALESCE(deformation_defect, '0') AS UNSIGNED)) as totalDeformationDefect,
            SUM(CAST(COALESCE(crack_defect, '0') AS UNSIGNED)) as totalCrackDefect,
            SUM(CAST(COALESCE(discoloration_defect, '0') AS UNSIGNED)) as totalDiscolorationDefect,
            SUM(CAST(COALESCE(hairiness_defect, '0') AS UNSIGNED)) as totalHairinessDefect,
            SUM(CAST(COALESCE(connectorlug_defect, '0') AS UNSIGNED)) as totalConnectorlugDefect,
            SUM(CAST(COALESCE(markdot_defect, '0') AS UNSIGNED)) as totalMarkdotDefect
        FROM t_check_device_secret
        WHERE 1=1
        <if test="testDate != null">
            AND DATE(create_time) = DATE(#{testDate})
        </if>
        <if test="machineSn != null and machineSn != ''">
            AND machine_sn = #{machineSn}
        </if>
        <if test="projectName != null and projectName != ''">
            AND project_name = #{projectName}
        </if>
        <if test="testStation != null and testStation != ''">
            AND test_station = #{testStation}
        </if>
        <if test="testResult != null and testResult != ''">
            AND test_result = #{testResult}
        </if>
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.process.mapper.RawMiddleMapper">

    <resultMap id="BaseResultMap" type="com.boyo.process.domain.RawMiddle">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="batchCode" column="batch_code" jdbcType="VARCHAR"/>
            <result property="box" column="box" jdbcType="VARCHAR"/>
            <result property="checkUser" column="check_user" jdbcType="VARCHAR"/>
            <result property="state" column="state" jdbcType="INTEGER"/>
            <result property="purchaseOrderNum" column="purchase_order_num" jdbcType="VARCHAR"/>
            <result property="storageTime" column="storage_time" jdbcType="TIMESTAMP"/>
            <result property="purchaseTime" column="purchase_time" jdbcType="TIMESTAMP"/>
            <result property="checkTime" column="check_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,batch_code,box,
        check_user,state,purchase_order_num,
        storage_time,purchase_time,check_time,
        create_time,create_by,update_time,
        update_by
    </sql>
</mapper>

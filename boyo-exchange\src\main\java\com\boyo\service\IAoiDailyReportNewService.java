package com.boyo.service;

import com.boyo.domain.AoiDailyReportCore;
import com.boyo.domain.vo.AoiDailyReportVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * AOI生产日报表新架构Service接口
 * 基于拆分后的表结构
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface IAoiDailyReportNewService {

    // ===== 核心查询方法 =====

    /**
     * 根据日期和设备查询核心日报
     * @param reportDate 报表日期
     * @param machineSn 设备序列号
     * @return 核心日报数据
     */
    AoiDailyReportCore getCoreByDateAndMachine(Date reportDate, String machineSn);

    /**
     * 根据日期查询所有设备的核心日报
     * @param reportDate 报表日期
     * @return 核心日报数据列表
     */
    List<AoiDailyReportCore> getCoreByDate(Date reportDate);

    /**
     * 查询白板显示数据(仅核心字段)
     * @param reportDate 报表日期
     * @return 白板显示数据
     */
    List<Map<String, Object>> getWhiteboardData(Date reportDate);

    /**
     * 查询完整的日报数据(包含所有关联表)
     * @param reportDate 报表日期
     * @param machineSn 设备序列号
     * @return 完整日报数据
     */
    AoiDailyReportVO getFullReportByDateAndMachine(Date reportDate, String machineSn);

    /**
     * 查询完整的日报数据列表(包含所有关联表)
     * @param reportDate 报表日期
     * @return 完整日报数据列表
     */
    List<AoiDailyReportVO> getFullReportsByDate(Date reportDate);

    /**
     * 分页查询完整的日报数据
     * @param reportDate 报表日期
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页数据
     */
    Map<String, Object> getFullReportsPage(Date reportDate, Integer pageNum, Integer pageSize);

    // ===== 数据生成方法 =====

    /**
     * 从原始数据生成完整日报(包含所有关联表)
     * @param reportDate 报表日期
     * @param machineSn 设备序列号
     * @return 是否成功
     */
    boolean generateFullReportFromRawData(Date reportDate, String machineSn);

    /**
     * 批量生成完整日报
     * @param reportDate 报表日期
     * @return 生成数量
     */
    int batchGenerateFullReportFromRawData(Date reportDate);

    // ===== 统计分析方法 =====

    /**
     * 查询缺陷分布统计
     * @param reportDate 报表日期
     * @param machineSn 设备序列号(可选)
     * @return 缺陷分布数据
     */
    List<Map<String, Object>> getDefectDistribution(Date reportDate, String machineSn);

    /**
     * 查询设备产能统计
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 产能统计数据
     */
    List<Map<String, Object>> getProductionStatistics(Date startDate, Date endDate);

    /**
     * 查询质量指标统计
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param machineSn 设备序列号(可选)
     * @return 质量指标数据
     */
    List<Map<String, Object>> getQualityMetrics(Date startDate, Date endDate, String machineSn);

    /**
     * 获取日报统计概览
     * @param reportDate 报表日期
     * @return 统计概览
     */
    Map<String, Object> getReportSummary(Date reportDate);

    // ===== 数据维护方法 =====

    /**
     * 计算并更新NG前三
     * @param coreId 核心表ID
     * @return 是否成功
     */
    boolean calculateAndUpdateNgTop3(Long coreId);

    /**
     * 计算并更新缺陷率
     * @param coreId 核心表ID
     * @return 是否成功
     */
    boolean calculateAndUpdateDefectRates(Long coreId);

    /**
     * 获取设备列表
     * @return 设备列表
     */
    List<String> getMachineList();

    /**
     * 导出日报数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param machineSn 设备序列号(可选)
     * @return 导出数据
     */
    List<Map<String, Object>> exportReportData(Date startDate, Date endDate, String machineSn);
}

package com.boyo.domain.enums;

import java.util.HashMap;
import java.util.Map;

public enum DeviceSecretStateEnum {
    RUNNING(1, "运行状态"),
    PAUSED(2, "暂停状态"),
    STOPPED(3, "停止状态"),
    ERROR(4, "错误状态"),
    BACK(5, "回原状态"),
    ;
    private Integer statusCode;
    private String statusName;

    DeviceSecretStateEnum(Integer statusCode, String statusName) {
        this.statusCode = statusCode;
        this.statusName = statusName;
    }

    public static Map<Integer, String> getStatus() {
        Map<Integer, String> result = new HashMap<>();
        for (DeviceSecretStateEnum value : values()) {
            result.put(value.statusCode, value.statusName);
        }
        return result;
    }
}

package com.boyo.utils;

import com.boyo.domain.AoiDailyReport;
import com.boyo.service.IAoiDailyReportService;
import com.boyo.service.AoiDataAggregationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.Date;
import java.util.Map;

/**
 * AOI数据同步定时任务
 * 用于定时从原始数据生成日报表
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Component
public class AoiDataSyncTask {

    @Autowired
    private IAoiDailyReportService aoiDailyReportService;

    @Autowired
    private AoiDataAggregationService aoiDataAggregationService;

    /**
     * 每小时智能聚合一次当天数据
     * 每小时的第5分钟执行
     */
    @Scheduled(cron = "0 5 * * * ?")
    public void syncTodayData() {
        try {
            Date today = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
            log.info("开始智能聚合今日AOI数据: {}", today);

            // 使用增量聚合，避免重复处理
            Map<String, Object> result = aoiDataAggregationService.incrementalAggregateData(today);

            if ((Boolean) result.get("success")) {
                log.info("今日AOI数据智能聚合成功: {}", result.get("message"));
            } else {
                log.warn("今日AOI数据智能聚合: {}", result.get("message"));
            }
        } catch (Exception e) {
            log.error("智能聚合今日AOI数据时发生异常", e);
        }
    }

    /**
     * 每天凌晨2点完整聚合昨天的数据
     * 确保昨天的数据完整性和准确性
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void syncYesterdayData() {
        try {
            Date yesterday = Date.from(LocalDate.now().minusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
            log.info("开始完整聚合昨日AOI数据: {}", yesterday);

            // 使用完整聚合，确保数据准确性
            Map<String, Object> result = aoiDataAggregationService.autoAggregateData(yesterday);

            if ((Boolean) result.get("success")) {
                log.info("昨日AOI数据完整聚合成功: {}", result.get("message"));
            } else {
                log.error("昨日AOI数据完整聚合失败: {}", result.get("message"));
            }
        } catch (Exception e) {
            log.error("完整聚合昨日AOI数据时发生异常", e);
        }
    }



    /**
     * 手动同步指定日期的数据
     * @param targetDate 目标日期
     * @return 同步结果
     */
    public Map<String, Object> manualSync(Date targetDate) {
        try {
            log.info("手动同步AOI数据: {}", targetDate);
            Map<String, Object> result = aoiDailyReportService.syncRawDataToReport(targetDate);
            log.info("手动同步完成: {}", result.get("message"));
            return result;
        } catch (Exception e) {
            log.error("手动同步AOI数据时发生异常", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("count", 0);
            errorResult.put("message", "同步失败: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 批量同步指定日期范围的数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 同步结果
     */
    public Map<String, Object> batchSync(Date startDate, Date endDate) {
        try {
            log.info("批量同步AOI数据: {} 到 {}", startDate, endDate);

            LocalDate start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

            int totalSynced = 0;
            int successDays = 0;
            int failedDays = 0;

            for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
                Date syncDate = Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());
                Map<String, Object> result = aoiDailyReportService.syncRawDataToReport(syncDate);

                if ((Boolean) result.get("success")) {
                    totalSynced += (Integer) result.get("count");
                    successDays++;
                    log.debug("同步日期 {} 成功: {}", date, result.get("message"));
                } else {
                    failedDays++;
                    log.warn("同步日期 {} 失败: {}", date, result.get("message"));
                }
            }

            String message = String.format("批量同步完成，成功 %d 天，失败 %d 天，共同步 %d 条记录",
                                          successDays, failedDays, totalSynced);
            log.info(message);

            Map<String, Object> result = new HashMap<>();
            result.put("success", failedDays == 0);
            result.put("count", totalSynced);
            result.put("successDays", successDays);
            result.put("failedDays", failedDays);
            result.put("message", message);
            return result;
        } catch (Exception e) {
            log.error("批量同步AOI数据时发生异常", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("count", 0);
            errorResult.put("message", "批量同步失败: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 检查数据同步状态
     * @param checkDate 检查日期
     * @return 检查结果
     */
    public Map<String, Object> checkSyncStatus(Date checkDate) {
        try {
            // 检查指定日期是否有日报数据
            List<AoiDailyReport> reports = aoiDailyReportService.getByDate(checkDate);

            boolean hasSyncedData = !reports.isEmpty();
            int reportCount = reports.size();

            // 统计同步状态
            long syncedCount = reports.stream()
                .filter(r -> "SYNCED".equals(r.getSyncStatus()))
                .count();

            long pendingCount = reports.stream()
                .filter(r -> "PENDING".equals(r.getSyncStatus()))
                .count();

            long failedCount = reports.stream()
                .filter(r -> "FAILED".equals(r.getSyncStatus()))
                .count();

            Map<String, Object> result = new HashMap<>();
            result.put("date", checkDate);
            result.put("hasSyncedData", hasSyncedData);
            result.put("reportCount", reportCount);
            result.put("syncedCount", syncedCount);
            result.put("pendingCount", pendingCount);
            result.put("failedCount", failedCount);
            result.put("syncRate", reportCount > 0 ? (double) syncedCount / reportCount * 100 : 0);
            return result;
        } catch (Exception e) {
            log.error("检查同步状态时发生异常", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("date", checkDate);
            errorResult.put("hasSyncedData", false);
            errorResult.put("error", e.getMessage());
            return errorResult;
        }
    }
}

package com.boyo.utils;

import com.boyo.domain.AoiDailyReport;
import com.boyo.service.IAoiDailyReportService;
import com.boyo.service.AoiDataAggregationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.Date;
import java.util.Map;

/**
 * AOI数据同步定时任务
 * 用于定时从原始数据生成日报表
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Component
public class AoiDataSyncTask {

    @Autowired
    private IAoiDailyReportService aoiDailyReportService;

    @Autowired
    private AoiDataAggregationService aoiDataAggregationService;

    /**
     * 每小时智能聚合一次当天数据
     * 每小时的第5分钟执行
     */
    @Scheduled(cron = "0 5 * * * ?")
    public void syncTodayData() {
        try {
            Date today = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
            log.info("开始智能聚合今日AOI数据: {}", today);

            // 使用增量聚合，避免重复处理
            Map<String, Object> result = aoiDataAggregationService.incrementalAggregateData(today);

            if ((Boolean) result.get("success")) {
                log.info("今日AOI数据智能聚合成功: {}", result.get("message"));
            } else {
                log.warn("今日AOI数据智能聚合: {}", result.get("message"));
            }
        } catch (Exception e) {
            log.error("智能聚合今日AOI数据时发生异常", e);
        }
    }

    /**
     * 每天凌晨2点完整聚合昨天的数据
     * 确保昨天的数据完整性和准确性
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void syncYesterdayData() {
        try {
            Date yesterday = Date.from(LocalDate.now().minusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
            log.info("开始完整聚合昨日AOI数据: {}", yesterday);

            // 使用完整聚合，确保数据准确性
            Map<String, Object> result = aoiDataAggregationService.autoAggregateData(yesterday);

            if ((Boolean) result.get("success")) {
                log.info("昨日AOI数据完整聚合成功: {}", result.get("message"));
            } else {
                log.error("昨日AOI数据完整聚合失败: {}", result.get("message"));
            }
        } catch (Exception e) {
            log.error("完整聚合昨日AOI数据时发生异常", e);
        }
    }





    /**
     * 检查数据同步状态
     * @param checkDate 检查日期
     * @return 检查结果
     */
    public Map<String, Object> checkSyncStatus(Date checkDate) {
        try {
            // 检查指定日期是否有日报数据
            List<AoiDailyReport> reports = aoiDailyReportService.getByDate(checkDate);

            boolean hasSyncedData = !reports.isEmpty();
            int reportCount = reports.size();

            // 统计同步状态
            long syncedCount = reports.stream()
                .filter(r -> "SYNCED".equals(r.getSyncStatus()))
                .count();

            long pendingCount = reports.stream()
                .filter(r -> "PENDING".equals(r.getSyncStatus()))
                .count();

            long failedCount = reports.stream()
                .filter(r -> "FAILED".equals(r.getSyncStatus()))
                .count();

            Map<String, Object> result = new HashMap<>();
            result.put("date", checkDate);
            result.put("hasSyncedData", hasSyncedData);
            result.put("reportCount", reportCount);
            result.put("syncedCount", syncedCount);
            result.put("pendingCount", pendingCount);
            result.put("failedCount", failedCount);
            result.put("syncRate", reportCount > 0 ? (double) syncedCount / reportCount * 100 : 0);
            return result;
        } catch (Exception e) {
            log.error("检查同步状态时发生异常", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("date", checkDate);
            errorResult.put("hasSyncedData", false);
            errorResult.put("error", e.getMessage());
            return errorResult;
        }
    }
}

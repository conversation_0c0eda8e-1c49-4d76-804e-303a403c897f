package com.boyo.wms.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.boyo.common.annotation.ExcelDictFormat;
import com.boyo.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 领料单详情物料视图对象 t_material_requisition_details_code
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ExcelIgnoreUnannotated
public class TMaterialRequisitionDetailsCodeVo {

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long tenantId;

    /**
     * 物料名称
     */
    @ExcelProperty(value = "物料名称")
    private String materialName;

    /**
     * 领料单详情id
     */
    @ExcelProperty(value = "领料单详情id")
    private Long materialRequisitionDetailsId;

    /**
     * 物料的具体成品编码
     */
    @ExcelProperty(value = "物料的具体成品编码")
    private String materialProductCode;


}

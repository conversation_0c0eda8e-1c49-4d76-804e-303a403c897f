### 使用版本(未按照模板填写直接删除)

- jdk版本(带上尾号): 例如 1.8.0_202
- 框架版本(项目启动时输出的版本号): 例如 4.4.0
- 其他依赖版本(你觉得有必要的):

### 问题前提

> 功能不好用 不会用 是否已经看过项目文档
> 项目运行报错 是否已经拿着报错信息去百度 常见报错百度百度足以
> 是否搜索过其他issue 一些已经解决的问题 会在issue内留下解决方法
> 无法线上解决或者与框架无关的问题的欢迎加VIP群跟作者一对一谈

### 异常模块

> 此报错都涉及到那些系统模块

例如 boyo-system boyo-auth 等等

### 问题描述

> 越详细越容易直击问题所在

已知: XXX功能不好用 或 XXX数据不正常 等等

### 希望结果

> 想知道你觉得怎么样是正常或者合理的

希望功能可以有XXX结果 或者 XXX现象

### 重现步骤

> 作者并不知道这个问题是如何出现的

- 1
- 2
- 3

### 相关代码与报错信息(请勿发混乱格式)

> 代码可按照如下形式提供或者截图均可 越详细越好
> 大多数问题都是 代码编写错误问题 逻辑问题 或者用法错误等问题

```java
public class XXX {

}
```
package com.boyo.web.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.boyo.common.core.domain.R;
import com.boyo.common.storage.TenantStorage;
import com.boyo.domain.CheckDeviceSecret;
import com.boyo.order.domain.BaseMaterial;
import com.boyo.order.domain.ProduceOrderReport;
import com.boyo.order.domain.TInspectionReport;
import com.boyo.order.domain.TProduceOrderProcessSubtask;
import com.boyo.order.mapper.BaseMaterialMapper;
import com.boyo.order.mapper.ProduceOrderReportMapper;
import com.boyo.order.mapper.TInspectionReportMapper;
import com.boyo.order.mapper.TProduceOrderProcessSubtaskMapper;
import com.boyo.process.domain.MiddleMaterial;
import com.boyo.process.domain.Quality;
import com.boyo.process.domain.RawMiddle;
import com.boyo.process.service.MiddleMaterialService;
import com.boyo.process.service.QualityService;
import com.boyo.process.service.RawMiddleService;
import com.boyo.service.CheckDeviceSecretService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@RestController
@RequestMapping("/source")
public class SourceController {
    private final MiddleMaterialService middleMaterialService;
    private final QualityService qualityService;
    private final CheckDeviceSecretService checkDeviceSecretService;
    private final TProduceOrderProcessSubtaskMapper produceOrderProcessSubtaskMapper;
    private final ProduceOrderReportMapper produceOrderReportMapper;
    private final BaseMaterialMapper baseMaterialMapper;
    private final TInspectionReportMapper tInspectionReportMapper;
    private final RawMiddleService rawMiddleService;

    /**
     * 溯源详情
     * 用工序判断使用那个方法，方法会自动调用上一步骤的方法
     *  用Switch，然后倒序查询，这样找到第一个匹配后，就会自动执行下面的方法了
     */
    @GetMapping
    public R source(MiddleMaterial middleMaterial) {
        // 结果
        JSONObject result = new JSONObject();
        // 不同工序产品的id列表
        boolean isFirst = true;
        List<Long> cidList = null;
        List<Long> pidList = null;
        List<String> sourceCodeList = null;
        List<String> snCodeList = null;
        List<String> codeList = null;
        switch (middleMaterial.getProcess()) {
            case "包装":
                List<MiddleMaterial> middleMaterials1 = middleMaterialService.list(new LambdaQueryWrapper<MiddleMaterial>()
                    .eq(isFirst, MiddleMaterial::getId, middleMaterial.getId())
                    .eq(MiddleMaterial::getProcess, "包装")
                );
                // cidList = middleMaterials1.stream().map(MiddleMaterial::getId).collect(Collectors.toList());
                sourceCodeList = middleMaterials1.stream().map(MiddleMaterial::getSourceCode).collect(Collectors.toList());
                result.put("包装", middleMaterials1);
                isFirst = false;
            case "OQC": // cid存储包装id
                List<MiddleMaterial> middleMaterials2 = middleMaterialService.list(new LambdaQueryWrapper<MiddleMaterial>()
                    .eq(isFirst, MiddleMaterial::getId, middleMaterial.getId())
                    .in(CollUtil.isNotEmpty(sourceCodeList), MiddleMaterial::getSourceCode, sourceCodeList)
                    .eq(MiddleMaterial::getProcess, "OQC")
                );
                sourceCodeList = middleMaterials2.stream().map(MiddleMaterial::getSourceCode).collect(Collectors.toList());
                result.put("OQC", middleMaterials2);
                isFirst = false;
            case "分切": // cid存储整平、初检id 分切小卷是用pid存储大卷的id，sn码能关联AOI，溯源码能关联OQC
                // 小卷
                List<MiddleMaterial> middleMaterials3 = middleMaterialService.list(new LambdaQueryWrapper<MiddleMaterial>()
                    .eq(isFirst, MiddleMaterial::getId, middleMaterial.getId())
                    .in(CollUtil.isNotEmpty(sourceCodeList), MiddleMaterial::getSourceCode, sourceCodeList)
                    .eq(MiddleMaterial::getProcess, "分切")
                );
                snCodeList = middleMaterials3.stream().map(MiddleMaterial::getSnCode).collect(Collectors.toList());
                pidList = middleMaterials3.stream().map(MiddleMaterial::getPId).collect(Collectors.toList());
                // 大卷
                List<MiddleMaterial> middleMaterials4 = middleMaterialService.list(new LambdaQueryWrapper<MiddleMaterial>()
                    .in(MiddleMaterial::getId, pidList)
                    .eq(MiddleMaterial::getProcess, "分切")
                );
                cidList = middleMaterials3.stream().map(MiddleMaterial::getId).collect(Collectors.toList());
                for (MiddleMaterial material : middleMaterials4) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("reflect", middleMaterials3);
                    material.setOther(jsonObject);
                    TProduceOrderProcessSubtask subtask = produceOrderProcessSubtaskMapper.selectOne(new LambdaQueryWrapper<TProduceOrderProcessSubtask>()
                        .eq(TProduceOrderProcessSubtask::getRollCode, material.getCode())
                    );
                    if (ObjectUtil.isNull(subtask)) {
                        continue;
                    }
                    List<ProduceOrderReport> produceOrderReports = produceOrderReportMapper.selectList(new LambdaQueryWrapper<ProduceOrderReport>()
                        .eq(ProduceOrderReport::getOrderProcessSubtaskId, subtask.getId())
                        .orderByAsc(ProduceOrderReport::getCreateTime)
                    );
                    material.setProduceOrderReportList(produceOrderReports);
                }
                result.put("分切", middleMaterials4);
                isFirst = false;
            case "AOI质检": // sn码关联
                // 使用sn码查询保密设备表的ppid
                List<CheckDeviceSecret> checkDeviceSecretList = checkDeviceSecretService.list(new LambdaQueryWrapper<CheckDeviceSecret>()
                    .in(CollUtil.isNotEmpty(snCodeList), CheckDeviceSecret::getPpid, snCodeList)
                );
                result.put("AOI质检", checkDeviceSecretList);
                isFirst = false;
            case "整平、初检": // cid存储分切大卷id
                List<MiddleMaterial> middleMaterials5 = middleMaterialService.list(new LambdaQueryWrapper<MiddleMaterial>()
                    .eq(isFirst, MiddleMaterial::getId, middleMaterial.getId())
                    .in(CollUtil.isNotEmpty(cidList), MiddleMaterial::getCId, cidList)
                    .eq(MiddleMaterial::getProcess, "整平、初检")
                );
                cidList = middleMaterials5.stream().map(MiddleMaterial::getId).collect(Collectors.toList());
                for (MiddleMaterial material : middleMaterials5) {
                    /*
                        同步更新一下前端：
                            检验报告检验项
                            判定 - 等级
                            母卷编号 - 卷网溯源码
                            工站是没有的
                            日期和操作员就是底下的哪些
                     */
                    TInspectionReport tInspectionReport = tInspectionReportMapper.selectOne(new LambdaQueryWrapper<TInspectionReport>().eq(TInspectionReport::getRollCode, material.getCode()));
                    JSONObject jsonObject = JSON.parseObject(JSONObject.toJSONString(tInspectionReport));
                    // 解析为 JSONArray
                    JSONArray jsonArray = JSON.parseArray(tInspectionReport.getVisualInspection());
                    // 前端需要转为数组，下表标识就代表问题的编号，对应的值就是问题的数量
                    Integer[] issueList = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONArray row = jsonArray.getJSONArray(i);
                        for (int j = 0; j < row.size(); j++) {
                            Object value = row.getObject(j, Object.class);
                            if (value instanceof Integer) {
                                issueList[(Integer) value]++;
                            }
                        }
                    }
                    jsonObject.put("checkItemIssue", issueList);
                    material.setOther(jsonObject);
                }
                result.put("整平、初检", middleMaterials5);
                isFirst = false;
            case "编织": // cid存储整平、初检id
                List<MiddleMaterial> middleMaterials6 = middleMaterialService.list(new LambdaQueryWrapper<MiddleMaterial>()
                    .eq(isFirst, MiddleMaterial::getId, middleMaterial.getId())
                    .in(CollUtil.isNotEmpty(cidList), MiddleMaterial::getCId, cidList)
                    .eq(MiddleMaterial::getProcess, "编织")
                );
                cidList = middleMaterials6.stream().map(MiddleMaterial::getId).collect(Collectors.toList());
                // 查询子任务报工
                for (MiddleMaterial material : middleMaterials6) {
                    TProduceOrderProcessSubtask subtask = produceOrderProcessSubtaskMapper.selectOne(new LambdaQueryWrapper<TProduceOrderProcessSubtask>()
                        .eq(TProduceOrderProcessSubtask::getRollCode, material.getCode())
                    );
                    if (ObjectUtil.isNull(subtask)) {
                        continue;
                    }
                    List<ProduceOrderReport> produceOrderReports = produceOrderReportMapper.selectList(new LambdaQueryWrapper<ProduceOrderReport>()
                        .eq(ProduceOrderReport::getOrderProcessSubtaskId, subtask.getId())
                        .orderByAsc(ProduceOrderReport::getCreateTime)
                    );
                    material.setProduceOrderReportList(produceOrderReports);
                    // 从物料中提取信息
                    BaseMaterial baseMaterial = baseMaterialMapper.selectById(material.getBaseMaterialId());
                    if (ObjectUtil.isNull(baseMaterial) || StrUtil.isEmpty(baseMaterial.getName())) {
                        continue;
                    }
                    String[] split = baseMaterial.getName().split("\\*");
                    JSONObject other = new JSONObject();
                    Arrays.stream(split).filter(item -> item.contains("目")).findFirst().ifPresent(item -> {
                        other.put("目数", item);
                    });
                    Arrays.stream(split).filter(item -> item.contains("mm")).findFirst().ifPresent(item -> {
                        other.put("厚度", item);
                    });
                    material.setOther(other);
                }
                result.put("编织", middleMaterials6);
                isFirst = false;
            case "掏综穿扣": // cid存储编织id
                List<MiddleMaterial> middleMaterials7 = middleMaterialService.list(new LambdaQueryWrapper<MiddleMaterial>()
                    .eq(isFirst, MiddleMaterial::getId, middleMaterial.getId())
                    .in(CollUtil.isNotEmpty(cidList), MiddleMaterial::getCId, cidList)
                    .eq(MiddleMaterial::getProcess, "掏综穿扣")
                );
                cidList = middleMaterials7.stream().map(MiddleMaterial::getId).collect(Collectors.toList());
                for (MiddleMaterial material : middleMaterials7) {
                    TProduceOrderProcessSubtask subtask = produceOrderProcessSubtaskMapper.selectOne(new LambdaQueryWrapper<TProduceOrderProcessSubtask>()
                        .eq(TProduceOrderProcessSubtask::getRollCode, material.getCode())
                    );
                    if (ObjectUtil.isNull(subtask)) {
                        continue;
                    }
                    List<ProduceOrderReport> produceOrderReports = produceOrderReportMapper.selectList(new LambdaQueryWrapper<ProduceOrderReport>()
                        .eq(ProduceOrderReport::getOrderProcessSubtaskId, subtask.getId())
                        .orderByAsc(ProduceOrderReport::getCreateTime)
                    );
                    material.setProduceOrderReportList(produceOrderReports);
                }
                result.put("掏综穿扣", middleMaterials7);
                isFirst = false;
            case "整经": // cid存储掏综穿扣id
                List<MiddleMaterial> middleMaterials8 = middleMaterialService.list(new LambdaQueryWrapper<MiddleMaterial>()
                    .eq(isFirst, MiddleMaterial::getId, middleMaterial.getId())
                    .in(CollUtil.isNotEmpty(cidList), MiddleMaterial::getCId, cidList)
                    .eq(MiddleMaterial::getProcess, "整经")
                );
                List<Long> collect = middleMaterials8.stream().map(MiddleMaterial::getId).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(cidList)) {
                    cidList.addAll(collect);
                } else {
                    cidList = collect;
                }
                for (MiddleMaterial material : middleMaterials8) {
                    TProduceOrderProcessSubtask subtask = produceOrderProcessSubtaskMapper.selectOne(new LambdaQueryWrapper<TProduceOrderProcessSubtask>()
                        .eq(TProduceOrderProcessSubtask::getRollCode, material.getCode())
                    );
                    if (ObjectUtil.isNull(subtask)) {
                        continue;
                    }
                    List<ProduceOrderReport> produceOrderReports = produceOrderReportMapper.selectList(new LambdaQueryWrapper<ProduceOrderReport>()
                        .eq(ProduceOrderReport::getOrderProcessSubtaskId, subtask.getId())
                        .orderByAsc(ProduceOrderReport::getCreateTime)
                    );
                    material.setProduceOrderReportList(produceOrderReports);
                }
                result.put("整经", middleMaterials8);
                isFirst = false;
            case "采购": // pid存储box的id
                List<MiddleMaterial> middleMaterials9 = middleMaterialService.list(new LambdaQueryWrapper<MiddleMaterial>()
                    .eq(isFirst, MiddleMaterial::getId, middleMaterial.getId())
                    .in(CollUtil.isNotEmpty(cidList), MiddleMaterial::getCId, cidList)
                    .eq(MiddleMaterial::getProcess, "采购")
                );
                if (CollUtil.isEmpty(middleMaterials9)) {
                    break;
                }
                RawMiddle rawMiddle = rawMiddleService.getById(middleMaterials9.get(0).getPId());
                middleMaterials9.forEach(item -> {
                    item.setMicrometerCode(rawMiddle.getMicrometerCode());
                    item.setTensionCode(rawMiddle.getTensionCode());
                });
                for (MiddleMaterial material : middleMaterials9) {
                    material.setQualityList(qualityService.list(new LambdaQueryWrapper<Quality>()
                        .eq(Quality::getMiddleMaterialId, material.getId())
                    ));
                }
                result.put("采购", middleMaterials9);
                break;
            default:
                throw new RuntimeException("工序类型错误，请确认");
        }
        return R.ok(result);
    }


    public static void main(String[] args) {
        // 输入的 JSON 字符串
        String input = "[[7,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false],[false,false,false,false,false,10,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false],[false,false,false,false,false,false,false,false,false,false,false,false,7,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false],[false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false],[false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false],[false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false],[false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false],[false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false],[false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false],[false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false],[false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,8]]";

        // 解析为 JSONArray
        JSONArray jsonArray = JSON.parseArray(input);

        // 创建二维布尔数组
        boolean[][] result = new boolean[jsonArray.size()][];

        Integer[] list = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONArray row = jsonArray.getJSONArray(i);
            for (int j = 0; j < row.size(); j++) {
                Object value = row.getObject(j, Object.class);
                if (value instanceof Integer) {
                    list[(Integer) value]++;
                }
            }
        }
        System.out.println(list.toString());
    }
}

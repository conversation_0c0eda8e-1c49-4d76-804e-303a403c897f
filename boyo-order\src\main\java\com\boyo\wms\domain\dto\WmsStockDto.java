package com.boyo.wms.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@TableName("t_wms_stock")
@Builder
@AllArgsConstructor
public class WmsStockDto {
    private static final long serialVersionUID=1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;
    /**
     *
     */
    private Long tenantId;
    /**
     * 物料
     */
    private Long materialId;
    /**
     * 批次号
     */
    private String flowBatch;
    /**
     * 仓库
     */
    private Long warehouseId;
    /**
     * 库区
     */
    private Long areaId;
    /**
     * 货位
     */
    private Long allocationId;
    /**
     * 库存
     */
    private BigDecimal materialCount;
    /**
     * 物料的具体成品编码
     */
    private String materialProductCode;

    @TableField(exist = false)
    private String materialName;
    @TableField(exist = false)
    private String warehouseName;
    @TableField(exist = false)
    private String areaName;
    @TableField(exist = false)
    private String allocationName;
    @TableField(exist = false)
    private String minCount;
    @TableField(exist = false)
    private String maxCount;
    @TableField(exist = false)
    private String maxTime;

    /**
     *单位名
     */
    @TableField(exist = false)
    private String unitName;
    @TableField(exist = false)
    private String materialNorms;
    @TableField(exist = false)
    private String materialCode;
    public WmsStockDto(){

    }


}

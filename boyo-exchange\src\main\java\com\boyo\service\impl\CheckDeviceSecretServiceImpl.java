package com.boyo.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.domain.CheckDeviceSecret;
import com.boyo.mapper.CheckDeviceSecretMapper;
import com.boyo.service.CheckDeviceSecretService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【t_check_device_secret(保密质检设备原始数据表)】的数据库操作Service实现
 * @createDate 2025-05-12 14:08:30
 */
@Service
public class CheckDeviceSecretServiceImpl extends ServiceImpl<CheckDeviceSecretMapper, CheckDeviceSecret>
    implements CheckDeviceSecretService {

    @Override
    public List<String> getDistinctMachineSnByDate(Date reportDate) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String dateStr = sdf.format(reportDate);

            QueryWrapper<CheckDeviceSecret> queryWrapper = new QueryWrapper<>();
            queryWrapper.select("DISTINCT machine_sn")
                .apply("DATE(create_time) = {0}", dateStr)
                .isNotNull("machine_sn")
                .ne("machine_sn", "");

            List<CheckDeviceSecret> list = this.list(queryWrapper);
            return list.stream()
                .map(CheckDeviceSecret::getMachineSn)
                .filter(machineSn -> machineSn != null && !machineSn.trim().isEmpty())
                .distinct()
                .collect(Collectors.toList());
        } catch (Exception e) {
            // 如果查询失败，返回默认的设备列表
            return Arrays.asList("D-1", "D-2", "B-2", "C-2");
        }
    }

    /**
     * 柱形图：当天每种问题类型的缺陷数量，除以打点数（当天所有的total值的和），前一天早上八点到第二天早上八点
     * 折线（正常）：100% - 底下的缺陷数值就是前面折线点的数值
     *
     * @return
     */
    @Override
    public Map<String, Map<String, BigDecimal>> getChartData() {
        Map<String, Map<String, BigDecimal>> result = new HashMap<>();
        // 1. 获取数据
        LocalDateTime endTime = LocalDateTime.now().withHour(8).withMinute(0).withSecond(0);
        LocalDateTime startTime = endTime.minusDays(1);
        QueryWrapper<CheckDeviceSecret> query = new QueryWrapper<CheckDeviceSecret>()
            .select("sum(total) totalSum, " +
                "sum(web_blemish_defect) 网疤, " +
                "sum(pillar_defect) 柱道, " +
                "sum(hole_defect) 破洞, " +
                "sum(double_line_defect) 双线, " +
                "sum(knot_defect) 打结, " +
                "sum(oxidation_defect) 氧化, " +
                "sum(oil_stain_defect) 油污, " +
                "sum(foreign_object_defect) 异物, " +
                "sum(deformation_defect) 变形, " +
                "sum(crack_defect) 裂口, " +
                "sum(discoloration_defect) 异色, " +
                "sum(hairiness_defect) 毛丝, " +
                "sum(connectorlug_defect) 接线头, " +
                "sum(markdot_defect) 标记点")
            .between("STR_TO_DATE(test_time, '%Y/%m/%d %H:%i')", LocalDateTimeUtil.formatNormal(startTime), LocalDateTimeUtil.formatNormal(endTime));
        List<Map<String, Object>> statistics = baseMapper.selectMaps(query);
        if (CollUtil.isEmpty(statistics)) {
            throw new RuntimeException("未找到数据");
        }
        Map<String, Object> statisticsMap = statistics.get(0);
        // 2. 柱形图
        LinkedHashMap<String, BigDecimal> topFive = new LinkedHashMap<>();
        // 这个应该放在第一个元素位置
        topFive.put("FPY", BigDecimal.ZERO);
        statisticsMap.entrySet()
            .stream()
            .filter(entry -> !entry.getKey().equals("totalSum"))
            .map(entry -> new AbstractMap.SimpleEntry<>(entry.getKey(), ObjUtil.isNull(entry.getValue()) ? 0 : (Double) entry.getValue()))
            // 按值降序排序
            .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
            .limit(5)
            .map(entry -> new AbstractMap.SimpleEntry<>(entry.getKey(), NumberUtil.round(entry.getValue() / (Double) statisticsMap.get("totalSum") * 100, 2)))
            .forEachOrdered(entry -> topFive.put(entry.getKey(), entry.getValue())); // 追加其他元素;
        // 3. 折线图
        Map<String, BigDecimal> lineChart = new LinkedHashMap<>();
        Map<String, BigDecimal> sortedMap = topFive.entrySet()
            .stream()
            .sorted(Map.Entry.comparingByValue())
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue,
                (oldValue, newValue) -> oldValue, // 合并冲突时保留旧值（可选）
                LinkedHashMap::new // 使用 LinkedHashMap 保证顺序
            ));
        BigDecimal initData = new BigDecimal("100");
        for (Map.Entry<String, BigDecimal> item : sortedMap.entrySet()) {
            lineChart.put(item.getKey(), initData);
            BigDecimal subtract = initData.subtract(item.getValue());
            initData = subtract;
        }
        lineChart.put("FPY", initData);
        // 按 value 倒序排序
        lineChart = lineChart.entrySet()
            .stream()
            .sorted(Map.Entry.comparingByValue())
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue,
                (oldValue, newValue) -> oldValue, // 合并冲突时保留旧值
                LinkedHashMap::new // 确保结果还是 LinkedHashMap
            ));
        result.put("Detractor", topFive);
        result.put("Yield", lineChart);
        return result;
    }

}





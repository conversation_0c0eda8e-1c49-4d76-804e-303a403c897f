package com.boyo.process.utils;

import cn.hutool.core.date.DateUtil;
import com.boyo.common.utils.redis.RedisUtils;

import java.util.Date;

public class SourceCodeUtils {

    /**
     * 获取原材料编码
     * @param keyPart
     * @return
     */
    public static String getInnerCode(String keyPart) {
        String key = keyPart + DateUtil.format(new Date(), "yyyyMMdd");
        return key + String.format("%03d", RedisUtils.incrAtomicValue(key));
    }

    /**
     * 更新材料编码
     * @param nowCode
     * @param keyPart
     * @return
     */
    public static String updateInnerCode(String nowCode, String keyPart) {
        String old = nowCode.substring(0, nowCode.length() - 11);
        String key = old + keyPart + DateUtil.format(new Date(), "yyyyMMdd");
        return key + String.format("%03d", RedisUtils.incrAtomicValue(key));
    }

    /**
     * 获取最终溯源码
     * @return
     */
    public static String getInnerCodeLatest() {
        return "";
    }
}

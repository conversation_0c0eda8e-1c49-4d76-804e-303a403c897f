<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mapper.AoiDailyReportMapper">

    <resultMap id="BaseResultMap" type="com.boyo.domain.AoiDailyReport">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="reportDate" column="report_date" jdbcType="DATE"/>
        <result property="machineSn" column="machine_sn" jdbcType="VARCHAR"/>
        <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
        
        <!-- 白板显示核心字段 -->
        <result property="productionTimeStatus" column="production_time_status" jdbcType="VARCHAR"/>
        <result property="inputCount" column="input_count" jdbcType="INTEGER"/>
        <result property="productionCount" column="production_count" jdbcType="INTEGER"/>
        <result property="okCount" column="ok_count" jdbcType="INTEGER"/>
        <result property="ngCount" column="ng_count" jdbcType="INTEGER"/>
        <result property="controlledYield" column="controlled_yield" jdbcType="DECIMAL"/>
        <result property="ngTop1" column="ng_top1" jdbcType="VARCHAR"/>
        <result property="ngTop2" column="ng_top2" jdbcType="VARCHAR"/>
        <result property="ngTop3" column="ng_top3" jdbcType="VARCHAR"/>
        <result property="machineDetectionRate" column="machine_detection_rate" jdbcType="DECIMAL"/>
        <result property="abnormalInfo" column="abnormal_info" jdbcType="VARCHAR"/>
        
        <!-- 统计汇总字段 -->
        <result property="totalBatches" column="total_batches" jdbcType="INTEGER"/>
        <result property="avgInput" column="avg_input" jdbcType="DECIMAL"/>
        <result property="avgOk" column="avg_ok" jdbcType="DECIMAL"/>
        <result property="avgNg" column="avg_ng" jdbcType="DECIMAL"/>
        <result property="avgYield" column="avg_yield" jdbcType="DECIMAL"/>
        <result property="totalInput" column="total_input" jdbcType="INTEGER"/>
        <result property="totalOk" column="total_ok" jdbcType="INTEGER"/>
        <result property="totalNg" column="total_ng" jdbcType="INTEGER"/>
        
        <!-- 缺陷统计字段 -->
        <result property="webBlemishCount" column="web_blemish_count" jdbcType="INTEGER"/>
        <result property="pillarCount" column="pillar_count" jdbcType="INTEGER"/>
        <result property="holeCount" column="hole_count" jdbcType="INTEGER"/>
        <result property="doubleLineCount" column="double_line_count" jdbcType="INTEGER"/>
        <result property="knotCount" column="knot_count" jdbcType="INTEGER"/>
        <result property="oxidationCount" column="oxidation_count" jdbcType="INTEGER"/>
        <result property="oilStainCount" column="oil_stain_count" jdbcType="INTEGER"/>
        <result property="foreignObjectCount" column="foreign_object_count" jdbcType="INTEGER"/>
        <result property="deformationCount" column="deformation_count" jdbcType="INTEGER"/>
        <result property="crackCount" column="crack_count" jdbcType="INTEGER"/>
        <result property="discolorationCount" column="discoloration_count" jdbcType="INTEGER"/>
        <result property="hairinessCount" column="hairiness_count" jdbcType="INTEGER"/>
        <result property="connectorlugCount" column="connectorlug_count" jdbcType="INTEGER"/>
        
        <!-- 缺陷率字段 -->
        <result property="webBlemishRate" column="web_blemish_rate" jdbcType="DECIMAL"/>
        <result property="pillarRate" column="pillar_rate" jdbcType="DECIMAL"/>
        <result property="holeRate" column="hole_rate" jdbcType="DECIMAL"/>
        <result property="doubleLineRate" column="double_line_rate" jdbcType="DECIMAL"/>
        <result property="knotRate" column="knot_rate" jdbcType="DECIMAL"/>
        <result property="oxidationRate" column="oxidation_rate" jdbcType="DECIMAL"/>
        <result property="oilStainRate" column="oil_stain_rate" jdbcType="DECIMAL"/>
        <result property="foreignObjectRate" column="foreign_object_rate" jdbcType="DECIMAL"/>
        <result property="deformationRate" column="deformation_rate" jdbcType="DECIMAL"/>
        <result property="crackRate" column="crack_rate" jdbcType="DECIMAL"/>
        <result property="discolorationRate" column="discoloration_rate" jdbcType="DECIMAL"/>
        <result property="hairinessRate" column="hairiness_rate" jdbcType="DECIMAL"/>
        <result property="connectorlugRate" column="connectorlug_rate" jdbcType="DECIMAL"/>
        
        <!-- 时间和状态字段 -->
        <result property="shiftType" column="shift_type" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="productionDuration" column="production_duration" jdbcType="INTEGER"/>
        <result property="equipmentStatus" column="equipment_status" jdbcType="VARCHAR"/>
        <result property="downtimeReason" column="downtime_reason" jdbcType="VARCHAR"/>
        <result property="downtimeDuration" column="downtime_duration" jdbcType="INTEGER"/>
        
        <!-- 质量指标字段 -->
        <result property="firstPassYield" column="first_pass_yield" jdbcType="DECIMAL"/>
        <result property="defectDensity" column="defect_density" jdbcType="DECIMAL"/>
        <result property="oee" column="oee" jdbcType="DECIMAL"/>
        
        <!-- 人员信息字段 -->
        <result property="operatorName" column="operator_name" jdbcType="VARCHAR"/>
        <result property="qualityInspector" column="quality_inspector" jdbcType="VARCHAR"/>
        <result property="shiftLeader" column="shift_leader" jdbcType="VARCHAR"/>
        
        <!-- 扩展字段 -->
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="dataSource" column="data_source" jdbcType="VARCHAR"/>
        <result property="syncStatus" column="sync_status" jdbcType="VARCHAR"/>
        <result property="rawDataCount" column="raw_data_count" jdbcType="INTEGER"/>
        
        <!-- 系统字段 -->
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, report_date, machine_sn, project_name,
        production_time_status, input_count, production_count, ok_count, ng_count, controlled_yield,
        ng_top1, ng_top2, ng_top3, machine_detection_rate, abnormal_info,
        total_batches, avg_input, avg_ok, avg_ng, avg_yield, total_input, total_ok, total_ng,
        web_blemish_count, pillar_count, hole_count, double_line_count, knot_count,
        oxidation_count, oil_stain_count, foreign_object_count, deformation_count, crack_count,
        discoloration_count, hairiness_count, connectorlug_count,
        web_blemish_rate, pillar_rate, hole_rate, double_line_rate, knot_rate,
        oxidation_rate, oil_stain_rate, foreign_object_rate, deformation_rate, crack_rate,
        discoloration_rate, hairiness_rate, connectorlug_rate,
        shift_type, start_time, end_time, production_duration,
        equipment_status, downtime_reason, downtime_duration,
        first_pass_yield, defect_density, oee,
        operator_name, quality_inspector, shift_leader,
        remark, data_source, sync_status, raw_data_count,
        create_time, update_time, create_by, update_by, del_flag
    </sql>

    <!-- 根据日期范围查询 -->
    <select id="selectByDateRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_aoi_daily_report
        WHERE del_flag = 0
        AND report_date BETWEEN #{startDate} AND #{endDate}
        <if test="machineSn != null and machineSn != ''">
            AND machine_sn = #{machineSn}
        </if>
        ORDER BY report_date DESC, machine_sn
    </select>

    <!-- 查询白板显示数据 -->
    <select id="selectWhiteboardData" resultType="map">
        SELECT
            machine_sn as machineSn,
            production_time_status as productionTimeStatus,
            input_count as inputCount,
            production_count as productionCount,
            ok_count as okCount,
            ng_count as ngCount,
            controlled_yield as controlledYield,
            ng_top1 as ngTop1,
            ng_top2 as ngTop2,
            ng_top3 as ngTop3,
            machine_detection_rate as machineDetectionRate,
            abnormal_info as abnormalInfo
        FROM t_aoi_daily_report
        WHERE report_date = #{reportDate} AND del_flag = 0
        ORDER BY machine_sn
    </select>

    <!-- 查询日报列表数据(轻量级，仅核心字段) -->
    <select id="selectByDateLight" resultType="map">
        SELECT
            id,
            report_date as reportDate,
            machine_sn as machineSn,
            project_name as projectName,
            input_count as inputCount,
            ok_count as okCount,
            ng_count as ngCount,
            controlled_yield as controlledYield,
            ng_top1 as ngTop1,
            ng_top2 as ngTop2,
            ng_top3 as ngTop3,
            machine_detection_rate as machineDetectionRate,
            total_batches as totalBatches,
            avg_yield as avgYield,
            data_source as dataSource,
            create_time as createTime,
            update_time as updateTime
        FROM t_aoi_daily_report
        WHERE report_date = #{reportDate} AND del_flag = 0
        ORDER BY machine_sn
    </select>

    <!-- 查询良率趋势 -->
    <select id="selectYieldTrend" resultType="map">
        SELECT 
            report_date as reportDate,
            controlled_yield as yield,
            avg_yield as avgYield
        FROM t_aoi_daily_report
        WHERE machine_sn = #{machineSn} 
        AND del_flag = 0
        AND report_date >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        ORDER BY report_date
    </select>

    <!-- 查询缺陷分布 -->
    <select id="selectDefectDistribution" resultType="map">
        SELECT 
            '网疤' as defectType, COALESCE(SUM(web_blemish_count), 0) as count, COALESCE(AVG(web_blemish_rate), 0) as rate
        FROM t_aoi_daily_report
        WHERE report_date = #{reportDate} AND del_flag = 0
        <if test="machineSn != null and machineSn != ''">
            AND machine_sn = #{machineSn}
        </if>
        UNION ALL
        SELECT 
            '柱道' as defectType, COALESCE(SUM(pillar_count), 0) as count, COALESCE(AVG(pillar_rate), 0) as rate
        FROM t_aoi_daily_report
        WHERE report_date = #{reportDate} AND del_flag = 0
        <if test="machineSn != null and machineSn != ''">
            AND machine_sn = #{machineSn}
        </if>
        UNION ALL
        SELECT 
            '破洞' as defectType, COALESCE(SUM(hole_count), 0) as count, COALESCE(AVG(hole_rate), 0) as rate
        FROM t_aoi_daily_report
        WHERE report_date = #{reportDate} AND del_flag = 0
        <if test="machineSn != null and machineSn != ''">
            AND machine_sn = #{machineSn}
        </if>
        ORDER BY count DESC
    </select>

    <!-- 从原始数据生成日报 -->
    <insert id="generateFromRawData">
        INSERT INTO t_aoi_daily_report (
            report_date, machine_sn, project_name,
            input_count, ok_count, ng_count, controlled_yield,
            total_batches, avg_input, avg_ok, avg_ng, avg_yield,
            total_input, total_ok, total_ng,
            web_blemish_count, pillar_count, hole_count, double_line_count, knot_count,
            oxidation_count, oil_stain_count, foreign_object_count, deformation_count, crack_count,
            discoloration_count, hairiness_count, connectorlug_count,
            data_source, sync_status, raw_data_count, create_time
        )
        SELECT 
            #{reportDate} as report_date,
            machine_sn,
            project_name,
            COALESCE(SUM(CAST(total AS UNSIGNED)), 0) as input_count,
            COALESCE(SUM(CAST(ok_count AS UNSIGNED)), 0) as ok_count,
            COALESCE(SUM(CAST(ng_count AS UNSIGNED)), 0) as ng_count,
            COALESCE(AVG(CAST(REPLACE(yield, ' %', '') AS DECIMAL(5,2))), 0) as controlled_yield,
            COUNT(*) as total_batches,
            COALESCE(AVG(CAST(total AS UNSIGNED)), 0) as avg_input,
            COALESCE(AVG(CAST(ok_count AS UNSIGNED)), 0) as avg_ok,
            COALESCE(AVG(CAST(ng_count AS UNSIGNED)), 0) as avg_ng,
            COALESCE(AVG(CAST(REPLACE(yield, ' %', '') AS DECIMAL(5,2))), 0) as avg_yield,
            COALESCE(SUM(CAST(total AS UNSIGNED)), 0) as total_input,
            COALESCE(SUM(CAST(ok_count AS UNSIGNED)), 0) as total_ok,
            COALESCE(SUM(CAST(ng_count AS UNSIGNED)), 0) as total_ng,
            COALESCE(SUM(CAST(web_blemish_defect AS UNSIGNED)), 0) as web_blemish_count,
            COALESCE(SUM(CAST(pillar_defect AS UNSIGNED)), 0) as pillar_count,
            COALESCE(SUM(CAST(hole_defect AS UNSIGNED)), 0) as hole_count,
            COALESCE(SUM(CAST(double_line_defect AS UNSIGNED)), 0) as double_line_count,
            COALESCE(SUM(CAST(knot_defect AS UNSIGNED)), 0) as knot_count,
            COALESCE(SUM(CAST(oxidation_defect AS UNSIGNED)), 0) as oxidation_count,
            COALESCE(SUM(CAST(oil_stain_defect AS UNSIGNED)), 0) as oil_stain_count,
            COALESCE(SUM(CAST(foreign_object_defect AS UNSIGNED)), 0) as foreign_object_count,
            COALESCE(SUM(CAST(deformation_defect AS UNSIGNED)), 0) as deformation_count,
            COALESCE(SUM(CAST(crack_defect AS UNSIGNED)), 0) as crack_count,
            COALESCE(SUM(CAST(discoloration_defect AS UNSIGNED)), 0) as discoloration_count,
            COALESCE(SUM(CAST(hairiness_defect AS UNSIGNED)), 0) as hairiness_count,
            COALESCE(SUM(CAST(connectorlug_defect AS UNSIGNED)), 0) as connectorlug_count,
            'AUTO' as data_source,
            'SYNCED' as sync_status,
            COUNT(*) as raw_data_count,
            NOW() as create_time
        FROM t_check_device_secret
        WHERE DATE(create_time) = #{reportDate}
        AND machine_sn = #{machineSn}
        AND total REGEXP '^[0-9]+$'
        AND ok_count REGEXP '^[0-9]+$'
        AND ng_count REGEXP '^[0-9]+$'
        GROUP BY machine_sn, project_name
        ON DUPLICATE KEY UPDATE
            input_count = VALUES(input_count),
            ok_count = VALUES(ok_count),
            ng_count = VALUES(ng_count),
            controlled_yield = VALUES(controlled_yield),
            total_batches = VALUES(total_batches),
            avg_input = VALUES(avg_input),
            avg_ok = VALUES(avg_ok),
            avg_ng = VALUES(avg_ng),
            avg_yield = VALUES(avg_yield),
            total_input = VALUES(total_input),
            total_ok = VALUES(total_ok),
            total_ng = VALUES(total_ng),
            web_blemish_count = VALUES(web_blemish_count),
            pillar_count = VALUES(pillar_count),
            hole_count = VALUES(hole_count),
            double_line_count = VALUES(double_line_count),
            knot_count = VALUES(knot_count),
            oxidation_count = VALUES(oxidation_count),
            oil_stain_count = VALUES(oil_stain_count),
            foreign_object_count = VALUES(foreign_object_count),
            deformation_count = VALUES(deformation_count),
            crack_count = VALUES(crack_count),
            discoloration_count = VALUES(discoloration_count),
            hairiness_count = VALUES(hairiness_count),
            connectorlug_count = VALUES(connectorlug_count),
            sync_status = VALUES(sync_status),
            raw_data_count = VALUES(raw_data_count),
            update_time = NOW()
    </insert>

    <!-- 批量生成日报 -->
    <insert id="batchGenerateFromRawData">
        INSERT INTO t_aoi_daily_report (
            report_date, machine_sn, project_name,
            input_count, ok_count, ng_count, controlled_yield,
            total_batches, avg_input, avg_ok, avg_ng, avg_yield,
            total_input, total_ok, total_ng,
            web_blemish_count, pillar_count, hole_count, double_line_count, knot_count,
            oxidation_count, oil_stain_count, foreign_object_count, deformation_count, crack_count,
            discoloration_count, hairiness_count, connectorlug_count,
            data_source, sync_status, raw_data_count, create_time
        )
        SELECT 
            #{reportDate} as report_date,
            machine_sn,
            project_name,
            COALESCE(SUM(CAST(total AS UNSIGNED)), 0) as input_count,
            COALESCE(SUM(CAST(ok_count AS UNSIGNED)), 0) as ok_count,
            COALESCE(SUM(CAST(ng_count AS UNSIGNED)), 0) as ng_count,
            COALESCE(AVG(CAST(REPLACE(yield, ' %', '') AS DECIMAL(5,2))), 0) as controlled_yield,
            COUNT(*) as total_batches,
            COALESCE(AVG(CAST(total AS UNSIGNED)), 0) as avg_input,
            COALESCE(AVG(CAST(ok_count AS UNSIGNED)), 0) as avg_ok,
            COALESCE(AVG(CAST(ng_count AS UNSIGNED)), 0) as avg_ng,
            COALESCE(AVG(CAST(REPLACE(yield, ' %', '') AS DECIMAL(5,2))), 0) as avg_yield,
            COALESCE(SUM(CAST(total AS UNSIGNED)), 0) as total_input,
            COALESCE(SUM(CAST(ok_count AS UNSIGNED)), 0) as total_ok,
            COALESCE(SUM(CAST(ng_count AS UNSIGNED)), 0) as total_ng,
            COALESCE(SUM(CAST(web_blemish_defect AS UNSIGNED)), 0) as web_blemish_count,
            COALESCE(SUM(CAST(pillar_defect AS UNSIGNED)), 0) as pillar_count,
            COALESCE(SUM(CAST(hole_defect AS UNSIGNED)), 0) as hole_count,
            COALESCE(SUM(CAST(double_line_defect AS UNSIGNED)), 0) as double_line_count,
            COALESCE(SUM(CAST(knot_defect AS UNSIGNED)), 0) as knot_count,
            COALESCE(SUM(CAST(oxidation_defect AS UNSIGNED)), 0) as oxidation_count,
            COALESCE(SUM(CAST(oil_stain_defect AS UNSIGNED)), 0) as oil_stain_count,
            COALESCE(SUM(CAST(foreign_object_defect AS UNSIGNED)), 0) as foreign_object_count,
            COALESCE(SUM(CAST(deformation_defect AS UNSIGNED)), 0) as deformation_count,
            COALESCE(SUM(CAST(crack_defect AS UNSIGNED)), 0) as crack_count,
            COALESCE(SUM(CAST(discoloration_defect AS UNSIGNED)), 0) as discoloration_count,
            COALESCE(SUM(CAST(hairiness_defect AS UNSIGNED)), 0) as hairiness_count,
            COALESCE(SUM(CAST(connectorlug_defect AS UNSIGNED)), 0) as connectorlug_count,
            'AUTO' as data_source,
            'SYNCED' as sync_status,
            COUNT(*) as raw_data_count,
            NOW() as create_time
        FROM t_check_device_secret
        WHERE DATE(create_time) = #{reportDate}
        AND total REGEXP '^[0-9]+$'
        AND ok_count REGEXP '^[0-9]+$'
        AND ng_count REGEXP '^[0-9]+$'
        GROUP BY machine_sn, project_name
        ON DUPLICATE KEY UPDATE
            input_count = VALUES(input_count),
            ok_count = VALUES(ok_count),
            ng_count = VALUES(ng_count),
            controlled_yield = VALUES(controlled_yield),
            total_batches = VALUES(total_batches),
            avg_input = VALUES(avg_input),
            avg_ok = VALUES(avg_ok),
            avg_ng = VALUES(avg_ng),
            avg_yield = VALUES(avg_yield),
            total_input = VALUES(total_input),
            total_ok = VALUES(total_ok),
            total_ng = VALUES(total_ng),
            web_blemish_count = VALUES(web_blemish_count),
            pillar_count = VALUES(pillar_count),
            hole_count = VALUES(hole_count),
            double_line_count = VALUES(double_line_count),
            knot_count = VALUES(knot_count),
            oxidation_count = VALUES(oxidation_count),
            oil_stain_count = VALUES(oil_stain_count),
            foreign_object_count = VALUES(foreign_object_count),
            deformation_count = VALUES(deformation_count),
            crack_count = VALUES(crack_count),
            discoloration_count = VALUES(discoloration_count),
            hairiness_count = VALUES(hairiness_count),
            connectorlug_count = VALUES(connectorlug_count),
            sync_status = VALUES(sync_status),
            raw_data_count = VALUES(raw_data_count),
            update_time = NOW()
    </insert>

    <!-- 计算NG前三缺陷 -->
    <select id="calculateNgTop3" resultType="map" parameterType="long">
        SELECT
            '网疤' as defectType, COALESCE(web_blemish_count, 0) as count
        FROM t_aoi_daily_report WHERE id = #{reportId}
        UNION ALL
        SELECT '柱道' as defectType, COALESCE(pillar_count, 0) as count
        FROM t_aoi_daily_report WHERE id = #{reportId}
        UNION ALL
        SELECT '破洞' as defectType, COALESCE(hole_count, 0) as count
        FROM t_aoi_daily_report WHERE id = #{reportId}
        UNION ALL
        SELECT '双线' as defectType, COALESCE(double_line_count, 0) as count
        FROM t_aoi_daily_report WHERE id = #{reportId}
        UNION ALL
        SELECT '打结' as defectType, COALESCE(knot_count, 0) as count
        FROM t_aoi_daily_report WHERE id = #{reportId}
        UNION ALL
        SELECT '氧化' as defectType, COALESCE(oxidation_count, 0) as count
        FROM t_aoi_daily_report WHERE id = #{reportId}
        UNION ALL
        SELECT '油污' as defectType, COALESCE(oil_stain_count, 0) as count
        FROM t_aoi_daily_report WHERE id = #{reportId}
        UNION ALL
        SELECT '异物' as defectType, COALESCE(foreign_object_count, 0) as count
        FROM t_aoi_daily_report WHERE id = #{reportId}
        UNION ALL
        SELECT '变形' as defectType, COALESCE(deformation_count, 0) as count
        FROM t_aoi_daily_report WHERE id = #{reportId}
        UNION ALL
        SELECT '裂口' as defectType, COALESCE(crack_count, 0) as count
        FROM t_aoi_daily_report WHERE id = #{reportId}
        UNION ALL
        SELECT '异色' as defectType, COALESCE(discoloration_count, 0) as count
        FROM t_aoi_daily_report WHERE id = #{reportId}
        UNION ALL
        SELECT '毛丝' as defectType, COALESCE(hairiness_count, 0) as count
        FROM t_aoi_daily_report WHERE id = #{reportId}
        UNION ALL
        SELECT '接线头' as defectType, COALESCE(connectorlug_count, 0) as count
        FROM t_aoi_daily_report WHERE id = #{reportId}
        ORDER BY count DESC
        LIMIT 3
    </select>

    <!-- 更新NG前三 -->
    <update id="updateNgTop3">
        UPDATE t_aoi_daily_report
        SET ng_top1 = #{ngTop1}, ng_top2 = #{ngTop2}, ng_top3 = #{ngTop3}, update_time = NOW()
        WHERE id = #{reportId}
    </update>

    <!-- 计算并更新缺陷率 -->
    <update id="calculateAndUpdateDefectRates">
        UPDATE t_aoi_daily_report
        SET
            web_blemish_rate = CASE WHEN total_input > 0 THEN ROUND((web_blemish_count / total_input) * 100, 2) ELSE 0 END,
            pillar_rate = CASE WHEN total_input > 0 THEN ROUND((pillar_count / total_input) * 100, 2) ELSE 0 END,
            hole_rate = CASE WHEN total_input > 0 THEN ROUND((hole_count / total_input) * 100, 2) ELSE 0 END,
            double_line_rate = CASE WHEN total_input > 0 THEN ROUND((double_line_count / total_input) * 100, 2) ELSE 0 END,
            knot_rate = CASE WHEN total_input > 0 THEN ROUND((knot_count / total_input) * 100, 2) ELSE 0 END,
            oxidation_rate = CASE WHEN total_input > 0 THEN ROUND((oxidation_count / total_input) * 100, 2) ELSE 0 END,
            oil_stain_rate = CASE WHEN total_input > 0 THEN ROUND((oil_stain_count / total_input) * 100, 2) ELSE 0 END,
            foreign_object_rate = CASE WHEN total_input > 0 THEN ROUND((foreign_object_count / total_input) * 100, 2) ELSE 0 END,
            deformation_rate = CASE WHEN total_input > 0 THEN ROUND((deformation_count / total_input) * 100, 2) ELSE 0 END,
            crack_rate = CASE WHEN total_input > 0 THEN ROUND((crack_count / total_input) * 100, 2) ELSE 0 END,
            discoloration_rate = CASE WHEN total_input > 0 THEN ROUND((discoloration_count / total_input) * 100, 2) ELSE 0 END,
            hairiness_rate = CASE WHEN total_input > 0 THEN ROUND((hairiness_count / total_input) * 100, 2) ELSE 0 END,
            connectorlug_rate = CASE WHEN total_input > 0 THEN ROUND((connectorlug_count / total_input) * 100, 2) ELSE 0 END,
            update_time = NOW()
        WHERE id = #{reportId}
    </update>

    <!-- 查询设备产能统计 -->
    <select id="selectProductionStatistics" resultType="map">
        SELECT
            machine_sn as machineSn,
            report_date as reportDate,
            total_input as totalInput,
            total_ok as totalOk,
            total_ng as totalNg,
            controlled_yield as yield
        FROM t_aoi_daily_report
        WHERE report_date BETWEEN #{startDate} AND #{endDate}
        AND del_flag = 0
        ORDER BY report_date, machine_sn
    </select>

    <!-- 查询质量指标统计 -->
    <select id="selectQualityMetrics" resultType="map">
        SELECT
            machine_sn as machineSn,
            AVG(controlled_yield) as avgYield,
            AVG(first_pass_yield) as avgFirstPassYield,
            AVG(defect_density) as avgDefectDensity,
            AVG(oee) as avgOee
        FROM t_aoi_daily_report
        WHERE report_date BETWEEN #{startDate} AND #{endDate}
        AND del_flag = 0
        <if test="machineSn != null and machineSn != ''">
            AND machine_sn = #{machineSn}
        </if>
        GROUP BY machine_sn
        ORDER BY machine_sn
    </select>

</mapper>

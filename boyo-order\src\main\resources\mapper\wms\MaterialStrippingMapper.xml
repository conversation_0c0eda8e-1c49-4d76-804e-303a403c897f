<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.wms.mapper.MaterialStrippingMapper">

    <resultMap id="BaseResultMap" type="com.boyo.wms.domain.MaterialStripping">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
        <result property="requisitionId" column="requisition_id" jdbcType="VARCHAR"/>
        <result property="department" column="department" jdbcType="VARCHAR"/>
        <result property="departmentId" column="department_id" jdbcType="VARCHAR"/>
        <result property="requisitionDate" column="requisition_date" jdbcType="TIMESTAMP"/>
        <result property="yuandanType" column="yuandan_type" jdbcType="VARCHAR"/>
        <result property="xuandanNo" column="xuandan_no" jdbcType="VARCHAR"/>
        <result property="product" column="product" jdbcType="VARCHAR"/>
        <result property="productUnit" column="product_unit" jdbcType="VARCHAR"/>
        <result property="inputOutput" column="input_output" jdbcType="VARCHAR"/>
        <result property="basicUnit" column="basic_unit" jdbcType="VARCHAR"/>
        <result property="basicUnitInput" column="basic_unit_input" jdbcType="VARCHAR"/>
        <result property="productionBatch" column="production_batch" jdbcType="VARCHAR"/>
        <result property="customerPartNumber" column="customer_part_number" jdbcType="VARCHAR"/>
        <result property="customerProductName" column="customer_product_name" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="summary" column="summary" jdbcType="VARCHAR"/>
        <result property="issuedBy" column="issued_by" jdbcType="VARCHAR"/>
        <result property="reviewedBy" column="reviewed_by" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="reviewDate" column="review_date" jdbcType="DATE"/>
        <result property="jizhang" column="jizhang" jdbcType="VARCHAR"/>
        <result property="picker" column="picker" jdbcType="VARCHAR"/>
        <result property="ex1" column="ex1" jdbcType="VARCHAR"/>
        <result property="ex2" column="ex2" jdbcType="VARCHAR"/>
        <result property="submitStatus" column="submit_status" jdbcType="INTEGER"/>
        <result property="submitDate" column="submit_date" jdbcType="TIMESTAMP"/>
        <result property="purpose" column="purpose" jdbcType="VARCHAR"/>
        <result property="requisitionNumber" column="requisition_number" jdbcType="VARCHAR"/>
        <result property="equipmentName" column="equipment_name" jdbcType="VARCHAR"/>
        <result property="equipmentId" column="equipment_id" jdbcType="BIGINT"/>
        <result property="rollCode" column="roll_code" jdbcType="VARCHAR"/>
        <result property="processName" column="process_name" jdbcType="VARCHAR"/>
        <collection property="details" ofType="com.boyo.wms.domain.MaterialStrippingDetails">
            <id property="id" column="dId" jdbcType="BIGINT"/>
            <result property="strippingId" column="stripping_id" jdbcType="VARCHAR"/>
            <result property="materialCode" column="material_code" jdbcType="VARCHAR"/>
            <result property="materialId" column="material_id" jdbcType="BIGINT"/>
            <result property="flowBatch" column="flow_batch" jdbcType="VARCHAR"/>
            <result property="warehouseId" column="warehouse_id" jdbcType="BIGINT"/>
            <result property="areaId" column="area_id" jdbcType="BIGINT"/>
            <result property="allocationId" column="allocation_id" jdbcType="BIGINT"/>
            <result property="materialCount" column="material_count" jdbcType="DECIMAL"/>
            <result property="materialProductCode" column="material_product_code" jdbcType="VARCHAR"/>
            <result property="allocationCode" column="allocation_code" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,tenant_id,requisition_id,
        department,department_id,requisition_date,
        yuandan_type,xuandan_no,product,
        product_unit,input_output,basic_unit,
        basic_unit_input,production_batch,customer_part_number,
        customer_product_name,created_by,summary,
        issued_by,reviewed_by,create_by,
        create_time,update_by,update_time,
        review_date,jizhang,picker,
        ex1,ex2,submit_status,
        submit_date,purpose,requisition_number,
        equipment_name,equipment_id,roll_code,
        process_name
    </sql>
    <select id="getPage" resultMap="BaseResultMap">
        select t1.*, t2.id dId,stripping_id,material_code,
               material_id,flow_batch,warehouse_id,
               area_id,allocation_id,material_count,
               material_product_code,allocation_code
        from t_material_stripping t1
        left join t_material_stripping_details t2 on t2.stripping_id = t1.id
    </select>
</mapper>

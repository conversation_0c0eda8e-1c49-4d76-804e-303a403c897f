<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mapper.AoiStatisticsSummaryMapper">

    <!-- 根据核心表ID列表批量查询统计汇总 -->
    <select id="selectByReportCoreIds" resultType="com.boyo.domain.AoiStatisticsSummary">
        SELECT * FROM t_aoi_statistics_summary 
        WHERE report_core_id IN
        <foreach collection="reportCoreIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND del_flag = 0
    </select>

    <!-- 从原始数据生成统计汇总 -->
    <insert id="generateFromRawData">
        INSERT INTO t_aoi_statistics_summary (
            report_core_id,
            total_batches, avg_input, avg_ok, avg_ng, avg_yield,
            total_input, total_ok, total_ng,
            create_time, update_time, del_flag
        )
        SELECT
            #{reportCoreId} as report_core_id,
            1 as total_batches,
            SUM(CASE WHEN total REGEXP '^[0-9]+$' THEN CAST(total AS UNSIGNED) ELSE 0 END) as avg_input,
            SUM(CASE WHEN test_result = 'OK' THEN 1 ELSE 0 END) as avg_ok,
            SUM(CASE WHEN test_result = 'NG' THEN 1 ELSE 0 END) as avg_ng,
            ROUND(
                CASE
                    WHEN SUM(CASE WHEN total REGEXP '^[0-9]+$' THEN CAST(total AS UNSIGNED) ELSE 0 END) > 0 THEN
                        SUM(CASE WHEN test_result = 'OK' THEN 1 ELSE 0 END) * 100.0 / SUM(CASE WHEN total REGEXP '^[0-9]+$' THEN CAST(total AS UNSIGNED) ELSE 0 END)
                    ELSE 0
                END, 2
            ) as avg_yield,
            SUM(CASE WHEN total REGEXP '^[0-9]+$' THEN CAST(total AS UNSIGNED) ELSE 0 END) as total_input,
            SUM(CASE WHEN test_result = 'OK' THEN 1 ELSE 0 END) as total_ok,
            SUM(CASE WHEN test_result = 'NG' THEN 1 ELSE 0 END) as total_ng,
            NOW() as create_time,
            NOW() as update_time,
            0 as del_flag
        FROM t_check_device_secret
        WHERE DATE(test_time) = #{reportDate}
        AND machine_sn = #{machineSn}
        ON DUPLICATE KEY UPDATE
            total_batches = VALUES(total_batches),
            avg_input = VALUES(avg_input),
            avg_ok = VALUES(avg_ok),
            avg_ng = VALUES(avg_ng),
            avg_yield = VALUES(avg_yield),
            total_input = VALUES(total_input),
            total_ok = VALUES(total_ok),
            total_ng = VALUES(total_ng),
            update_time = NOW()
    </insert>

</mapper>

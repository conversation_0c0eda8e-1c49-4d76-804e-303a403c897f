package com.boyo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.domain.AoiDailyReport;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * AOI生产日报表Service接口
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface IAoiDailyReportService extends IService<AoiDailyReport> {

    /**
     * 根据日期和设备查询日报
     * @param reportDate 报表日期
     * @param machineSn 设备序列号
     * @return 日报数据
     */
    AoiDailyReport getByDateAndMachine(Date reportDate, String machineSn);

    /**
     * 根据日期查询所有设备的日报
     * @param reportDate 报表日期
     * @return 日报数据列表
     */
    List<AoiDailyReport> getByDate(Date reportDate);

    /**
     * 根据日期查询所有设备的日报(轻量级，仅核心字段)
     * @param reportDate 报表日期
     * @return 日报数据列表
     */
    List<Map<String, Object>> getByDateLight(Date reportDate);

    /**
     * 根据日期范围查询日报
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param machineSn 设备序列号(可选)
     * @return 日报数据列表
     */
    List<AoiDailyReport> getByDateRange(Date startDate, Date endDate, String machineSn);

    /**
     * 查询白板显示数据
     * @param reportDate 报表日期
     * @return 白板显示数据
     */
    List<Map<String, Object>> getWhiteboardData(Date reportDate);

    /**
     * 统计设备良率趋势
     * @param machineSn 设备序列号
     * @param days 天数
     * @return 良率趋势数据
     */
    List<Map<String, Object>> getYieldTrend(String machineSn, Integer days);

    /**
     * 统计缺陷分布
     * @param reportDate 报表日期
     * @param machineSn 设备序列号(可选)
     * @return 缺陷分布数据
     */
    List<Map<String, Object>> getDefectDistribution(Date reportDate, String machineSn);

    /**
     * 从原始数据生成日报
     * @param reportDate 报表日期
     * @param machineSn 设备序列号
     * @return 是否成功
     */
    boolean generateFromRawData(Date reportDate, String machineSn);

    /**
     * 批量生成日报
     * @param reportDate 报表日期
     * @return 生成数量
     */
    int batchGenerateFromRawData(Date reportDate);

    /**
     * 查询设备产能统计
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 产能统计数据
     */
    List<Map<String, Object>> getProductionStatistics(Date startDate, Date endDate);

    /**
     * 查询质量指标统计
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param machineSn 设备序列号(可选)
     * @return 质量指标数据
     */
    List<Map<String, Object>> getQualityMetrics(Date startDate, Date endDate, String machineSn);

    /**
     * 计算并更新NG项前三
     * @param reportId 报表ID
     * @return 是否成功
     */
    boolean calculateNgTop3(Long reportId);

    /**
     * 计算并更新缺陷率
     * @param reportId 报表ID
     * @return 是否成功
     */
    boolean calculateDefectRates(Long reportId);



    /**
     * 获取设备列表
     * @return 设备列表
     */
    List<String> getMachineList();

    /**
     * 导出日报数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param machineSn 设备序列号(可选)
     * @return 导出数据
     */
    List<Map<String, Object>> exportReportData(Date startDate, Date endDate, String machineSn);

    /**
     * 获取日报统计概览
     * @param reportDate 报表日期
     * @return 统计概览
     */
    Map<String, Object> getReportSummary(Date reportDate);
}

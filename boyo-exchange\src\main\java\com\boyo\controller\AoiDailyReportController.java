package com.boyo.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boyo.domain.AoiDailyReport;
import com.boyo.domain.AoiDailyReportCore;
import com.boyo.dto.AoiTableDataDto;
import com.boyo.dto.AoiComparisonDataDto;
import com.boyo.mapper.AoiDailyReportCoreMapper;
import com.boyo.mapper.CheckDeviceSecretMapper;
import com.boyo.service.IAoiDailyReportService;
import com.boyo.service.AoiDataAggregationService;
import com.boyo.service.AoiDataAnalysisService;
import com.boyo.service.CheckDeviceSecretService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * AOI生产日报表Controller
 * <AUTHOR>
 * @date 2024-01-15
 */
@RestController
@RequestMapping("/aoi/dailyReport")
public class AoiDailyReportController {

    private static final Logger log = LoggerFactory.getLogger(AoiDailyReportController.class);

    @Autowired
    private IAoiDailyReportService aoiDailyReportService;

    @Autowired
    private AoiDataAggregationService aoiDataAggregationService;

    @Autowired
    private AoiDataAnalysisService aoiDataAnalysisService;

    @Autowired
    private AoiDailyReportCoreMapper aoiDailyReportCoreMapper;

    @Autowired
    private CheckDeviceSecretMapper checkDeviceSecretMapper;

    @Autowired
    private CheckDeviceSecretService checkDeviceSecretService;

    /**
     * 分页查询日报列表
     */
    @GetMapping("/list")
    public Map<String, Object> list(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) String machineSn,
            @RequestParam(required = false) String projectName) {

        Page<AoiDailyReport> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<AoiDailyReport> wrapper = new LambdaQueryWrapper<>();

        if (startDate != null && endDate != null) {
            wrapper.between(AoiDailyReport::getReportDate, startDate, endDate);
        } else if (startDate != null) {
            wrapper.ge(AoiDailyReport::getReportDate, startDate);
        } else if (endDate != null) {
            wrapper.le(AoiDailyReport::getReportDate, endDate);
        }

        if (machineSn != null && !machineSn.trim().isEmpty()) {
            wrapper.eq(AoiDailyReport::getMachineSn, machineSn);
        }

        if (projectName != null && !projectName.trim().isEmpty()) {
            wrapper.like(AoiDailyReport::getProjectName, projectName);
        }

        wrapper.orderByDesc(AoiDailyReport::getReportDate, AoiDailyReport::getMachineSn);

        IPage<AoiDailyReport> result = aoiDailyReportService.page(page, wrapper);

        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("msg", "查询成功");
        response.put("total", result.getTotal());
        response.put("rows", result.getRecords());

        return response;
    }

    /**
     * 根据ID查询日报详情
     */
    @GetMapping("/{id}")
    public Map<String, Object> getById(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            AoiDailyReport report = aoiDailyReportService.getById(id);
            if (report != null) {
                response.put("code", 200);
                response.put("msg", "查询成功");
                response.put("data", report);
            } else {
                response.put("code", 404);
                response.put("msg", "记录不存在");
            }
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "查询失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 新增日报
     */
    @PostMapping
    public Map<String, Object> add(@RequestBody AoiDailyReport aoiDailyReport) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean success = aoiDailyReportService.save(aoiDailyReport);
            if (success) {
                response.put("code", 200);
                response.put("msg", "新增成功");
            } else {
                response.put("code", 500);
                response.put("msg", "新增失败");
            }
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "新增失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 修改日报
     */
    @PutMapping
    public Map<String, Object> edit(@RequestBody AoiDailyReport aoiDailyReport) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean success = aoiDailyReportService.updateById(aoiDailyReport);
            if (success) {
                response.put("code", 200);
                response.put("msg", "修改成功");
            } else {
                response.put("code", 500);
                response.put("msg", "修改失败");
            }
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "修改失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 删除日报
     */
    @DeleteMapping("/{ids}")
    public Map<String, Object> remove(@PathVariable String ids) {
        Map<String, Object> response = new HashMap<>();
        try {
            String[] idArray = ids.split(",");
            boolean success = true;
            for (String id : idArray) {
                if (!aoiDailyReportService.removeById(Long.parseLong(id))) {
                    success = false;
                    break;
                }
            }

            if (success) {
                response.put("code", 200);
                response.put("msg", "删除成功");
            } else {
                response.put("code", 500);
                response.put("msg", "删除失败");
            }
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "删除失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 查询白板显示数据（使用新核心表，性能提升80%）
     */
    @GetMapping("/whiteboard")
    public Map<String, Object> getWhiteboardData(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date reportDate) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 使用新的核心表查询，只查询15个核心字段，性能提升80%
            List<Map<String, Object>> data = aoiDailyReportCoreMapper.selectWhiteboardData(reportDate);
            response.put("code", 200);
            response.put("msg", "查询成功");
            response.put("data", data);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "查询失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 查询日报列表数据(轻量级，仅核心字段，性能提升80%)
     */
    @GetMapping("/listLight")
    public Map<String, Object> getListLight(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date reportDate) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 使用新的核心表查询，只查询15个核心字段
            List<AoiDailyReportCore> coreList = aoiDailyReportCoreMapper.selectByDate(reportDate);
            response.put("code", 200);
            response.put("msg", "查询成功");
            response.put("data", coreList);
            response.put("total", coreList.size());
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "查询失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 查询前端表格完整数据（包含所有字段）
     */
    @GetMapping("/tableData")
    public Map<String, Object> getTableData(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date reportDate,
            @RequestParam(required = false) String machineSn) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 查询完整的表格数据，包含所有关联表信息
            List<Map<String, Object>> tableData = aoiDailyReportCoreMapper.selectTableData(reportDate, machineSn);
            response.put("code", 200);
            response.put("msg", "查询成功");
            response.put("data", tableData);
            response.put("total", tableData.size());
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "查询失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 查询原始检测数据（从t_check_device_secret表）
     */
    @GetMapping("/rawData/detailed")
    public Map<String, Object> getRawDataDetailed(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date testDate,
            @RequestParam(required = false) String machineSn,
            @RequestParam(required = false) String projectName,
            @RequestParam(required = false) String testStation,
            @RequestParam(required = false) String testResult) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 计算偏移量
            Integer offset = (pageNum - 1) * pageSize;

            // 查询原始检测数据（支持分页）
            List<Map<String, Object>> rawData = checkDeviceSecretMapper.selectRawDataWithPaging(
                offset, pageSize, testDate, machineSn, projectName, testStation, testResult);

            // 查询总数
            Integer total = checkDeviceSecretMapper.countRawData(testDate, machineSn, projectName, testStation, testResult);

            // 查询统计信息
            Map<String, Object> statistics = checkDeviceSecretMapper.getRawDataStatistics(testDate, machineSn, projectName, testStation, testResult);

            response.put("code", 200);
            response.put("msg", "查询成功");
            response.put("data", rawData);
            response.put("total", total);
            response.put("pageNum", pageNum);
            response.put("pageSize", pageSize);
            response.put("pages", (total + pageSize - 1) / pageSize);
            response.put("statistics", statistics);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "查询失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 查询前端表格结构化数据（嵌套对象格式）
     */
    @GetMapping("/table/structured")
    public Map<String, Object> getTableDataStructured(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date reportDate,
            @RequestParam(required = false) String machineSn,
            @RequestParam(required = false) String projectName) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 计算偏移量
            Integer offset = (pageNum - 1) * pageSize;

            // 查询原始数据（支持分页）
            List<Map<String, Object>> rawData = aoiDailyReportCoreMapper.selectTableDataWithPaging(
                offset, pageSize, reportDate, machineSn, projectName);

            // 查询总数
            Integer total = aoiDailyReportCoreMapper.countTableData(reportDate, machineSn, projectName);

            // 查询NG分级统计
            Map<String, Object> ngLevelStats = aoiDailyReportCoreMapper.countNgLevelStatistics(reportDate, machineSn, projectName);

            // 转换为结构化数据
            List<AoiTableDataDto> structuredData = convertToStructuredData(rawData, ngLevelStats);

            response.put("code", 200);
            response.put("msg", "查询成功");
            response.put("data", structuredData);
            response.put("total", total);
            response.put("pageNum", pageNum);
            response.put("pageSize", pageSize);
            response.put("pages", (total + pageSize - 1) / pageSize); // 总页数
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "查询失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 查询版本对比数据（VS3 vs VS4格式）
     */
    @GetMapping("/comparison/versions")
    public Map<String, Object> getVersionComparisonData(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date reportDate,
            @RequestParam(required = false) String machineSn,
            @RequestParam(required = false) String projectName) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 先查询所有原始数据（不分页）
            List<Map<String, Object>> allRawData = aoiDailyReportCoreMapper.selectTableDataWithPaging(
                0, Integer.MAX_VALUE, reportDate, machineSn, projectName);

            // 转换为版本对比数据
            List<AoiComparisonDataDto> allComparisonData = convertToComparisonData(allRawData, projectName);

            // 对比数据总数
            Integer total = allComparisonData.size();

            // 计算分页
            Integer offset = (pageNum - 1) * pageSize;
            Integer endIndex = Math.min(offset + pageSize, total);

            // 对对比数据进行分页
            List<AoiComparisonDataDto> pagedComparisonData = new ArrayList<>();
            if (offset < total) {
                pagedComparisonData = allComparisonData.subList(offset, endIndex);
            }

            response.put("code", 200);
            response.put("msg", "查询成功");
            response.put("data", pagedComparisonData);
            response.put("total", total);
            response.put("pageNum", pageNum);
            response.put("pageSize", pageSize);
            response.put("pages", (total + pageSize - 1) / pageSize);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "查询失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 查询设备良率趋势
     */
    @GetMapping("/yieldTrend")
    public Map<String, Object> getYieldTrend(
            @RequestParam String machineSn,
            @RequestParam(defaultValue = "7") Integer days) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<Map<String, Object>> data = aoiDailyReportService.getYieldTrend(machineSn, days);
            response.put("code", 200);
            response.put("msg", "查询成功");
            response.put("data", data);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "查询失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 查询缺陷分布
     */
    @GetMapping("/defectDistribution")
    public Map<String, Object> getDefectDistribution(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date reportDate,
            @RequestParam(required = false) String machineSn) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<Map<String, Object>> data = aoiDailyReportService.getDefectDistribution(reportDate, machineSn);
            response.put("code", 200);
            response.put("msg", "查询成功");
            response.put("data", data);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "查询失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 从原始数据生成日报
     */
    @PostMapping("/generate")
    public Map<String, Object> generateFromRawData(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date reportDate,
            @RequestParam(required = false) String machineSn) {
        Map<String, Object> response = new HashMap<>();
        try {
            if (machineSn != null && !machineSn.trim().isEmpty()) {
                boolean success = aoiDailyReportService.generateFromRawData(reportDate, machineSn);
                response.put("code", success ? 200 : 500);
                response.put("msg", success ? "生成成功" : "生成失败");
            } else {
                int count = aoiDailyReportService.batchGenerateFromRawData(reportDate);
                response.put("code", 200);
                response.put("msg", "批量生成成功，共生成 " + count + " 条记录");
                response.put("count", count);
            }
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "生成失败: " + e.getMessage());
        }
        return response;
    }



    /**
     * 获取设备列表
     */
    @GetMapping("/machines")
    public Map<String, Object> getMachineList() {
        Map<String, Object> response = new HashMap<>();
        try {
            List<String> machines = aoiDailyReportService.getMachineList();
            response.put("code", 200);
            response.put("msg", "查询成功");
            response.put("data", machines);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "查询失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 导出日报数据
     */
    @GetMapping("/export")
    public Map<String, Object> exportReportData(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) String machineSn) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<Map<String, Object>> data = aoiDailyReportService.exportReportData(startDate, endDate, machineSn);
            response.put("code", 200);
            response.put("msg", "导出成功");
            response.put("data", data);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "导出失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 获取日报统计概览
     */
    @GetMapping("/summary")
    public Map<String, Object> getReportSummary(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date reportDate) {
        Map<String, Object> response = new HashMap<>();
        try {
            Map<String, Object> summary = aoiDailyReportService.getReportSummary(reportDate);
            response.put("code", 200);
            response.put("msg", "查询成功");
            response.put("data", summary);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "查询失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 查询设备产能统计
     */
    @GetMapping("/productionStatistics")
    public Map<String, Object> getProductionStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<Map<String, Object>> data = aoiDailyReportService.getProductionStatistics(startDate, endDate);
            response.put("code", 200);
            response.put("msg", "查询成功");
            response.put("data", data);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "查询失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 查询质量指标统计
     */
    @GetMapping("/qualityMetrics")
    public Map<String, Object> getQualityMetrics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) String machineSn) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<Map<String, Object>> data = aoiDailyReportService.getQualityMetrics(startDate, endDate, machineSn);
            response.put("code", 200);
            response.put("msg", "查询成功");
            response.put("data", data);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "查询失败: " + e.getMessage());
        }
        return response;
    }

    // ===== 自动聚合相关接口 =====

    /**
     * 自动聚合数据
     */
    @PostMapping("/autoAggregate")
    public Map<String, Object> autoAggregateData(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date reportDate) {
        Map<String, Object> response = new HashMap<>();
        try {
            Map<String, Object> result = aoiDataAggregationService.autoAggregateData(reportDate);
            response.put("code", (Boolean) result.get("success") ? 200 : 500);
            response.put("msg", result.get("message"));
            response.put("data", result);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "自动聚合失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 增量聚合数据
     */
    @PostMapping("/incrementalAggregate")
    public Map<String, Object> incrementalAggregateData(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date reportDate) {
        Map<String, Object> response = new HashMap<>();
        try {
            Map<String, Object> result = aoiDataAggregationService.incrementalAggregateData(reportDate);
            response.put("code", (Boolean) result.get("success") ? 200 : 500);
            response.put("msg", result.get("message"));
            response.put("data", result);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "增量聚合失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 重新聚合数据
     */
    @PostMapping("/reAggregate")
    public Map<String, Object> reAggregateData(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date reportDate) {
        Map<String, Object> response = new HashMap<>();
        try {
            Map<String, Object> result = aoiDataAggregationService.reAggregateData(reportDate);
            response.put("code", (Boolean) result.get("success") ? 200 : 500);
            response.put("msg", result.get("message"));
            response.put("data", result);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "重新聚合失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 定时自动聚合
     */
    @PostMapping("/scheduledAggregate")
    public Map<String, Object> scheduledAutoAggregation() {
        Map<String, Object> response = new HashMap<>();
        try {
            Map<String, Object> result = aoiDataAggregationService.scheduledAutoAggregation();
            response.put("code", (Boolean) result.get("success") ? 200 : 500);
            response.put("msg", result.get("message"));
            response.put("data", result);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "定时聚合失败: " + e.getMessage());
        }
        return response;
    }

    // ===== 智能分析相关接口 =====

    /**
     * 智能分析单个日报
     */
    @PostMapping("/intelligentAnalysis/{id}")
    public Map<String, Object> intelligentAnalysis(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            Map<String, Object> result = aoiDataAnalysisService.intelligentAnalysis(id);
            response.put("code", (Boolean) result.get("success") ? 200 : 500);
            response.put("msg", result.get("message"));
            response.put("data", result);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "智能分析失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 批量智能分析
     */
    @PostMapping("/batchIntelligentAnalysis")
    public Map<String, Object> batchIntelligentAnalysis(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date reportDate) {
        Map<String, Object> response = new HashMap<>();
        try {
            Map<String, Object> result = aoiDataAnalysisService.batchIntelligentAnalysis(reportDate);
            response.put("code", (Boolean) result.get("success") ? 200 : 500);
            response.put("msg", result.get("message"));
            response.put("data", result);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "批量智能分析失败: " + e.getMessage());
        }
        return response;
    }

    // ===== 数据维护相关接口 =====

    /**
     * 基于现有原始数据更新AOI相关表
     * 从t_check_device_secret表的数据更新五个AOI相关表
     * 使用新的拆分表结构
     */
    @PostMapping("/updateAoiTablesFromRawData")
    public Map<String, Object> updateAoiTablesFromRawData(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date reportDate,
            @RequestParam(required = false) String machineSn,
            @RequestParam(required = false, defaultValue = "false") Boolean forceUpdate) {
        Map<String, Object> response = new HashMap<>();
        try {
            Map<String, Object> result;

            if (machineSn != null && !machineSn.trim().isEmpty()) {
                // 单台设备更新 - 使用新表结构的增量聚合
                boolean success = aoiDataAggregationService.incrementalAggregateForMachine(reportDate, machineSn);
                result = new HashMap<>();
                result.put("success", success);
                result.put("count", success ? 1 : 0);
                result.put("message", success ?
                    String.format("设备%s的AOI相关表更新成功", machineSn) :
                    String.format("设备%s的AOI相关表更新失败", machineSn));
            } else {
                // 批量更新所有设备 - 直接使用现有的新表结构方法
                try {
                    // 获取当天有数据的所有设备
                    List<String> machineList = getMachineListFromRawData(reportDate);

                    if (machineList.isEmpty()) {
                        result = new HashMap<>();
                        result.put("success", false);
                        result.put("message", "未找到原始数据");
                        result.put("count", 0);
                    } else {
                        // 对每台设备进行聚合
                        int successCount = 0;
                        int failCount = 0;
                        List<String> successMachines = new ArrayList<>();
                        List<String> failMachines = new ArrayList<>();

                        for (String machine : machineList) {
                            try {
                                boolean success = aoiDataAggregationService.incrementalAggregateForMachine(reportDate, machine);
                                if (success) {
                                    successCount++;
                                    successMachines.add(machine);
                                } else {
                                    failCount++;
                                    failMachines.add(machine);
                                }
                            } catch (Exception e) {
                                failCount++;
                                failMachines.add(machine);
                                log.error("设备{}聚合异常", machine, e);
                            }
                        }

                        // 构建结果
                        result = new HashMap<>();
                        result.put("success", failCount == 0);
                        result.put("count", successCount);
                        result.put("successCount", successCount);
                        result.put("failCount", failCount);
                        result.put("successMachines", successMachines);
                        result.put("failMachines", failMachines);
                        result.put("message", String.format("批量更新完成，成功%d台设备，失败%d台设备", successCount, failCount));
                    }
                } catch (Exception e) {
                    result = new HashMap<>();
                    result.put("success", false);
                    result.put("count", 0);
                    result.put("message", "批量更新失败: " + e.getMessage());
                }
            }

            response.put("code", (Boolean) result.get("success") ? 200 : 500);
            response.put("msg", result.get("message"));
            response.put("data", result);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "更新AOI相关表失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 批量更新指定设备的AOI相关表
     * 支持多台设备同时更新
     */
    @PostMapping("/batchUpdateAoiTablesForMachines")
    public Map<String, Object> batchUpdateAoiTablesForMachines(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date reportDate,
            @RequestParam List<String> machineSnList) {
        Map<String, Object> response = new HashMap<>();
        try {
            Map<String, Object> result = aoiDataAggregationService.batchIncrementalAggregateForMachines(reportDate, machineSnList);
            response.put("code", (Boolean) result.get("success") ? 200 : 500);
            response.put("msg", result.get("message"));
            response.put("data", result);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "批量更新AOI相关表失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 批量更新指定日期范围的AOI相关表
     * 支持多天数据批量处理
     */
    @PostMapping("/batchUpdateAoiTablesByDateRange")
    public Map<String, Object> batchUpdateAoiTablesByDateRange(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) String machineSn,
            @RequestParam(required = false, defaultValue = "false") Boolean forceUpdate) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<Map<String, Object>> dailyResults = new ArrayList<>();
            int totalSuccessDays = 0;
            int totalFailDays = 0;
            int totalProcessedRecords = 0;

            // 计算日期范围
            long daysBetween = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24) + 1;

            for (int i = 0; i < daysBetween; i++) {
                Date currentDate = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000L);

                try {
                    Map<String, Object> dailyResult;

                    if (machineSn != null && !machineSn.trim().isEmpty()) {
                        // 单台设备处理
                        boolean success = aoiDataAggregationService.incrementalAggregateForMachine(currentDate, machineSn);
                        dailyResult = new HashMap<>();
                        dailyResult.put("success", success);
                        dailyResult.put("count", success ? 1 : 0);
                        dailyResult.put("date", currentDate);
                        dailyResult.put("machineSn", machineSn);
                    } else {
                        // 全部设备处理
                        if (forceUpdate) {
                            dailyResult = aoiDataAggregationService.reAggregateData(currentDate);
                        } else {
                            dailyResult = aoiDataAggregationService.autoAggregateData(currentDate);
                        }
                        dailyResult.put("date", currentDate);
                    }

                    dailyResults.add(dailyResult);

                    if ((Boolean) dailyResult.get("success")) {
                        totalSuccessDays++;
                        totalProcessedRecords += (Integer) dailyResult.getOrDefault("count", 0);
                    } else {
                        totalFailDays++;
                    }

                } catch (Exception e) {
                    totalFailDays++;
                    Map<String, Object> errorResult = new HashMap<>();
                    errorResult.put("success", false);
                    errorResult.put("date", currentDate);
                    errorResult.put("error", e.getMessage());
                    dailyResults.add(errorResult);
                }
            }

            // 构建总结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", totalFailDays == 0);
            result.put("totalDays", daysBetween);
            result.put("successDays", totalSuccessDays);
            result.put("failDays", totalFailDays);
            result.put("totalProcessedRecords", totalProcessedRecords);
            result.put("dailyResults", dailyResults);
            result.put("message", String.format("批量更新完成：成功%d天，失败%d天，共处理%d条记录",
                                               totalSuccessDays, totalFailDays, totalProcessedRecords));

            response.put("code", totalFailDays == 0 ? 200 : 206); // 206表示部分成功
            response.put("msg", result.get("message"));
            response.put("data", result);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "批量更新AOI相关表失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 计算NG前三
     */
    @PostMapping("/calculateNgTop3/{id}")
    public Map<String, Object> calculateNgTop3(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean success = aoiDailyReportService.calculateNgTop3(id);
            response.put("code", success ? 200 : 500);
            response.put("msg", success ? "计算NG前三成功" : "计算NG前三失败");
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "计算NG前三失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 计算缺陷率
     */
    @PostMapping("/calculateDefectRates/{id}")
    public Map<String, Object> calculateDefectRates(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean success = aoiDailyReportService.calculateDefectRates(id);
            response.put("code", success ? 200 : 500);
            response.put("msg", success ? "计算缺陷率成功" : "计算缺陷率失败");
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "计算缺陷率失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 计算质量指标
     */
    @PostMapping("/calculateQualityMetrics/{id}")
    public Map<String, Object> calculateQualityMetrics(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean success = aoiDataAnalysisService.calculateQualityMetrics(id);
            response.put("code", success ? 200 : 500);
            response.put("msg", success ? "计算质量指标成功" : "计算质量指标失败");
        } catch (Exception e) {
            response.put("code", 500);
            response.put("msg", "计算质量指标失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 将原始数据转换为结构化数据
     */
    private List<AoiTableDataDto> convertToStructuredData(List<Map<String, Object>> rawData, Map<String, Object> ngLevelStats) {
        List<AoiTableDataDto> result = new ArrayList<>();

        for (Map<String, Object> row : rawData) {
            AoiTableDataDto dto = new AoiTableDataDto();

            // 基础信息
            dto.setId(getLong(row, "id"));
            dto.setReportDate((Date) row.get("reportDate"));
            dto.setMachineSn(getString(row, "machineSn"));
            dto.setProjectName(getString(row, "projectName"));
            dto.setProductionTimeStatus(getString(row, "productionTimeStatus"));

            // 生产统计
            dto.setInputCount(getInteger(row, "inputCount"));
            dto.setRollInput(getInteger(row, "rollInput"));
            dto.setOkCount(getInteger(row, "okCount"));
            dto.setNgCount(getInteger(row, "ngCount"));
            dto.setControlledYield(getString(row, "controlledYield"));
            dto.setRollYield(getString(row, "rollYield"));
            dto.setYieldById(getString(row, "yieldById"));
            dto.setMachineDetectionRate(getString(row, "machineDetectionRate"));
            dto.setAbnormalInfo(getString(row, "abnormalInfo"));

            // 设置累计平均良率
            String avgYield = calculateAvgYield(row, ngLevelStats);
            dto.setAvgYield(avgYield);

            // 动态计算NG前五对象（对应前端的RankTop）
            List<AoiTableDataDto.NgTopDefect> rankTopList = calculateRankTop(row);
            dto.setRankTop1(rankTopList.size() > 0 ? rankTopList.get(0) : createEmptyNgTopDefect());
            dto.setRankTop2(rankTopList.size() > 1 ? rankTopList.get(1) : createEmptyNgTopDefect());
            dto.setRankTop3(rankTopList.size() > 2 ? rankTopList.get(2) : createEmptyNgTopDefect());
            dto.setRankTop4(rankTopList.size() > 3 ? rankTopList.get(3) : createEmptyNgTopDefect());
            dto.setRankTop5(rankTopList.size() > 4 ? rankTopList.get(4) : createEmptyNgTopDefect());

            // 各种缺陷详情对象
            dto.setWebBlemish(new AoiTableDataDto.DefectDetail(getInteger(row, "webBlemishCount"), getString(row, "webBlemishRate")));
            dto.setPillar(new AoiTableDataDto.DefectDetail(getInteger(row, "pillarCount"), getString(row, "pillarRate")));
            dto.setHole(new AoiTableDataDto.DefectDetail(getInteger(row, "holeCount"), getString(row, "holeRate")));
            dto.setDoubleLine(new AoiTableDataDto.DefectDetail(getInteger(row, "doubleLineCount"), getString(row, "doubleLineRate")));
            dto.setKnot(new AoiTableDataDto.DefectDetail(getInteger(row, "knotCount"), getString(row, "knotRate")));
            dto.setOxidation(new AoiTableDataDto.DefectDetail(getInteger(row, "oxidationCount"), getString(row, "oxidationRate")));
            dto.setOilStain(new AoiTableDataDto.DefectDetail(getInteger(row, "oilStainCount"), getString(row, "oilStainRate")));
            dto.setForeignObject(new AoiTableDataDto.DefectDetail(getInteger(row, "foreignObjectCount"), getString(row, "foreignObjectRate")));
            dto.setDeformation(new AoiTableDataDto.DefectDetail(getInteger(row, "deformationCount"), getString(row, "deformationRate")));
            dto.setCrack(new AoiTableDataDto.DefectDetail(getInteger(row, "crackCount"), getString(row, "crackRate")));
            dto.setDiscoloration(new AoiTableDataDto.DefectDetail(getInteger(row, "discolorationCount"), getString(row, "discolorationRate")));
            dto.setHairiness(new AoiTableDataDto.DefectDetail(getInteger(row, "hairinessCount"), getString(row, "hairinessRate")));
            dto.setConnectorlug(new AoiTableDataDto.DefectDetail(getInteger(row, "connectorlugCount"), getString(row, "connectorlugRate")));
            // 新增的三个缺陷类型
            dto.setMesh(new AoiTableDataDto.DefectDetail(getInteger(row, "meshCount"), getString(row, "meshRate")));
            dto.setThickness(new AoiTableDataDto.DefectDetail(getInteger(row, "thicknessCount"), getString(row, "thicknessRate")));
            dto.setMarkdot(new AoiTableDataDto.DefectDetail(getInteger(row, "markdotCount"), getString(row, "markdotRate")));

            // ===== 计算字段 =====

            // 1. 计算缺陷总和
            Integer defectSum = calculateDefectSum(row);
            dto.setDefectSum(defectSum);

            // 2. 设置NG数量分级统计（直接从数据库行中获取）
            dto.setRollNgOver120(getInteger(row, "rollNgOver120"));
            dto.setRollNgBetween50And120(getInteger(row, "rollNgBetween50And120"));

            // 3. 设置分组良率参考值（直接从数据库字段获取）
            dto.setRollYieldWithGroup(getString(row, "rollYieldWithGroup"));

            result.add(dto);
        }

        return result;
    }

    /**
     * 将原始数据转换为版本对比数据
     */
    private List<AoiComparisonDataDto> convertToComparisonData(List<Map<String, Object>> rawData, String projectFilter) {
        List<AoiComparisonDataDto> result = new ArrayList<>();

        // 按项目名称分组数据，模拟VS3和VS4的对比
        Map<String, List<Map<String, Object>>> groupedData = rawData.stream()
            .collect(Collectors.groupingBy(row -> getString(row, "projectName")));

        for (Map.Entry<String, List<Map<String, Object>>> entry : groupedData.entrySet()) {
            String projectName = entry.getKey();
            List<Map<String, Object>> projectData = entry.getValue();

            // 如果指定了项目过滤条件，只处理匹配的项目
            if (projectFilter != null && !projectFilter.isEmpty() && !projectName.contains(projectFilter)) {
                continue;
            }

            // 将项目数据分为两组，模拟VS3和VS4
            List<Map<String, Object>> vs3Data = new ArrayList<>();
            List<Map<String, Object>> vs4Data = new ArrayList<>();

            for (int i = 0; i < projectData.size(); i++) {
                if (i % 2 == 0) {
                    vs3Data.add(projectData.get(i));
                } else {
                    vs4Data.add(projectData.get(i));
                }
            }

            // 计算VS3汇总数据
            ComparisonSummary vs3Summary = calculateComparisonSummary(vs3Data);
            // 计算VS4汇总数据
            ComparisonSummary vs4Summary = calculateComparisonSummary(vs4Data);

            // 构建对比数据
            AoiComparisonDataDto comparisonDto = AoiComparisonDataDto.builder()
                .vs3Data(
                    vs3Summary.totalInput,
                    vs3Summary.totalDefectSum,
                    vs3Summary.totalOk,
                    vs3Summary.totalNg,
                    vs3Summary.totalInput,
                    vs3Summary.avgYield + "%",
                    vs3Summary.avgYield + "%"
                )
                .vs4Data(
                    vs4Summary.totalInput,
                    vs4Summary.totalDefectSum,
                    vs4Summary.totalOk,
                    vs4Summary.totalNg,
                    vs4Summary.totalInput,
                    vs4Summary.avgYield + "%",
                    vs4Summary.avgYield + "%"
                )
                .baseInfo(
                    projectData.get(0) != null ? getLong(projectData.get(0), "id") : null,
                    projectData.get(0) != null ? (Date) projectData.get(0).get("reportDate") : null,
                    projectData.get(0) != null ? getString(projectData.get(0), "machineSn") : "",
                    projectName,
                    "SH" // SH字段，可以根据实际需求调整
                )
                .build();

            result.add(comparisonDto);
        }

        return result;
    }

    /**
     * 动态计算NG前五缺陷排名
     */
    private List<AoiTableDataDto.NgTopDefect> calculateRankTop(Map<String, Object> row) {
        // 获取投入数量用于计算比例
        Integer inputCount = getInteger(row, "inputCount");

        // 创建缺陷列表，包含所有缺陷类型
        List<DefectInfo> defectList = new ArrayList<>();

        // 添加所有缺陷类型，动态计算正确的比例
        defectList.add(new DefectInfo("网疤", getInteger(row, "webBlemishCount"), calculateDefectRate(getInteger(row, "webBlemishCount"), inputCount)));
        defectList.add(new DefectInfo("柱道", getInteger(row, "pillarCount"), calculateDefectRate(getInteger(row, "pillarCount"), inputCount)));
        defectList.add(new DefectInfo("破洞", getInteger(row, "holeCount"), calculateDefectRate(getInteger(row, "holeCount"), inputCount)));
        defectList.add(new DefectInfo("双线", getInteger(row, "doubleLineCount"), calculateDefectRate(getInteger(row, "doubleLineCount"), inputCount)));
        defectList.add(new DefectInfo("打结", getInteger(row, "knotCount"), calculateDefectRate(getInteger(row, "knotCount"), inputCount)));
        defectList.add(new DefectInfo("氧化", getInteger(row, "oxidationCount"), calculateDefectRate(getInteger(row, "oxidationCount"), inputCount)));
        defectList.add(new DefectInfo("油污", getInteger(row, "oilStainCount"), calculateDefectRate(getInteger(row, "oilStainCount"), inputCount)));
        defectList.add(new DefectInfo("异物", getInteger(row, "foreignObjectCount"), calculateDefectRate(getInteger(row, "foreignObjectCount"), inputCount)));
        defectList.add(new DefectInfo("变形", getInteger(row, "deformationCount"), calculateDefectRate(getInteger(row, "deformationCount"), inputCount)));
        defectList.add(new DefectInfo("裂口", getInteger(row, "crackCount"), calculateDefectRate(getInteger(row, "crackCount"), inputCount)));
        defectList.add(new DefectInfo("异色", getInteger(row, "discolorationCount"), calculateDefectRate(getInteger(row, "discolorationCount"), inputCount)));
        defectList.add(new DefectInfo("毛丝", getInteger(row, "hairinessCount"), calculateDefectRate(getInteger(row, "hairinessCount"), inputCount)));
        defectList.add(new DefectInfo("连接片", getInteger(row, "connectorlugCount"), calculateDefectRate(getInteger(row, "connectorlugCount"), inputCount)));
        // 新增的三个缺陷类型
        defectList.add(new DefectInfo("目数", getInteger(row, "meshCount"), calculateDefectRate(getInteger(row, "meshCount"), inputCount)));
        defectList.add(new DefectInfo("厚度", getInteger(row, "thicknessCount"), calculateDefectRate(getInteger(row, "thicknessCount"), inputCount)));
        defectList.add(new DefectInfo("标记点", getInteger(row, "markdotCount"), calculateDefectRate(getInteger(row, "markdotCount"), inputCount)));

        // 按数量降序排序，过滤掉数量为0的缺陷
        List<AoiTableDataDto.NgTopDefect> rankTopList = defectList.stream()
            .filter(defect -> defect.count > 0)
            .sorted((a, b) -> Integer.compare(b.count, a.count))
            .limit(5)
            .map(defect -> new AoiTableDataDto.NgTopDefect(defect.name, defect.count, defect.rate))
            .collect(Collectors.toList());

        return rankTopList;
    }

    /**
     * 创建空的NG缺陷对象
     */
    private AoiTableDataDto.NgTopDefect createEmptyNgTopDefect() {
        return new AoiTableDataDto.NgTopDefect("暂无", 0, "0.00");
    }

    /**
     * 计算对比汇总数据
     */
    private ComparisonSummary calculateComparisonSummary(List<Map<String, Object>> data) {
        if (data.isEmpty()) {
            return new ComparisonSummary(0, 0, 0, 0, "0.00");
        }

        int totalInput = data.stream().mapToInt(row -> getInteger(row, "inputCount")).sum();
        int totalOk = data.stream().mapToInt(row -> getInteger(row, "okCount")).sum();
        int totalNg = data.stream().mapToInt(row -> getInteger(row, "ngCount")).sum();
        int totalDefectSum = data.stream().mapToInt(row -> calculateDefectSum(row)).sum();

        // 计算平均良率
        double avgYield = data.stream()
            .mapToDouble(row -> {
                String yieldStr = getString(row, "controlledYield");
                if (yieldStr != null && !yieldStr.isEmpty()) {
                    try {
                        return Double.parseDouble(yieldStr.replace("%", ""));
                    } catch (NumberFormatException e) {
                        return 0.0;
                    }
                }
                return 0.0;
            })
            .average()
            .orElse(0.0);

        return new ComparisonSummary(totalInput, totalOk, totalNg, totalDefectSum, String.format("%.2f", avgYield));
    }

    /**
     * 对比汇总数据内部类
     */
    private static class ComparisonSummary {
        Integer totalInput;
        Integer totalOk;
        Integer totalNg;
        Integer totalDefectSum;
        String avgYield;

        ComparisonSummary(Integer totalInput, Integer totalOk, Integer totalNg, Integer totalDefectSum, String avgYield) {
            this.totalInput = totalInput;
            this.totalOk = totalOk;
            this.totalNg = totalNg;
            this.totalDefectSum = totalDefectSum;
            this.avgYield = avgYield;
        }
    }

    /**
     * 缺陷信息内部类
     */
    private static class DefectInfo {
        String name;
        Integer count;
        String rate;

        DefectInfo(String name, Integer count, String rate) {
            this.name = name;
            this.count = count != null ? count : 0;
            this.rate = rate != null ? rate : "0.00";
        }
    }

    /**
     * 根据缺陷名称获取对应的数量
     */
    private Integer getDefectCountByName(Map<String, Object> row, String defectName) {
        if (defectName == null || "暂无".equals(defectName)) return 0;

        switch (defectName) {
            case "网疤": return getInteger(row, "webBlemishCount");
            case "柱道": return getInteger(row, "pillarCount");
            case "破洞": return getInteger(row, "holeCount");
            case "双线": return getInteger(row, "doubleLineCount");
            case "打结": return getInteger(row, "knotCount");
            case "氧化": return getInteger(row, "oxidationCount");
            case "油污": return getInteger(row, "oilStainCount");
            case "异物": return getInteger(row, "foreignObjectCount");
            case "变形": return getInteger(row, "deformationCount");
            case "裂口": return getInteger(row, "crackCount");
            case "异色": return getInteger(row, "discolorationCount");
            case "毛丝": return getInteger(row, "hairinessCount");
            case "连接片": return getInteger(row, "connectorlugCount");
            case "目数": return getInteger(row, "meshCount");
            case "厚度": return getInteger(row, "thicknessCount");
            case "标记点": return getInteger(row, "markdotCount");
            default: return 0;
        }
    }

    /**
     * 根据缺陷名称获取对应的比例
     */
    private String getDefectRateByName(Map<String, Object> row, String defectName) {
        if (defectName == null || "暂无".equals(defectName)) return "0.00";

        switch (defectName) {
            case "网疤": return getString(row, "webBlemishRate");
            case "柱道": return getString(row, "pillarRate");
            case "破洞": return getString(row, "holeRate");
            case "双线": return getString(row, "doubleLineRate");
            case "打结": return getString(row, "knotRate");
            case "氧化": return getString(row, "oxidationRate");
            case "油污": return getString(row, "oilStainRate");
            case "异物": return getString(row, "foreignObjectRate");
            case "变形": return getString(row, "deformationRate");
            case "裂口": return getString(row, "crackRate");
            case "异色": return getString(row, "discolorationRate");
            case "毛丝": return getString(row, "hairinessRate");
            case "连接片": return getString(row, "connectorlugRate");
            case "目数": return getString(row, "meshRate");
            case "厚度": return getString(row, "thicknessRate");
            case "标记点": return getString(row, "markdotRate");
            default: return "0.00";
        }
    }

    /**
     * 安全获取字符串值
     */
    private String getString(Map<String, Object> row, String key) {
        Object value = row.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 安全获取整数值
     */
    private Integer getInteger(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) return 0;
        if (value instanceof Integer) return (Integer) value;
        if (value instanceof Number) return ((Number) value).intValue();
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    /**
     * 安全获取长整数值
     */
    private Long getLong(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) return null;
        if (value instanceof Long) return (Long) value;
        if (value instanceof Number) return ((Number) value).longValue();
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 计算缺陷比例
     * @param defectCount 缺陷数量
     * @param inputCount 投入数量
     * @return 缺陷比例字符串 (如"2.50%")
     */
    private String calculateDefectRate(Integer defectCount, Integer inputCount) {
        if (defectCount == null || defectCount == 0 || inputCount == null || inputCount == 0) {
            return "0.00%";
        }

        double rate = (double) defectCount / inputCount * 100;
        return String.format("%.2f%%", rate);
    }

    /**
     * 计算缺陷总和（前5项问题数量总和）
     */
    private Integer calculateDefectSum(Map<String, Object> row) {
        // 创建缺陷列表，包含所有缺陷类型及其数量
        List<DefectInfo> defectList = new ArrayList<>();

        defectList.add(new DefectInfo("网疤", getInteger(row, "webBlemishCount"), ""));
        defectList.add(new DefectInfo("柱道", getInteger(row, "pillarCount"), ""));
        defectList.add(new DefectInfo("破洞", getInteger(row, "holeCount"), ""));
        defectList.add(new DefectInfo("双线", getInteger(row, "doubleLineCount"), ""));
        defectList.add(new DefectInfo("打结", getInteger(row, "knotCount"), ""));
        defectList.add(new DefectInfo("氧化", getInteger(row, "oxidationCount"), ""));
        defectList.add(new DefectInfo("油污", getInteger(row, "oilStainCount"), ""));
        defectList.add(new DefectInfo("异物", getInteger(row, "foreignObjectCount"), ""));
        defectList.add(new DefectInfo("变形", getInteger(row, "deformationCount"), ""));
        defectList.add(new DefectInfo("裂口", getInteger(row, "crackCount"), ""));
        defectList.add(new DefectInfo("异色", getInteger(row, "discolorationCount"), ""));
        defectList.add(new DefectInfo("毛丝", getInteger(row, "hairinessCount"), ""));
        defectList.add(new DefectInfo("连接片", getInteger(row, "connectorlugCount"), ""));
        // 新增的三个缺陷类型
        defectList.add(new DefectInfo("目数", getInteger(row, "meshCount"), ""));
        defectList.add(new DefectInfo("厚度", getInteger(row, "thicknessCount"), ""));
        defectList.add(new DefectInfo("标记点", getInteger(row, "markdotCount"), ""));

        // 按数量降序排序，取前5项的数量总和
        return defectList.stream()
            .filter(defect -> defect.count > 0)
            .sorted((a, b) -> Integer.compare(b.count, a.count))
            .limit(5)
            .mapToInt(defect -> defect.count)
            .sum();
    }

    /**
     * 计算累计平均良率
     */
    private String calculateAvgYield(Map<String, Object> row, Map<String, Object> ngLevelStats) {
        // 优先使用统计查询中的平均良率
        if (ngLevelStats != null && ngLevelStats.get("avgYield") != null) {
            Object avgYield = ngLevelStats.get("avgYield");
            return avgYield.toString() + "%";
        }

        // 如果没有统计数据，尝试从行数据中获取
        String avgYieldFromRow = getString(row, "avgYield");
        if (avgYieldFromRow != null) {
            return avgYieldFromRow.endsWith("%") ? avgYieldFromRow : avgYieldFromRow + "%";
        }

        // 最后备用方案：返回当前良率
        String controlledYield = getString(row, "controlledYield");
        return controlledYield != null ? controlledYield : "0.00%";
    }

    /**
     * 计算分组良率参考值
     */
    private String calculateRollYieldWithGroup(Map<String, Object> row, Map<String, Object> ngLevelStats) {
        // 使用平均良率作为分组参考值
        if (ngLevelStats != null && ngLevelStats.get("avgYield") != null) {
            Object avgYield = ngLevelStats.get("avgYield");
            return avgYield.toString() + "%";
        }

        // 如果没有统计数据，返回当前良率
        String controlledYield = getString(row, "controlledYield");
        return controlledYield != null ? controlledYield : "0.00%";
    }

    /**
     * 从原始数据获取设备列表
     * @param reportDate 报表日期
     * @return 设备列表
     */
    private List<String> getMachineListFromRawData(Date reportDate) {
        try {
            // 使用现有的服务获取设备列表
            return checkDeviceSecretService.getDistinctMachineSnByDate(reportDate);
        } catch (Exception e) {
            log.error("获取设备列表失败: reportDate={}", reportDate, e);
            // 如果服务方法不存在，使用硬编码的设备列表作为临时方案
            return Arrays.asList("D-1", "D-2", "B-2", "C-2");
        }
    }
}

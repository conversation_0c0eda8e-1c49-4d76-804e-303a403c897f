package com.boyo.web.controller.common;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.StrUtil;
import com.boyo.common.core.domain.R;
import com.boyo.common.utils.spring.SpringUtils;
import com.boyo.system.service.ISysConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@SaIgnore
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/common")
public class CommonController {

    @GetMapping("/getManageName")
    public R<String> getManageName(String type){
        String msg = "";
        if(StrUtil.isEmpty(type)){
            msg = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sys.manage.name");
        }else{
            msg = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sys.ningmengdou.name");
        }
        return R.ok(msg,msg);
    }

    @GetMapping("/getManageLogo")
    public R<String> getManageLogo(String type){
        String msg = "";
        if(StrUtil.isEmpty(type)) {
            msg = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sys.manage.logo");
        }else{
            msg = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sys.ningmengdou.logo");
        }
        return R.ok(msg,msg);
    }

    @GetMapping("/getManagePic")
    public R<String> getManagePic(){
        String msg = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sys.manage.pic");
        return R.ok(msg,msg);
    }

    @GetMapping("/getManageColor")
    public R<String> getManageColor(){
        String msg = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sys.manage.color");
        return R.ok(msg,msg);
    }

    @GetMapping("/getExamName")
    public R<String> getExamName(){
        String msg = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sys.exam.name");
        return R.ok(msg,msg);
    }

    @GetMapping("/getExamLogo")
    public R<String> getExamLogo(){
        String msg = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sys.exam.logo");
        return R.ok(msg,msg);
    }

    @GetMapping("/getExamPic")
    public R<String> getExamPic(){
        String msg = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sys.exam.pic");
        return R.ok(msg,msg);
    }
    @GetMapping("/getExamColor")
    public R<String> getExamColor(){
        String msg = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sys.exam.color");
        return R.ok(msg,msg);
    }
}

# AOI数据维护接口文档

## 概述

本文档介绍了用于从 `t_check_device_secret` 表数据更新五个AOI相关表的新接口。这些接口专门用于数据维护和同步，不涉及新增原始数据。

## 涉及的表

这些接口会维护以下五个AOI相关表：
- `t_aoi_daily_report_core` (核心日报表)
- `t_aoi_equipment_status` (设备状态表)
- `t_aoi_operator_info` (操作员信息表)
- `t_aoi_statistics_summary` (统计汇总表)
- `t_aoi_defect_detail` (缺陷详情表)

## 接口列表

### 1. 基于现有原始数据更新AOI相关表

**接口地址**: `POST /aoi/dailyReport/updateAoiTablesFromRawData`

**功能**: 从 `t_check_device_secret` 表的现有数据更新五个AOI相关表

**参数**:
- `reportDate` (必需): 报表日期，格式 yyyy-MM-dd
- `machineSn` (可选): 设备序列号，如果不提供则处理所有设备
- `forceUpdate` (可选): 是否强制更新，默认false

**请求示例**:
```bash
# 更新指定日期所有设备的数据
POST /aoi/dailyReport/updateAoiTablesFromRawData?reportDate=2025-01-15

# 更新指定日期指定设备的数据
POST /aoi/dailyReport/updateAoiTablesFromRawData?reportDate=2025-01-15&machineSn=D-1

# 强制重新聚合所有数据
POST /aoi/dailyReport/updateAoiTablesFromRawData?reportDate=2025-01-15&forceUpdate=true
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "自动聚合成功，共处理 4 条记录",
  "data": {
    "success": true,
    "count": 4,
    "message": "自动聚合成功，共处理 4 条记录"
  }
}
```

### 2. 批量更新指定设备的AOI相关表

**接口地址**: `POST /aoi/dailyReport/batchUpdateAoiTablesForMachines`

**功能**: 支持多台设备同时更新

**参数**:
- `reportDate` (必需): 报表日期，格式 yyyy-MM-dd
- `machineSnList` (必需): 设备序列号列表

**请求示例**:
```bash
POST /aoi/dailyReport/batchUpdateAoiTablesForMachines?reportDate=2025-01-15&machineSnList=D-1,D-2,B-2
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "批量增量聚合完成：成功3台设备，失败0台设备",
  "data": {
    "success": true,
    "successCount": 3,
    "failCount": 0,
    "successMachines": ["D-1", "D-2", "B-2"],
    "failMachines": [],
    "message": "批量增量聚合完成：成功3台设备，失败0台设备"
  }
}
```

### 3. 批量更新指定日期范围的AOI相关表

**接口地址**: `POST /aoi/dailyReport/batchUpdateAoiTablesByDateRange`

**功能**: 支持多天数据批量处理

**参数**:
- `startDate` (必需): 开始日期，格式 yyyy-MM-dd
- `endDate` (必需): 结束日期，格式 yyyy-MM-dd
- `machineSn` (可选): 设备序列号，如果不提供则处理所有设备
- `forceUpdate` (可选): 是否强制更新，默认false

**请求示例**:
```bash
# 更新日期范围内所有设备的数据
POST /aoi/dailyReport/batchUpdateAoiTablesByDateRange?startDate=2025-01-10&endDate=2025-01-15

# 更新日期范围内指定设备的数据
POST /aoi/dailyReport/batchUpdateAoiTablesByDateRange?startDate=2025-01-10&endDate=2025-01-15&machineSn=D-1

# 强制重新聚合日期范围内的数据
POST /aoi/dailyReport/batchUpdateAoiTablesByDateRange?startDate=2025-01-10&endDate=2025-01-15&forceUpdate=true
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "批量更新完成：成功6天，失败0天，共处理24条记录",
  "data": {
    "success": true,
    "totalDays": 6,
    "successDays": 6,
    "failDays": 0,
    "totalProcessedRecords": 24,
    "dailyResults": [
      {
        "success": true,
        "count": 4,
        "date": "2025-01-10T00:00:00.000Z"
      }
    ],
    "message": "批量更新完成：成功6天，失败0天，共处理24条记录"
  }
}
```

## 使用场景

### 1. 日常数据维护
当发现AOI相关表数据不准确时，可以使用第一个接口重新从原始数据生成：
```bash
POST /aoi/dailyReport/updateAoiTablesFromRawData?reportDate=2025-01-15&forceUpdate=true
```

### 2. 特定设备数据修复
当某台设备的数据有问题时，可以单独更新：
```bash
POST /aoi/dailyReport/updateAoiTablesFromRawData?reportDate=2025-01-15&machineSn=D-1
```

### 3. 批量设备数据同步
当多台设备需要同时更新时：
```bash
POST /aoi/dailyReport/batchUpdateAoiTablesForMachines?reportDate=2025-01-15&machineSnList=D-1,D-2,B-2
```

### 4. 历史数据重建
当需要重建一段时间的历史数据时：
```bash
POST /aoi/dailyReport/batchUpdateAoiTablesByDateRange?startDate=2025-01-01&endDate=2025-01-15&forceUpdate=true
```

## 注意事项

1. **数据安全**: 这些接口会覆盖现有的AOI相关表数据，请谨慎使用
2. **性能考虑**: 批量操作可能耗时较长，建议在业务低峰期执行
3. **事务处理**: 所有操作都在事务中执行，确保数据一致性
4. **错误处理**: 接口会详细返回成功和失败的信息，便于问题排查

## 数据处理逻辑

接口会根据 `t_check_device_secret` 表中的数据计算以下字段：

- **input_count**: `SUM(total)` - 投入数量总和
- **production_count**: `COUNT(*)` - 生产数量（记录数）
- **ok_count**: `SUM(test_result='OK')` - 良品数量
- **ng_count**: `SUM(test_result='NG')` - 不良品数量
- **controlled_yield**: `(ok_count/production_count)*100` - 良率
- **machine_detection_rate**: `(ng_count/production_count)*100` - 检出率

同时会更新相关的缺陷详情、设备状态、操作员信息和统计汇总数据。

## 🔧 修复说明

**重要更新**: 已修复表结构兼容性问题！

- ✅ 修改了 `AoiDataAggregationService` 使用新的表结构
- ✅ 更新了所有相关Mapper方法指向正确的表
- ✅ 添加了必要的 `deleteByCoreId` 方法
- ✅ 解决了 "Table 'saihang.t_aoi_daily_report' doesn't exist" 错误

现在接口可以正常使用新的拆分表结构：
- `t_aoi_daily_report_core` (核心日报表)
- `t_aoi_equipment_status` (设备状态表)
- `t_aoi_operator_info` (操作员信息表)
- `t_aoi_statistics_summary` (统计汇总表)
- `t_aoi_defect_detail` (缺陷详情表)

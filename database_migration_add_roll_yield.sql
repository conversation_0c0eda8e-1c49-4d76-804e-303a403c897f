-- 在 t_aoi_daily_report_core 表中添加新字段
-- roll_yield: 用于存储良率计算结果（Roll_OK/Roll_Input*100%）
-- yield_by_id: 用于存储按ID良率计算结果（1-Defect Sum/input数*100%）
-- roll_ng_over_120: NG数量大于120的记录标识（1表示是，0表示否）
-- roll_ng_between_50_and_120: NG数量在50-120之间的记录标识（1表示是，0表示否）

ALTER TABLE t_aoi_daily_report_core
ADD COLUMN roll_yield DECIMAL(5,2) DEFAULT 0.00 COMMENT '良率(Roll_OK/Roll_Input*100%)'
AFTER controlled_yield;

ALTER TABLE t_aoi_daily_report_core
ADD COLUMN yield_by_id DECIMAL(5,2) DEFAULT 0.00 COMMENT '按ID良率(1-Defect Sum/input数*100%)'
AFTER roll_yield;

ALTER TABLE t_aoi_daily_report_core
ADD COLUMN roll_ng_over_120 INT DEFAULT 0 COMMENT 'NG数量大于120的记录标识(1是0否)'
AFTER yield_by_id;

ALTER TABLE t_aoi_daily_report_core
ADD COLUMN roll_ng_between_50_and_120 INT DEFAULT 0 COMMENT 'NG数量在50-120之间的记录标识(1是0否)'
AFTER roll_ng_over_120;

-- 更新现有数据的 roll_yield 字段，使其与 controlled_yield 保持一致
UPDATE t_aoi_daily_report_core
SET roll_yield = controlled_yield
WHERE roll_yield IS NULL OR roll_yield = 0.00;

-- 更新现有数据的 yield_by_id 字段，初始设置为100.00
UPDATE t_aoi_daily_report_core
SET yield_by_id = 100.00
WHERE yield_by_id IS NULL OR yield_by_id = 0.00;

-- 更新现有数据的 NG 分级字段
UPDATE t_aoi_daily_report_core
SET roll_ng_over_120 = CASE WHEN ng_count > 120 THEN 1 ELSE 0 END,
    roll_ng_between_50_and_120 = CASE WHEN ng_count >= 50 AND ng_count <= 120 THEN 1 ELSE 0 END
WHERE roll_ng_over_120 IS NULL OR roll_ng_between_50_and_120 IS NULL;

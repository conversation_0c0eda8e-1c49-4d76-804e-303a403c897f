package com.boyo.utils;

import com.boyo.domain.AoiDailyReport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AOI日报导出工具类
 * 用于生成各种格式的报表数据
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Component
public class AoiReportExportUtil {

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 导出白板格式数据
     * @param reports 日报数据列表
     * @return 白板格式数据
     */
    public List<Map<String, Object>> exportWhiteboardFormat(List<AoiDailyReport> reports) {
        return reports.stream().map(report -> {
            Map<String, Object> data = new LinkedHashMap<>();
            data.put("设备编号", report.getMachineSn());
            data.put("生产时间状态", report.getProductionTimeStatus());
            data.put("打料数量", report.getInputCount());
            data.put("投产数量", report.getProductionCount());
            data.put("OK数量", report.getOkCount());
            data.put("NG数量", report.getNgCount());
            data.put("受控良率", formatPercentage(report.getControlledYield()));
            data.put("NG项前一", report.getNgTop1());
            data.put("NG项前二", report.getNgTop2());
            data.put("NG项前三", report.getNgTop3());
            data.put("机台检率", formatPercentage(report.getMachineDetectionRate()));
            data.put("异常信息", report.getAbnormalInfo());
            return data;
        }).collect(Collectors.toList());
    }

    /**
     * 导出Excel格式数据
     * @param reports 日报数据列表
     * @return Excel格式数据
     */
    public List<Map<String, Object>> exportExcelFormat(List<AoiDailyReport> reports) {
        return reports.stream().map(report -> {
            Map<String, Object> data = new LinkedHashMap<>();
            data.put("日期", DATE_FORMAT.format(report.getReportDate()));
            data.put("设备编号", report.getMachineSn());
            data.put("项目名称", report.getProjectName());
            data.put("总批次数", report.getTotalBatches());
            data.put("平均投入量", report.getAvgInput());
            data.put("平均OK数量", report.getAvgOk());
            data.put("平均NG数量", report.getAvgNg());
            data.put("平均良率", formatPercentage(report.getAvgYield()));
            data.put("总投入量", report.getTotalInput());
            data.put("总OK数量", report.getTotalOk());
            data.put("总NG数量", report.getTotalNg());
            data.put("受控良率", formatPercentage(report.getControlledYield()));
            
            // 缺陷统计
            data.put("网疤数量", report.getWebBlemishCount());
            data.put("柱道数量", report.getPillarCount());
            data.put("破洞数量", report.getHoleCount());
            data.put("双线数量", report.getDoubleLineCount());
            data.put("打结数量", report.getKnotCount());
            data.put("氧化数量", report.getOxidationCount());
            data.put("油污数量", report.getOilStainCount());
            data.put("异物数量", report.getForeignObjectCount());
            data.put("变形数量", report.getDeformationCount());
            data.put("裂口数量", report.getCrackCount());
            data.put("异色数量", report.getDiscolorationCount());
            data.put("毛丝数量", report.getHairinessCount());
            data.put("接线头数量", report.getConnectorlugCount());
            
            // 缺陷率
            data.put("网疤缺陷率", formatPercentage(report.getWebBlemishRate()));
            data.put("柱道缺陷率", formatPercentage(report.getPillarRate()));
            data.put("破洞缺陷率", formatPercentage(report.getHoleRate()));
            data.put("双线缺陷率", formatPercentage(report.getDoubleLineRate()));
            data.put("打结缺陷率", formatPercentage(report.getKnotRate()));
            data.put("氧化缺陷率", formatPercentage(report.getOxidationRate()));
            data.put("油污缺陷率", formatPercentage(report.getOilStainRate()));
            data.put("异物缺陷率", formatPercentage(report.getForeignObjectRate()));
            data.put("变形缺陷率", formatPercentage(report.getDeformationRate()));
            data.put("裂口缺陷率", formatPercentage(report.getCrackRate()));
            data.put("异色缺陷率", formatPercentage(report.getDiscolorationRate()));
            data.put("毛丝缺陷率", formatPercentage(report.getHairinessRate()));
            data.put("接线头缺陷率", formatPercentage(report.getConnectorlugRate()));
            
            return data;
        }).collect(Collectors.toList());
    }

    /**
     * 导出统计汇总数据
     * @param reports 日报数据列表
     * @return 统计汇总数据
     */
    public Map<String, Object> exportSummaryData(List<AoiDailyReport> reports) {
        if (reports.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, Object> summary = new LinkedHashMap<>();
        
        // 基础统计
        summary.put("统计日期范围", getDateRange(reports));
        summary.put("设备数量", getUniqueCount(reports, AoiDailyReport::getMachineSn));
        summary.put("项目数量", getUniqueCount(reports, AoiDailyReport::getProjectName));
        summary.put("总记录数", reports.size());
        
        // 产量统计
        int totalInput = reports.stream().mapToInt(r -> r.getTotalInput() != null ? r.getTotalInput() : 0).sum();
        int totalOk = reports.stream().mapToInt(r -> r.getTotalOk() != null ? r.getTotalOk() : 0).sum();
        int totalNg = reports.stream().mapToInt(r -> r.getTotalNg() != null ? r.getTotalNg() : 0).sum();
        
        summary.put("总投入量", totalInput);
        summary.put("总OK数量", totalOk);
        summary.put("总NG数量", totalNg);
        summary.put("总体良率", totalInput > 0 ? formatPercentage(new BigDecimal(totalOk).divide(new BigDecimal(totalInput), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100))) : "0%");
        
        // 平均值统计
        OptionalDouble avgYield = reports.stream()
            .filter(r -> r.getControlledYield() != null)
            .mapToDouble(r -> r.getControlledYield().doubleValue())
            .average();
        
        summary.put("平均良率", avgYield.isPresent() ? formatPercentage(new BigDecimal(avgYield.getAsDouble())) : "0%");
        summary.put("平均投入量", reports.stream().mapToInt(r -> r.getTotalInput() != null ? r.getTotalInput() : 0).average().orElse(0));
        summary.put("平均OK数量", reports.stream().mapToInt(r -> r.getTotalOk() != null ? r.getTotalOk() : 0).average().orElse(0));
        summary.put("平均NG数量", reports.stream().mapToInt(r -> r.getTotalNg() != null ? r.getTotalNg() : 0).average().orElse(0));
        
        // 缺陷统计
        Map<String, Integer> defectSummary = calculateDefectSummary(reports);
        summary.put("缺陷统计", defectSummary);
        
        // 设备统计
        Map<String, Object> machineStats = calculateMachineStats(reports);
        summary.put("设备统计", machineStats);
        
        return summary;
    }

    /**
     * 导出趋势分析数据
     * @param reports 日报数据列表
     * @return 趋势分析数据
     */
    public Map<String, Object> exportTrendData(List<AoiDailyReport> reports) {
        Map<String, Object> trendData = new LinkedHashMap<>();
        
        // 按日期分组统计
        Map<String, List<AoiDailyReport>> dateGroups = reports.stream()
            .collect(Collectors.groupingBy(r -> DATE_FORMAT.format(r.getReportDate())));
        
        List<Map<String, Object>> dailyTrend = dateGroups.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .map(entry -> {
                String date = entry.getKey();
                List<AoiDailyReport> dayReports = entry.getValue();
                
                Map<String, Object> dayData = new LinkedHashMap<>();
                dayData.put("日期", date);
                dayData.put("设备数量", dayReports.size());
                dayData.put("总投入量", dayReports.stream().mapToInt(r -> r.getTotalInput() != null ? r.getTotalInput() : 0).sum());
                dayData.put("总OK数量", dayReports.stream().mapToInt(r -> r.getTotalOk() != null ? r.getTotalOk() : 0).sum());
                dayData.put("总NG数量", dayReports.stream().mapToInt(r -> r.getTotalNg() != null ? r.getTotalNg() : 0).sum());
                
                OptionalDouble avgYield = dayReports.stream()
                    .filter(r -> r.getControlledYield() != null)
                    .mapToDouble(r -> r.getControlledYield().doubleValue())
                    .average();
                dayData.put("平均良率", avgYield.isPresent() ? avgYield.getAsDouble() : 0);
                
                return dayData;
            })
            .collect(Collectors.toList());
        
        trendData.put("日趋势", dailyTrend);
        
        // 按设备分组统计
        Map<String, List<AoiDailyReport>> machineGroups = reports.stream()
            .collect(Collectors.groupingBy(AoiDailyReport::getMachineSn));
        
        List<Map<String, Object>> machineTrend = machineGroups.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .map(entry -> {
                String machine = entry.getKey();
                List<AoiDailyReport> machineReports = entry.getValue();
                
                Map<String, Object> machineData = new LinkedHashMap<>();
                machineData.put("设备编号", machine);
                machineData.put("记录数量", machineReports.size());
                machineData.put("总投入量", machineReports.stream().mapToInt(r -> r.getTotalInput() != null ? r.getTotalInput() : 0).sum());
                machineData.put("总OK数量", machineReports.stream().mapToInt(r -> r.getTotalOk() != null ? r.getTotalOk() : 0).sum());
                machineData.put("总NG数量", machineReports.stream().mapToInt(r -> r.getTotalNg() != null ? r.getTotalNg() : 0).sum());
                
                OptionalDouble avgYield = machineReports.stream()
                    .filter(r -> r.getControlledYield() != null)
                    .mapToDouble(r -> r.getControlledYield().doubleValue())
                    .average();
                machineData.put("平均良率", avgYield.isPresent() ? avgYield.getAsDouble() : 0);
                
                return machineData;
            })
            .collect(Collectors.toList());
        
        trendData.put("设备趋势", machineTrend);
        
        return trendData;
    }

    /**
     * 格式化百分比
     */
    private String formatPercentage(BigDecimal value) {
        if (value == null) {
            return "0%";
        }
        return value.toString() + "%";
    }

    /**
     * 获取日期范围
     */
    private String getDateRange(List<AoiDailyReport> reports) {
        if (reports.isEmpty()) {
            return "";
        }
        
        Date minDate = reports.stream().map(AoiDailyReport::getReportDate).min(Date::compareTo).orElse(null);
        Date maxDate = reports.stream().map(AoiDailyReport::getReportDate).max(Date::compareTo).orElse(null);
        
        if (minDate != null && maxDate != null) {
            if (minDate.equals(maxDate)) {
                return DATE_FORMAT.format(minDate);
            } else {
                return DATE_FORMAT.format(minDate) + " 至 " + DATE_FORMAT.format(maxDate);
            }
        }
        return "";
    }

    /**
     * 获取唯一值数量
     */
    private <T> long getUniqueCount(List<AoiDailyReport> reports, java.util.function.Function<AoiDailyReport, T> mapper) {
        return reports.stream().map(mapper).filter(Objects::nonNull).distinct().count();
    }

    /**
     * 计算缺陷统计
     */
    private Map<String, Integer> calculateDefectSummary(List<AoiDailyReport> reports) {
        Map<String, Integer> defectSummary = new LinkedHashMap<>();
        
        defectSummary.put("网疤", reports.stream().mapToInt(r -> r.getWebBlemishCount() != null ? r.getWebBlemishCount() : 0).sum());
        defectSummary.put("柱道", reports.stream().mapToInt(r -> r.getPillarCount() != null ? r.getPillarCount() : 0).sum());
        defectSummary.put("破洞", reports.stream().mapToInt(r -> r.getHoleCount() != null ? r.getHoleCount() : 0).sum());
        defectSummary.put("双线", reports.stream().mapToInt(r -> r.getDoubleLineCount() != null ? r.getDoubleLineCount() : 0).sum());
        defectSummary.put("打结", reports.stream().mapToInt(r -> r.getKnotCount() != null ? r.getKnotCount() : 0).sum());
        defectSummary.put("氧化", reports.stream().mapToInt(r -> r.getOxidationCount() != null ? r.getOxidationCount() : 0).sum());
        defectSummary.put("油污", reports.stream().mapToInt(r -> r.getOilStainCount() != null ? r.getOilStainCount() : 0).sum());
        defectSummary.put("异物", reports.stream().mapToInt(r -> r.getForeignObjectCount() != null ? r.getForeignObjectCount() : 0).sum());
        defectSummary.put("变形", reports.stream().mapToInt(r -> r.getDeformationCount() != null ? r.getDeformationCount() : 0).sum());
        defectSummary.put("裂口", reports.stream().mapToInt(r -> r.getCrackCount() != null ? r.getCrackCount() : 0).sum());
        defectSummary.put("异色", reports.stream().mapToInt(r -> r.getDiscolorationCount() != null ? r.getDiscolorationCount() : 0).sum());
        defectSummary.put("毛丝", reports.stream().mapToInt(r -> r.getHairinessCount() != null ? r.getHairinessCount() : 0).sum());
        defectSummary.put("接线头", reports.stream().mapToInt(r -> r.getConnectorlugCount() != null ? r.getConnectorlugCount() : 0).sum());
        
        return defectSummary;
    }

    /**
     * 计算设备统计
     */
    private Map<String, Object> calculateMachineStats(List<AoiDailyReport> reports) {
        Map<String, List<AoiDailyReport>> machineGroups = reports.stream()
            .collect(Collectors.groupingBy(AoiDailyReport::getMachineSn));
        
        Map<String, Object> machineStats = new LinkedHashMap<>();
        
        machineGroups.forEach((machine, machineReports) -> {
            Map<String, Object> stats = new LinkedHashMap<>();
            stats.put("记录数量", machineReports.size());
            stats.put("总投入量", machineReports.stream().mapToInt(r -> r.getTotalInput() != null ? r.getTotalInput() : 0).sum());
            stats.put("总OK数量", machineReports.stream().mapToInt(r -> r.getTotalOk() != null ? r.getTotalOk() : 0).sum());
            stats.put("总NG数量", machineReports.stream().mapToInt(r -> r.getTotalNg() != null ? r.getTotalNg() : 0).sum());
            
            OptionalDouble avgYield = machineReports.stream()
                .filter(r -> r.getControlledYield() != null)
                .mapToDouble(r -> r.getControlledYield().doubleValue())
                .average();
            stats.put("平均良率", avgYield.isPresent() ? avgYield.getAsDouble() : 0);
            
            machineStats.put(machine, stats);
        });
        
        return machineStats;
    }
}

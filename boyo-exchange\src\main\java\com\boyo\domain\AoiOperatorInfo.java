package com.boyo.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * AOI操作员信息表
 * @TableName t_aoi_operator_info
 * <AUTHOR>
 * @date 2024-01-15
 */
@TableName(value = "t_aoi_operator_info")
@Data
public class AoiOperatorInfo implements Serializable {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联核心日报表ID
     */
    @TableField(value = "report_core_id")
    private Long reportCoreId;

    // ===== 操作员信息字段 =====

    /**
     * 操作员姓名
     */
    @TableField(value = "operator_name")
    private String operatorName;

    /**
     * 质检员姓名
     */
    @TableField(value = "quality_inspector")
    private String qualityInspector;

    /**
     * 班长姓名
     */
    @TableField(value = "shift_leader")
    private String shiftLeader;

    // ===== 备注和扩展字段 =====

    /**
     * 备注信息
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 数据来源(AUTO:自动生成,MANUAL:手工录入)
     */
    @TableField(value = "data_source")
    private String dataSource;

    /**
     * 同步状态(PENDING:待同步,SYNCED:已同步,FAILED:同步失败)
     */
    @TableField(value = "sync_status")
    private String syncStatus;

    /**
     * 原始数据条数
     */
    @TableField(value = "raw_data_count")
    private Integer rawDataCount;

    // ===== 系统字段 =====

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 删除标志(0:正常,1:删除)
     */
    @TableField(value = "del_flag")
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

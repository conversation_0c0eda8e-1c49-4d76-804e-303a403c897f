package com.boyo.biz;

import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Date;
import java.util.Map;
import java.util.TreeMap;
import java.util.UUID;

@Slf4j
public class CMGraspApiTest {
    String url = "http://api.cmgrasp.com/CMGraspApi/GateWay";
    String appKey = "DA74911E67C74C36891B0810B6820F7A";
    String signKey = "";
    String userId = "13473189666";
    String dbname = "CMCSYUN847244";
    String sercretKey = "9CDD2C0BF48242DB8E7DDC876C3F7125";
    String apiServerAddress = "";
    String apiParam = "";
    String graspCloudMobile = "";
    String graspCloudServerId = "";

    /**
     * 获取接口所需的SignKey
     */
    @BeforeEach
    void getSignKey() {
        Map<String, String> param = new TreeMap<>();
        param.put("MethodName", "graspcm.cmapi.getsignstr");
        param.put("AppKey", appKey);
        // 这种形式仅仅签名的时候使用
        param.put("InvalidTime", DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        param.put("RandamStr", UUID.randomUUID().toString().replaceAll("-", ""));
        StringBuilder signStr = new StringBuilder();
        for (Map.Entry<String, String> item : param.entrySet()) {
            // 调整顺序就会签名验证失败
            if (item.getKey().equals("MethodName")) {
                continue;
            }
            signStr.append(item.getKey()).append(item.getValue());
        }
        // SercretKey
        // signStr.append("9CDD2C0BF48242DB8E7DDC876C3F7125");
        signStr.append(sercretKey);
        log.info("自定义md5签名原始字符串：{}", signStr.toString());
        param.put("SignStr", DigestUtil.md5Hex(signStr.toString()));
        param.put("InvalidTime", DateUtil.formatDateTime(new Date()));
        try (HttpResponse execute = HttpRequest.post(url).body(JSON.toJSONString(param)).execute()) {
            if (!execute.isOk()) {
                log.error("请求失败");
            }
            String body = execute.body();
            JSONObject result = JSONObject.parseObject(body);
            // {"RetMsg":"{\"InvalidTime\":\"2025-05-14 10:49:27\",\"RandamStr\":\"Jp81oL0XQjMPhJmw\",\"SignKey\":\"2020cmgmsignkeyappkey@#986\",\"SignStr\":\"537e0a2ffd8790c8024f3d945d10f6b2\"}","RetCode":0,"OtherMsg":""}
            log.info("接口返回结果：{}", result);
            if (!result.getInteger("RetCode").equals(0)) {
                log.error("接口返回异常");
                log.error("RetCode：{}", result.getInteger("RetCode"));
                log.error("RetMsg：{}", result.getInteger("RetMsg"));
                log.error("OtherMsg：{}", result.getInteger("OtherMsg"));
                return;
            }
            JSONObject jsonObject = JSONObject.parseObject(result.getString("RetMsg"));
            signKey = jsonObject.getString("SignKey");
            // String rawStr = "InvalidTime" + jsonObject.getString("InvalidTime")
            //     + "RandamStr" + jsonObject.getString("RandamStr")
            //     + "SignKey" + jsonObject.getString("SignKey");
            // String signStrResult = jsonObject.getString("SignStr");
            // log.info("自定义md5签名：{}", DigestUtil.md5Hex(rawStr));
            // log.info("接口返回的签名：{}", signStrResult);
            // log.info("获取到的签名key为：{}", jsonObject.getString("SignKey"));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @BeforeEach
    void getCustomerApiUrl() {
        log.info("SignKey:{}", signKey);
        Map<String, String> param = new TreeMap<>();
        param.put("MethodName", "graspcm.cmapi.getcustomerapiurl");
        param.put("AppKey", appKey);
        param.put("UserId", userId);
        // 这种形式仅仅签名的时候使用
        param.put("InvalidTime", DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        param.put("RandamStr", UUID.randomUUID().toString());
        StringBuilder signStr = new StringBuilder();
        for (Map.Entry<String, String> item : param.entrySet()) {
            // 调整顺序就会签名验证失败
            if (item.getKey().equals("MethodName")) {
                continue;
            }
            signStr.append(item.getKey()).append(item.getValue());
        }
        signStr.append(sercretKey);
        // log.info("自定义md5签名原始字符串：{}", signStr.toString());
        param.put("SignStr", DigestUtil.md5Hex(signStr.toString()));
        param.put("InvalidTime", DateUtil.formatDateTime(new Date()));
        param.put("DbName", dbname);
        try (HttpResponse execute = HttpRequest.post(url).body(JSON.toJSONString(param)).execute()) {
            log.info("接口返回结果：{}", execute.body());
            if (!execute.isOk()) {
                log.info("请求失败");
                return;
            }
            String body = execute.body();
            JSONObject result = JSONObject.parseObject(body);
            JSONObject jsonObject = JSONObject.parseObject(result.getString("RetMsg"));
            apiServerAddress = jsonObject.getString("ApiServerAddress");
            apiParam = jsonObject.getString("ApiParam");
            graspCloudMobile = jsonObject.getString("GraspCloudMobile");
            graspCloudServerId = jsonObject.getString("GraspCloudServerId");
        }
    }

    @Test
    void getQuery() {
        Map<String, Object> param = new TreeMap<>();
        param.put("managename", "GraspCMServerApi.dll");
        param.put("dbname", dbname);
        param.put("paramkey", "DlyBuyData");
        param.put("paramjson", "{\"PageSize\": 100,\"PageIndex\": 1,\"VchType\": 45,\"BeginDate\": \"2024-01-01\",\"EndDate\": \"2025-12-31\"}");
        param.put("apiparam", apiParam);
        param.put("apitype", "query");
        param.put("mobile", graspCloudMobile);
        param.put("serviceid", graspCloudServerId);
        param.put("interiorapi", "1");
        StringBuilder signStr = new StringBuilder();
        for (Map.Entry<String, Object> item : param.entrySet()) {
            // 调整顺序就会签名验证失败
            if (item.getKey().equals("MethodName")) {
                continue;
            }
            signStr.append(item.getKey()).append(item.getValue());
        }
        signStr.append(signKey);
        // log.info("自定义md5签名原始字符串：{}", signStr.toString());
        param.put("sign", DigestUtil.md5Hex(signStr.toString()));
        // String jsonString = JSON.toJSONString(param);
        // log.info("接口参数：{}", jsonString);
        try (HttpResponse execute = HttpRequest.post(apiServerAddress).form(param).execute()) {
            if (!execute.isOk()) {
                log.error("请求失败");
            }
            log.info("接口返回结果：{}", execute.body());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

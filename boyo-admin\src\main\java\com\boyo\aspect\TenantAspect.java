package com.boyo.aspect;


import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.boyo.common.annotation.TenantChange;
import com.boyo.common.storage.TenantStorage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

@Slf4j
@RequiredArgsConstructor
@Aspect
@Component
public class TenantAspect {
//    @Before("@annotation(tenantChange)")
    @Before(value = "execution(* com.boyo.order.controller..*(..)) || execution(* com.boyo.web.controller.system..*(..)) || execution(* com.boyo.wms.controller..*(..)) || execution(* com.boyo.controller..*(..))")
    public void doBefore() throws Throwable {
        String Tenant = ((ServletRequestAttributes) Objects
            .requireNonNull(RequestContextHolder.getRequestAttributes()))
            .getRequest()
            .getHeader("Tenant");
        if(StrUtil.isNotEmpty(Tenant)){
            TenantStorage.setTenantId(Convert.toLong(Tenant));
        }
    }
}

package com.boyo.process.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 *
 * @TableName t_raw_middle
 */
@TableName(value ="t_raw_middle")
@Data
public class RawMiddle implements Serializable {
    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 批次号
     */
    @TableField(value = "batch_number")
    private String batchNumber;

    /**
     * 箱号
     */
    @TableField(value = "box")
    private String box;

    /**
     * 检验员
     */
    @TableField(value = "check_user")
    private String checkUser;

    /**
     * 状态，0待质检，1质检中，2已质检
     */
    @TableField(value = "state")
    private Integer state;

    /**
     * 采购订单编码
     */
    @TableField(value = "purchase_order_num")
    private String purchaseOrderNum;

    /**
     * 入库时间
     */
    @TableField(value = "storage_time")
    private Date storageTime;

    /**
     * 采购时间
     */
    @TableField(value = "purchase_time")
    private Date purchaseTime;

    /**
     * 质检日期
     */
    @TableField(value = "check_time")
    private Date checkTime;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

package com.boyo.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.boyo.domain.AoiDailyReport;
import com.boyo.domain.AoiDailyReportCore;
import com.boyo.domain.AoiDefectDetail;
import com.boyo.domain.AoiEquipmentStatus;
import com.boyo.domain.AoiOperatorInfo;
import com.boyo.domain.AoiStatisticsSummary;
import com.boyo.mapper.AoiDailyReportMapper;
import com.boyo.mapper.AoiDailyReportCoreMapper;
import com.boyo.mapper.AoiDefectDetailMapper;
import com.boyo.mapper.AoiEquipmentStatusMapper;
import com.boyo.mapper.AoiOperatorInfoMapper;
import com.boyo.mapper.AoiStatisticsSummaryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

/**
 * AOI数据自动聚合服务
 * 负责从原始数据自动生成日报表（支持新拆分表结构）
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Service
public class AoiDataAggregationService {

    @Autowired
    private AoiDailyReportMapper aoiDailyReportMapper;

    @Autowired
    private IAoiDailyReportService aoiDailyReportService;

    @Autowired
    private AoiDataAnalysisService aoiDataAnalysisService;

    // 新拆分表的Mapper
    @Autowired
    private AoiDailyReportCoreMapper aoiDailyReportCoreMapper;

    @Autowired
    private AoiDefectDetailMapper aoiDefectDetailMapper;

    @Autowired
    private AoiEquipmentStatusMapper aoiEquipmentStatusMapper;

    @Autowired
    private AoiOperatorInfoMapper aoiOperatorInfoMapper;

    @Autowired
    private AoiStatisticsSummaryMapper aoiStatisticsSummaryMapper;

    /**
     * 自动聚合指定日期的数据
     * @param reportDate 报表日期
     * @return 聚合结果
     */
    @Transactional
    public Map<String, Object> autoAggregateData(Date reportDate) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始自动聚合AOI数据: reportDate={}", reportDate);

            // 1. 从原始数据生成核心日报（使用新表结构）
            int generatedCount = aoiDailyReportCoreMapper.batchGenerateFromRawData(reportDate);
            
            if (generatedCount == 0) {
                result.put("success", false);
                result.put("message", "未找到原始数据或数据已存在");
                return result;
            }

            log.info("成功生成{}条日报记录", generatedCount);

            // 2. 获取生成的核心日报记录
            List<AoiDailyReportCore> coreReports = aoiDailyReportCoreMapper.selectByDate(reportDate);
            
            // 3. 对每条记录进行智能分析
            int analysisSuccessCount = 0;
            int analysisFailCount = 0;
            List<String> analysisErrors = new ArrayList<>();

            for (AoiDailyReportCore coreReport : coreReports) {
                try {
                    Long coreId = coreReport.getId();
                    String machineSn = coreReport.getMachineSn();

                    // 生成关联表数据
                    aoiDefectDetailMapper.generateFromRawData(coreId, reportDate, machineSn);
                    aoiEquipmentStatusMapper.generateFromRawData(coreId, reportDate, machineSn);
                    aoiOperatorInfoMapper.generateFromRawData(coreId, reportDate, machineSn);
                    aoiStatisticsSummaryMapper.generateFromRawData(coreId, reportDate, machineSn);

                    // 计算NG前三和缺陷率
                    calculateNgTop3AndDefectRates(coreId);

                    analysisSuccessCount++;
                    log.debug("关联数据生成成功: machineSn={}", machineSn);

                } catch (Exception e) {
                    analysisFailCount++;
                    analysisErrors.add(coreReport.getMachineSn() + ": " + e.getMessage());
                    log.error("关联数据生成异常: machineSn={}", coreReport.getMachineSn(), e);
                }
            }

            // 4. 生成汇总统计（基于核心表）
            Map<String, Object> summary = generateSummaryFromCore(coreReports);

            result.put("success", true);
            result.put("generatedCount", generatedCount);
            result.put("analysisSuccessCount", analysisSuccessCount);
            result.put("analysisFailCount", analysisFailCount);
            result.put("analysisErrors", analysisErrors);
            result.put("summary", summary);
            result.put("message", String.format("自动聚合完成: 生成%d条记录，分析成功%d条，失败%d条", 
                    generatedCount, analysisSuccessCount, analysisFailCount));

            log.info("自动聚合完成: reportDate={}, 结果={}", reportDate, result.get("message"));

        } catch (Exception e) {
            log.error("自动聚合失败: reportDate={}", reportDate, e);
            result.put("success", false);
            result.put("message", "自动聚合失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 增量聚合数据
     * 只处理新增或更新的原始数据
     * @param reportDate 报表日期
     * @return 聚合结果
     */
    @Transactional
    public Map<String, Object> incrementalAggregateData(Date reportDate) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始增量聚合AOI数据: reportDate={}", reportDate);

            // 1. 检查已存在的日报记录
            List<AoiDailyReport> existingReports = aoiDailyReportService.getByDate(reportDate);
            Set<String> existingMachines = new HashSet<>();
            for (AoiDailyReport report : existingReports) {
                existingMachines.add(report.getMachineSn());
            }

            // 2. 获取所有设备列表
            List<String> allMachines = aoiDailyReportService.getMachineList();
            
            // 3. 找出需要处理的设备
            List<String> machinesToProcess = new ArrayList<>();
            for (String machine : allMachines) {
                if (!existingMachines.contains(machine)) {
                    machinesToProcess.add(machine);
                }
            }

            if (machinesToProcess.isEmpty()) {
                result.put("success", true);
                result.put("message", "所有设备数据已存在，无需增量处理");
                return result;
            }

            // 4. 逐个设备处理
            int processedCount = 0;
            List<String> processErrors = new ArrayList<>();

            for (String machineSn : machinesToProcess) {
                try {
                    boolean success = aoiDailyReportService.generateFromRawData(reportDate, machineSn);
                    if (success) {
                        processedCount++;
                        log.debug("增量处理成功: machineSn={}", machineSn);
                    } else {
                        processErrors.add(machineSn + ": 生成失败");
                        log.warn("增量处理失败: machineSn={}", machineSn);
                    }
                } catch (Exception e) {
                    processErrors.add(machineSn + ": " + e.getMessage());
                    log.error("增量处理异常: machineSn={}", machineSn, e);
                }
            }

            result.put("success", processErrors.isEmpty());
            result.put("processedCount", processedCount);
            result.put("totalMachines", machinesToProcess.size());
            result.put("processErrors", processErrors);
            result.put("message", String.format("增量聚合完成: 处理%d/%d台设备", 
                    processedCount, machinesToProcess.size()));

            log.info("增量聚合完成: reportDate={}, 结果={}", reportDate, result.get("message"));

        } catch (Exception e) {
            log.error("增量聚合失败: reportDate={}", reportDate, e);
            result.put("success", false);
            result.put("message", "增量聚合失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 定时自动聚合任务
     * 处理当天和昨天的数据
     * @return 处理结果
     */
    @Transactional
    public Map<String, Object> scheduledAutoAggregation() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            LocalDate today = LocalDate.now();
            LocalDate yesterday = today.minusDays(1);
            
            Date todayDate = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date yesterdayDate = Date.from(yesterday.atStartOfDay(ZoneId.systemDefault()).toInstant());

            log.info("开始定时自动聚合: today={}, yesterday={}", today, yesterday);

            // 1. 处理昨天的数据（完整聚合）
            Map<String, Object> yesterdayResult = autoAggregateData(yesterdayDate);
            
            // 2. 处理今天的数据（增量聚合）
            Map<String, Object> todayResult = incrementalAggregateData(todayDate);

            result.put("success", true);
            result.put("yesterdayResult", yesterdayResult);
            result.put("todayResult", todayResult);
            result.put("message", "定时自动聚合完成");

            log.info("定时自动聚合完成: {}", result.get("message"));

        } catch (Exception e) {
            log.error("定时自动聚合失败", e);
            result.put("success", false);
            result.put("message", "定时自动聚合失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 生成汇总统计
     * @param reports 日报列表
     * @return 汇总统计
     */
    private Map<String, Object> generateSummary(List<AoiDailyReport> reports) {
        Map<String, Object> summary = new HashMap<>();
        
        if (reports.isEmpty()) {
            return summary;
        }

        // 基础统计
        int totalMachines = reports.size();
        int totalInput = reports.stream().mapToInt(r -> r.getTotalInput() != null ? r.getTotalInput() : 0).sum();
        int totalOk = reports.stream().mapToInt(r -> r.getTotalOk() != null ? r.getTotalOk() : 0).sum();
        int totalNg = reports.stream().mapToInt(r -> r.getTotalNg() != null ? r.getTotalNg() : 0).sum();
        
        double overallYield = totalInput > 0 ? (double) totalOk / totalInput * 100 : 0;

        summary.put("totalMachines", totalMachines);
        summary.put("totalInput", totalInput);
        summary.put("totalOk", totalOk);
        summary.put("totalNg", totalNg);
        summary.put("overallYield", Math.round(overallYield * 100.0) / 100.0);

        // 良率分布统计
        Map<String, Integer> yieldDistribution = new HashMap<>();
        yieldDistribution.put("excellent", 0); // >= 98%
        yieldDistribution.put("good", 0);      // >= 95%
        yieldDistribution.put("normal", 0);    // >= 90%
        yieldDistribution.put("poor", 0);      // < 90%

        for (AoiDailyReport report : reports) {
            if (report.getControlledYield() != null) {
                double yield = report.getControlledYield().doubleValue();
                if (yield >= 98) {
                    yieldDistribution.put("excellent", yieldDistribution.get("excellent") + 1);
                } else if (yield >= 95) {
                    yieldDistribution.put("good", yieldDistribution.get("good") + 1);
                } else if (yield >= 90) {
                    yieldDistribution.put("normal", yieldDistribution.get("normal") + 1);
                } else {
                    yieldDistribution.put("poor", yieldDistribution.get("poor") + 1);
                }
            }
        }

        summary.put("yieldDistribution", yieldDistribution);

        // 异常设备统计
        List<String> abnormalMachines = new ArrayList<>();
        for (AoiDailyReport report : reports) {
            if (report.getAbnormalInfo() != null && !report.getAbnormalInfo().trim().isEmpty()) {
                abnormalMachines.add(report.getMachineSn());
            }
        }
        summary.put("abnormalMachines", abnormalMachines);
        summary.put("abnormalCount", abnormalMachines.size());

        return summary;
    }

    /**
     * 生成汇总统计（基于核心表）
     * @param coreReports 核心日报列表
     * @return 汇总统计
     */
    private Map<String, Object> generateSummaryFromCore(List<AoiDailyReportCore> coreReports) {
        Map<String, Object> summary = new HashMap<>();

        if (coreReports.isEmpty()) {
            return summary;
        }

        // 基础统计
        int totalMachines = coreReports.size();
        int totalInput = coreReports.stream().mapToInt(r -> r.getInputCount() != null ? r.getInputCount() : 0).sum();
        int totalOk = coreReports.stream().mapToInt(r -> r.getOkCount() != null ? r.getOkCount() : 0).sum();
        int totalNg = coreReports.stream().mapToInt(r -> r.getNgCount() != null ? r.getNgCount() : 0).sum();

        double overallYield = totalInput > 0 ? (double) totalOk / totalInput * 100 : 0;

        summary.put("totalMachines", totalMachines);
        summary.put("totalInput", totalInput);
        summary.put("totalOk", totalOk);
        summary.put("totalNg", totalNg);
        summary.put("overallYield", Math.round(overallYield * 100.0) / 100.0);

        // 良率分布统计
        Map<String, Integer> yieldDistribution = new HashMap<>();
        yieldDistribution.put("excellent", 0); // >= 98%
        yieldDistribution.put("good", 0);      // >= 95%
        yieldDistribution.put("normal", 0);    // >= 90%
        yieldDistribution.put("poor", 0);      // < 90%

        for (AoiDailyReportCore coreReport : coreReports) {
            if (coreReport.getControlledYield() != null) {
                double yield = coreReport.getControlledYield().doubleValue();
                if (yield >= 98) {
                    yieldDistribution.put("excellent", yieldDistribution.get("excellent") + 1);
                } else if (yield >= 95) {
                    yieldDistribution.put("good", yieldDistribution.get("good") + 1);
                } else if (yield >= 90) {
                    yieldDistribution.put("normal", yieldDistribution.get("normal") + 1);
                } else {
                    yieldDistribution.put("poor", yieldDistribution.get("poor") + 1);
                }
            }
        }

        summary.put("yieldDistribution", yieldDistribution);

        // 异常设备统计
        List<String> abnormalMachines = new ArrayList<>();
        for (AoiDailyReportCore coreReport : coreReports) {
            if (coreReport.getAbnormalInfo() != null && !coreReport.getAbnormalInfo().trim().isEmpty()) {
                abnormalMachines.add(coreReport.getMachineSn());
            }
        }
        summary.put("abnormalMachines", abnormalMachines);
        summary.put("abnormalCount", abnormalMachines.size());

        return summary;
    }

    /**
     * 重新聚合指定日期的数据
     * 删除现有数据并重新生成
     * @param reportDate 报表日期
     * @return 处理结果
     */
    @Transactional
    public Map<String, Object> reAggregateData(Date reportDate) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始重新聚合AOI数据: reportDate={}", reportDate);

            // 1. 删除现有数据（新表结构）
            List<AoiDailyReportCore> existingCoreReports = aoiDailyReportCoreMapper.selectByDate(reportDate);
            for (AoiDailyReportCore coreReport : existingCoreReports) {
                // 删除关联表数据（通过外键级联删除或手动删除）
                Long coreId = coreReport.getId();
                aoiDefectDetailMapper.deleteByCoreId(coreId);
                aoiEquipmentStatusMapper.deleteByCoreId(coreId);
                aoiOperatorInfoMapper.deleteByCoreId(coreId);
                aoiStatisticsSummaryMapper.deleteByCoreId(coreId);

                // 删除核心表记录
                aoiDailyReportCoreMapper.deleteById(coreId);
            }
            log.info("删除了{}条现有核心记录及其关联数据", existingCoreReports.size());

            // 2. 重新聚合
            Map<String, Object> aggregateResult = autoAggregateData(reportDate);

            result.put("success", (Boolean) aggregateResult.get("success"));
            result.put("deletedCount", existingCoreReports.size());
            result.put("aggregateResult", aggregateResult);
            result.put("message", "重新聚合完成");

            log.info("重新聚合完成: reportDate={}", reportDate);

        } catch (Exception e) {
            log.error("重新聚合失败: reportDate={}", reportDate, e);
            result.put("success", false);
            result.put("message", "重新聚合失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 单台设备增量聚合（新拆分表结构）
     * 专门用于实时更新单台设备的日报数据
     * @param reportDate 报表日期
     * @param machineSn 设备序列号
     * @return 是否成功
     */
    @Transactional
    public boolean incrementalAggregateForMachine(Date reportDate, String machineSn) {
        try {
            log.debug("开始单台设备增量聚合（新表结构）: reportDate={}, machineSn={}", reportDate, machineSn);

            // 1. 检查是否已存在该设备的核心日报记录（可能有多个项目）
            List<AoiDailyReportCore> existingCores = aoiDailyReportCoreMapper.selectAllByDateAndMachine(reportDate, machineSn);

            if (!existingCores.isEmpty()) {
                // 2. 如果存在，删除现有记录并重新生成
                for (AoiDailyReportCore core : existingCores) {
                    // 删除关联表数据
                    deleteRelatedData(core.getId());
                    // 删除核心记录
                    aoiDailyReportCoreMapper.deleteById(core.getId());
                }
                log.debug("已删除设备{}的{}条现有记录", machineSn, existingCores.size());
            }

            // 3. 重新生成该设备的完整日报记录（按项目分组）
            return generateNewReportForMachine(reportDate, machineSn);

        } catch (Exception e) {
            log.error("单台设备增量聚合失败: reportDate={}, machineSn={}", reportDate, machineSn, e);
            return false;
        }
    }

    /**
     * 删除核心记录的关联数据
     * @param coreId 核心记录ID
     */
    private void deleteRelatedData(Long coreId) {
        try {
            // 删除缺陷详情
            aoiDefectDetailMapper.delete(new LambdaQueryWrapper<AoiDefectDetail>()
                    .eq(AoiDefectDetail::getReportCoreId, coreId));
            // 删除设备状态
            aoiEquipmentStatusMapper.delete(new LambdaQueryWrapper<AoiEquipmentStatus>()
                    .eq(AoiEquipmentStatus::getReportCoreId, coreId));
            // 删除操作员信息
            aoiOperatorInfoMapper.delete(new LambdaQueryWrapper<AoiOperatorInfo>()
                    .eq(AoiOperatorInfo::getReportCoreId, coreId));
            // 删除统计汇总
            aoiStatisticsSummaryMapper.delete(new LambdaQueryWrapper<AoiStatisticsSummary>()
                    .eq(AoiStatisticsSummary::getReportCoreId, coreId));
        } catch (Exception e) {
            log.warn("删除关联数据失败: coreId={}", coreId, e);
        }
    }

    /**
     * 为单台设备生成新的完整日报记录（新表结构）
     * @param reportDate 报表日期
     * @param machineSn 设备序列号
     * @return 是否成功
     */
    @Transactional
    public boolean generateNewReportForMachine(Date reportDate, String machineSn) {
        try {
            log.debug("为设备{}生成新的完整日报记录", machineSn);

            // 1. 生成核心日报数据（现在会按项目分组生成多条记录）
            int coreResult = aoiDailyReportCoreMapper.generateFromRawData(reportDate, machineSn);
            if (coreResult == 0) {
                log.warn("设备{}无原始数据，跳过生成", machineSn);
                return false;
            }

            // 2. 获取生成的所有核心记录（可能有多个项目）
            List<AoiDailyReportCore> coreReports = aoiDailyReportCoreMapper.selectAllByDateAndMachine(reportDate, machineSn);
            if (coreReports.isEmpty()) {
                log.error("设备{}核心日报生成失败", machineSn);
                return false;
            }

            // 3. 为每个项目生成关联表数据
            for (AoiDailyReportCore coreReport : coreReports) {
                Long coreId = coreReport.getId();

                // 生成关联表数据
                aoiDefectDetailMapper.generateFromRawData(coreId, reportDate, machineSn);
                aoiEquipmentStatusMapper.generateFromRawData(coreId, reportDate, machineSn);
                aoiOperatorInfoMapper.generateFromRawData(coreId, reportDate, machineSn);
                aoiStatisticsSummaryMapper.generateFromRawData(coreId, reportDate, machineSn);

                // 计算NG前三和缺陷率
                calculateNgTop3AndDefectRates(coreId);

                log.debug("项目{}的关联数据生成成功", coreReport.getProjectName());
            }

            log.debug("设备{}完整日报记录生成成功", machineSn);
            return true;

        } catch (Exception e) {
            log.error("设备{}完整日报记录生成失败", machineSn, e);
            return false;
        }
    }

    /**
     * 重新生成设备日报数据（新表结构）
     * @param reportDate 报表日期
     * @param machineSn 设备序列号
     * @param coreId 核心表ID
     * @return 是否成功
     */
    @Transactional
    public boolean regenerateReportForMachine(Date reportDate, String machineSn, Long coreId) {
        try {
            log.debug("重新生成设备{}日报数据", machineSn);

            // 1. 重新生成核心数据
            int coreResult = aoiDailyReportCoreMapper.generateFromRawData(reportDate, machineSn);
            if (coreResult == 0) {
                log.warn("设备{}无原始数据，跳过更新", machineSn);
                return false;
            }

            // 2. 重新生成关联表数据（先删除再生成）
            aoiDefectDetailMapper.generateFromRawData(coreId, reportDate, machineSn);
            aoiEquipmentStatusMapper.generateFromRawData(coreId, reportDate, machineSn);
            aoiOperatorInfoMapper.generateFromRawData(coreId, reportDate, machineSn);
            aoiStatisticsSummaryMapper.generateFromRawData(coreId, reportDate, machineSn);

            // 3. 重新计算NG前三和缺陷率
            calculateNgTop3AndDefectRates(coreId);

            log.debug("设备{}日报数据重新生成成功", machineSn);
            return true;

        } catch (Exception e) {
            log.error("设备{}日报数据重新生成失败", machineSn, e);
            return false;
        }
    }

    /**
     * 计算NG前三和缺陷率
     * @param coreId 核心表ID
     */
    private void calculateNgTop3AndDefectRates(Long coreId) {
        try {
            // 1. 计算NG前三
            List<Map<String, Object>> ngTop3 = aoiDefectDetailMapper.calculateNgTop3(coreId);

            String ngTop1 = null, ngTop2 = null, ngTop3Str = null;

            if (ngTop3.size() >= 1) {
                ngTop1 = (String) ngTop3.get(0).get("defectType");
            }
            if (ngTop3.size() >= 2) {
                ngTop2 = (String) ngTop3.get(1).get("defectType");
            }
            if (ngTop3.size() >= 3) {
                ngTop3Str = (String) ngTop3.get(2).get("defectType");
            }

            // 更新核心表的NG前三
            aoiDailyReportCoreMapper.updateNgTop3(coreId, ngTop1, ngTop2, ngTop3Str);
            log.info("成功更新NG前三: coreId={}, ngTop1={}, ngTop2={}, ngTop3={}",
                    coreId, ngTop1, ngTop2, ngTop3Str);

            // 2. 计算缺陷率
            aoiDefectDetailMapper.calculateAndUpdateDefectRates(coreId);
            log.info("成功计算缺陷率: coreId={}", coreId);

            // 3. 计算按ID良率
            aoiDailyReportCoreMapper.updateYieldById(coreId);
            log.info("成功计算按ID良率: coreId={}", coreId);

        } catch (Exception e) {
            log.error("计算NG前三、缺陷率和按ID良率失败: coreId={}", coreId, e);
        }
    }

    /**
     * 批量单台设备增量聚合
     * @param reportDate 报表日期
     * @param machineSnList 设备序列号列表
     * @return 聚合结果
     */
    @Transactional
    public Map<String, Object> batchIncrementalAggregateForMachines(Date reportDate, List<String> machineSnList) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始批量单台设备增量聚合: reportDate={}, machines={}", reportDate, machineSnList);

            int successCount = 0;
            int failCount = 0;
            List<String> successMachines = new ArrayList<>();
            List<String> failMachines = new ArrayList<>();

            for (String machineSn : machineSnList) {
                try {
                    boolean success = incrementalAggregateForMachine(reportDate, machineSn);
                    if (success) {
                        successCount++;
                        successMachines.add(machineSn);
                    } else {
                        failCount++;
                        failMachines.add(machineSn);
                    }
                } catch (Exception e) {
                    failCount++;
                    failMachines.add(machineSn);
                    log.error("设备{}增量聚合异常", machineSn, e);
                }
            }

            result.put("success", failCount == 0);
            result.put("totalCount", machineSnList.size());
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("successMachines", successMachines);
            result.put("failMachines", failMachines);
            result.put("message", String.format("批量增量聚合完成: 成功%d台，失败%d台", successCount, failCount));

            log.info("批量单台设备增量聚合完成: reportDate={}, 成功={}, 失败={}",
                    reportDate, successCount, failCount);

        } catch (Exception e) {
            log.error("批量单台设备增量聚合失败: reportDate={}", reportDate, e);
            result.put("success", false);
            result.put("message", "批量增量聚合失败: " + e.getMessage());
        }

        return result;
    }

    // ===== 新表结构专用方法 =====

    /**
     * 自动聚合数据（新表结构）
     * 专门针对拆分后的五个表进行数据聚合
     * @param reportDate 报表日期
     * @return 聚合结果
     */
    @Transactional
    public Map<String, Object> autoAggregateDataForNewStructure(Date reportDate) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始自动聚合AOI数据（新表结构）: reportDate={}", reportDate);

            // 1. 获取所有需要处理的设备
            List<String> machineList = getMachineListFromRawData(reportDate);

            if (machineList.isEmpty()) {
                result.put("success", false);
                result.put("message", "未找到原始数据");
                result.put("count", 0);
                return result;
            }

            log.info("找到{}台设备需要处理", machineList.size());

            // 2. 对每台设备进行聚合
            int successCount = 0;
            int failCount = 0;
            List<String> successMachines = new ArrayList<>();
            List<String> failMachines = new ArrayList<>();

            for (String machineSn : machineList) {
                try {
                    boolean success = incrementalAggregateForMachine(reportDate, machineSn);
                    if (success) {
                        successCount++;
                        successMachines.add(machineSn);
                        log.debug("设备{}聚合成功", machineSn);
                    } else {
                        failCount++;
                        failMachines.add(machineSn);
                        log.warn("设备{}聚合失败", machineSn);
                    }
                } catch (Exception e) {
                    failCount++;
                    failMachines.add(machineSn);
                    log.error("设备{}聚合异常", machineSn, e);
                }
            }

            // 3. 构建结果
            boolean overallSuccess = failCount == 0;
            String message = String.format("自动聚合完成，成功%d台设备，失败%d台设备", successCount, failCount);

            result.put("success", overallSuccess);
            result.put("count", successCount);
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("successMachines", successMachines);
            result.put("failMachines", failMachines);
            result.put("message", message);

            log.info("自动聚合完成（新表结构）: {}", message);

        } catch (Exception e) {
            log.error("自动聚合AOI数据失败（新表结构）: reportDate={}", reportDate, e);
            result.put("success", false);
            result.put("count", 0);
            result.put("message", "自动聚合失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 重新聚合数据（新表结构）
     * 强制重新处理所有数据
     * @param reportDate 报表日期
     * @return 聚合结果
     */
    @Transactional
    public Map<String, Object> reAggregateDataForNewStructure(Date reportDate) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始重新聚合AOI数据（新表结构）: reportDate={}", reportDate);

            // 1. 清理当天的现有数据
            cleanExistingDataForDate(reportDate);

            // 2. 重新聚合所有数据
            result = autoAggregateDataForNewStructure(reportDate);

            if ((Boolean) result.get("success")) {
                result.put("message", "重新聚合成功，" + result.get("message"));
            }

            log.info("重新聚合完成（新表结构）: {}", result.get("message"));

        } catch (Exception e) {
            log.error("重新聚合AOI数据失败（新表结构）: reportDate={}", reportDate, e);
            result.put("success", false);
            result.put("count", 0);
            result.put("message", "重新聚合失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 从原始数据获取设备列表
     * @param reportDate 报表日期
     * @return 设备列表
     */
    private List<String> getMachineListFromRawData(Date reportDate) {
        try {
            // 使用原始SQL查询获取设备列表
            String sql = "SELECT DISTINCT machine_sn FROM t_check_device_secret " +
                        "WHERE DATE(create_time) = ? AND machine_sn IS NOT NULL AND machine_sn != ''";

            // 这里需要通过数据库查询获取，暂时返回空列表
            // 实际实现中需要注入相应的Mapper或使用JdbcTemplate
            return new ArrayList<>();

        } catch (Exception e) {
            log.error("获取设备列表失败: reportDate={}", reportDate, e);
            return new ArrayList<>();
        }
    }

    /**
     * 清理指定日期的现有数据
     * @param reportDate 报表日期
     */
    private void cleanExistingDataForDate(Date reportDate) {
        try {
            log.info("清理日期{}的现有数据", reportDate);

            // 删除核心表数据
            aoiDailyReportCoreMapper.deleteByDate(reportDate);

            // 删除关联表数据会通过外键级联删除或者单独处理
            // 这里可以根据实际需要添加其他表的清理逻辑

            log.info("清理完成");

        } catch (Exception e) {
            log.error("清理现有数据失败: reportDate={}", reportDate, e);
            throw new RuntimeException("清理现有数据失败", e);
        }
    }

}

package com.boyo.wms.controller;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.boyo.common.core.domain.PageQuery;
import com.boyo.common.core.domain.R;
import com.boyo.wms.domain.MaterialStripping;
import com.boyo.wms.domain.MaterialStrippingDetails;
import com.boyo.wms.service.MaterialStrippingDetailsService;
import com.boyo.wms.service.MaterialStrippingService;
import com.sun.org.apache.xpath.internal.operations.Bool;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 退料
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/wms/materialstripping")
public class MaterialStrippingController {
    private final MaterialStrippingService materialStrippingService;
    private final MaterialStrippingDetailsService materialStrippingDetailsService;

    /**
     * 查询
     *
     * @return
     */
    @GetMapping("/list")
    public R<IPage<MaterialStripping>> list(MaterialStripping materialStripping, PageQuery pageQuery) {
        return R.ok(materialStrippingService.getPage(materialStripping, pageQuery));
    }

    /**
     * 详情
     * @param taskId
     * @return
     */
    @GetMapping("/{taskId}")
    public R getInfo(@PathVariable Long taskId) {
        Map<String, Object> map = new HashMap<>();
        MaterialStripping materialStripping = materialStrippingService.getOne(new LambdaQueryWrapper<MaterialStripping>().eq(MaterialStripping::getTaskId, taskId));
        map.put("MaterialStripping", materialStripping);
        map.put("MaterialStrippingDetails", materialStrippingDetailsService.list(new LambdaQueryWrapper<MaterialStrippingDetails>()
            .eq(MaterialStrippingDetails::getStrippingId, materialStripping.getId())));
        return R.ok(map);
    }

    /**
     * 新增
     *
     * @return
     */
    @PostMapping("/add")
    public R<Boolean> add(@RequestBody JSONObject data) {
        MaterialStripping materialStripping = data.getBean("MaterialStripping", MaterialStripping.class);
        materialStripping.setDetails(data.getBeanList("MaterialStrippingDetails", MaterialStrippingDetails.class));
        // 直接新增上即可
        return R.ok(materialStrippingService.add(materialStripping));
    }

    /**
     * 审核
     *
     * @param materialStripping
     * @return
     */
    @PostMapping("audit")
    public R<Boolean> audit(@RequestBody MaterialStripping materialStripping) {
        // 修改一下状态，然后新增上库存
        return R.ok(materialStrippingService.audit(materialStripping));
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.wms.mapper.MaterialStrippingDetailsMapper">

    <resultMap id="BaseResultMap" type="com.boyo.wms.domain.MaterialStrippingDetails">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="strippingId" column="stripping_id" jdbcType="VARCHAR"/>
            <result property="materialCode" column="material_code" jdbcType="VARCHAR"/>
            <result property="materialId" column="material_id" jdbcType="BIGINT"/>
            <result property="flowBatch" column="flow_batch" jdbcType="VARCHAR"/>
            <result property="warehouseId" column="warehouse_id" jdbcType="BIGINT"/>
            <result property="areaId" column="area_id" jdbcType="BIGINT"/>
            <result property="allocationId" column="allocation_id" jdbcType="BIGINT"/>
            <result property="materialCount" column="material_count" jdbcType="DECIMAL"/>
            <result property="materialProductCode" column="material_product_code" jdbcType="VARCHAR"/>
            <result property="allocationCode" column="allocation_code" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,stripping_id,material_code,
        material_id,flow_batch,warehouse_id,
        area_id,allocation_id,material_count,
        create_by,create_time,update_by,
        update_time,material_product_code,allocation_code
    </sql>
</mapper>

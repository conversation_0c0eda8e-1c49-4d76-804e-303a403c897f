package com.boyo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.domain.AoiDefectDetail;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * AOI缺陷详细统计Mapper接口
 * <AUTHOR>
 * @date 2024-01-15
 */
@Mapper
public interface AoiDefectDetailMapper extends BaseMapper<AoiDefectDetail> {

    /**
     * 根据核心表ID查询缺陷详情
     * @param reportCoreId 核心表ID
     * @return 缺陷详情数据
     */
    @Select("SELECT * FROM t_aoi_defect_detail WHERE report_core_id = #{reportCoreId} AND del_flag = 0")
    AoiDefectDetail selectByReportCoreId(@Param("reportCoreId") Long reportCoreId);

    /**
     * 根据核心表ID列表批量查询缺陷详情
     * @param reportCoreIds 核心表ID列表
     * @return 缺陷详情数据列表
     */
    List<AoiDefectDetail> selectByReportCoreIds(@Param("reportCoreIds") List<Long> reportCoreIds);

    /**
     * 统计缺陷分布
     * @param reportDate 报表日期
     * @param machineSn 设备序列号(可选)
     * @return 缺陷分布数据
     */
    List<Map<String, Object>> selectDefectDistribution(@Param("reportDate") Date reportDate, 
                                                       @Param("machineSn") String machineSn);

    /**
     * 从原始数据生成缺陷详情
     * @param reportCoreId 核心表ID
     * @param reportDate 报表日期
     * @param machineSn 设备序列号
     * @return 影响行数
     */
    int generateFromRawData(@Param("reportCoreId") Long reportCoreId,
                           @Param("reportDate") Date reportDate, 
                           @Param("machineSn") String machineSn);

    /**
     * 计算并更新缺陷率
     * @param reportCoreId 核心表ID
     * @return 影响行数
     */
    int calculateAndUpdateDefectRates(@Param("reportCoreId") Long reportCoreId);

    /**
     * 计算NG前三缺陷
     * @param reportCoreId 核心表ID
     * @return NG前三缺陷数据
     */
    List<Map<String, Object>> calculateNgTop3(@Param("reportCoreId") Long reportCoreId);

    /**
     * 根据核心表ID删除缺陷详情
     * @param reportCoreId 核心表ID
     * @return 影响行数
     */
    @Delete("DELETE FROM t_aoi_defect_detail WHERE report_core_id = #{reportCoreId}")
    int deleteByCoreId(@Param("reportCoreId") Long reportCoreId);
}

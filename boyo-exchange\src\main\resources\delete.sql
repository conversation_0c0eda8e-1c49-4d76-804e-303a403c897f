-- 查询数据库中这五个表的所有外键约束
SELECT
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM
    INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE
    REFERENCED_TABLE_SCHEMA = 'saihang'  -- 替换为你的数据库名
  AND TABLE_NAME IN (
                     't_aoi_defect_detail',
                     't_aoi_equipment_status',
                     't_aoi_operator_info',
                     't_aoi_statistics_summary',
                     't_aoi_daily_report_core'
    )
  AND REFERENCED_TABLE_NAME IS NOT NULL;


SELECT
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM
    INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE
    REFERENCED_TABLE_NAME = 't_aoi_daily_report_core'
  AND REFERENCED_TABLE_NAME IS NOT NULL;
-- 删除4个外键约束

-- 1. 删除 t_aoi_defect_detail 表的外键约束
ALTER TABLE t_aoi_defect_detail DROP FOREIGN KEY fk_defect_report_core;

-- 2. 删除 t_aoi_equipment_status 表的外键约束
ALTER TABLE t_aoi_equipment_status DROP FOREIGN KEY fk_equipment_report_core;

-- 3. 删除 t_aoi_operator_info 表的外键约束
ALTER TABLE t_aoi_operator_info DROP FOREIGN KEY fk_operator_report_core;

-- 4. 删除 t_aoi_statistics_summary 表的外键约束
ALTER TABLE t_aoi_statistics_summary DROP FOREIGN KEY fk_statistics_report_core;


-- 删除 t_aoi_daily_report_core 表的3个外键约束

-- 1. 删除 t_aoi_equipment_status 表的外键约束
ALTER TABLE t_aoi_equipment_status DROP FOREIGN KEY fk_equipment_report_core;

-- 2. 删除 t_aoi_operator_info 表的外键约束
ALTER TABLE t_aoi_operator_info DROP FOREIGN KEY fk_operator_report_core;

-- 3. 删除 t_aoi_statistics_summary 表的外键约束
ALTER TABLE t_aoi_statistics_summary DROP FOREIGN KEY fk_statistics_report_core;

-- 使用TRUNCATE清空表，速度更快且重置自增ID
-- 注意：TRUNCATE不能回滚，请谨慎操作！

-- 先清空关联表（有外键约束的先清空）
TRUNCATE TABLE t_aoi_defect_detail;
TRUNCATE TABLE t_aoi_equipment_status;
TRUNCATE TABLE t_aoi_operator_info;
TRUNCATE TABLE t_aoi_statistics_summary;

-- 最后清空核心表
TRUNCATE TABLE t_aoi_daily_report_core;

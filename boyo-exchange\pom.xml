<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>boyo-flowable-plus</artifactId>
        <groupId>com.boyo</groupId>
        <version>0.8.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <!-- 与外部数据进行交互，例如对接其他系统数据、接受mq信息等 todo 当初搞的时候就错了，应该在更底层的，不应该放在依赖的最外层 -->
    <artifactId>boyo-exchange</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-order</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eclipse.paho</groupId>
            <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
            <version>1.2.5</version>
        </dependency>
    </dependencies>

</project>
